const { parentPort } = require('worker_threads');

class PsuedoTicksWorker {
    constructor() {
        this.recentTick = null;
        this.initUpdateTimer();
    }

    initUpdateTimer() {
        try {
            const startEmitter = (startTime) => {
                parentPort.postMessage({ event: 'psuedoTick', time: startTime });
                this.accurateWait(startTime + 50).then(() => {
                    const stop = this.accurateInterval(() => {
                        const now = Date.now();
                        parentPort.postMessage({ event: 'psuedoTick', time: now });
                        this.recentTick = now;
                    }, 50);
                    return stop;
                });
            };

            let stop = null;

            parentPort.on('message', (message) => {
                if (message.action === 'restart') {
                    if (typeof stop === 'function') stop();
                    message.time = this.toNext50(message.time);
                    stop = startEmitter(message.time);
                }
            });
        } catch (e) {
            console.error(`Error in updateTimer`, e);
        }
    }

    toNext50(time) {
        const now = Date.now();
        while (time <= now) time += 50;
        return time;
    }

    accurateInterval(callback, interval) {
        let cancelled = false;
        let expected = Date.now() + interval;

        const tick = () => {
            if (cancelled) return;

            const drift = Date.now() - expected;
            expected += interval;

            callback();

            setTimeout(tick, Math.max(0, interval - drift));
        };

        setTimeout(tick, interval);
        return () => { cancelled = true; };
    }

    async accurateWait(targetTime) {
        const driftMargin = 5;
        while (true) {
            const now = Date.now();
            const remaining = targetTime - now;

            if (remaining <= 0) return;
            if (remaining > driftMargin) {
                await new Promise(res => setTimeout(res, remaining - driftMargin));
            } else {
                while (Date.now() < targetTime);
                return;
            }
        }
    }
}

new PsuedoTicksWorker();
