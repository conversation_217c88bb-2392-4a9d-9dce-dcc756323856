const { getPackets } = require('./packets.js');
const { config } = require('../config.js');
const { stripItemName, IHATETAXES, normalizeDate, getWindowName, isSkin, sleep, normalNumber, normalTime, getSlotLore, noColorCodes, sendDiscord, formatNumber, nicerFinders, betterOnce } = require('./Utils.js');
const { logmc, debug, removeIgn, error } = require('../logger.js');
let { delay, waittime, skip: skipSettings, clickDelay, bedSpam, delayBetweenClicks, angryCoopPrevention: coop, sendAllFlips: flipsWebhook, useItemImage, delayTime } = config;
let { always: useSkip, minProfit: skipMinProfit, userFinder: skipUser, skins: skipSkins, profitPercentage: skipMinPercent, minPrice: skipMinPrice } = skipSettings;
skipMinProfit = normalNumber(skipMinProfit);
skipMinPercent = normalNumber(skipMinPercent);
skipMinPrice = normalNumber(skipMinPrice);
delayBetweenClicks = delayBetweenClicks || 3;
if (useSkip && delay < 150) delay = 150;


class AutoBuy {

    constructor(bot, WebhookManager, coflSocket, ign, state, relist, bank, psuedoTicks) {
        this.bot = bot;
        this.webhook = WebhookManager;
        this.coflSocket = coflSocket;
        this.ws = coflSocket.getWs();
        this.ign = ign;
        this.state = state;
        this.bank = bank;
        this.relist = relist;
        this.recentProfit = 0;
        this.recentPercent = 0;
        this.recentFinder = 0;
        this.recentPrice = 0;
        this.recentlySkipped = false;
        this.recentName = null;
        this.fromCoflSocket = false;
        this.bedFailed = false;
        this.currentOpen = null;
        this.packets = getPackets(ign);
        this.currentlyTimingBed = false;//failsafe
        this.psuedoTicks = psuedoTicks.getTicks();//hahahhhaha
        this.killPusedoTicks = psuedoTicks.terminateWorker;
        this.setFromCoflSocket = this.setFromCoflSocket.bind(this);
        relist.giveSetCoflSocket(this.setFromCoflSocket)
        /*this.psuedoTicks.on('psuedoTick', (time) => {
            console.log(`Got psuedo tick after ${Date.now() - time}`);
        })*/

        this.flipHandler();
        this.initQueue();
    }

    async flipHandler() {
        const { webhook, bot, ws, state, packets } = this;
        let firstGui;

        bot._client.on('open_window', async (window) => {
            const windowID = window.windowId;
            const nextWindowID = windowID === 100 ? 1 : windowID + 1;
            const windowName = window.windowTitle;
            // Removed debug logging during window operations (time-critical)
            packets.confirmClick(windowID);
            if (windowName === '{"italic":false,"extra":[{"text":"BIN Auction View"}],"text":""}' && state.get() !== 'listing') {
                const slow = this.slowDown(this.recentProfit);
                if(!slow) packets.click(31, windowID, -1);
                const finderCheck = this.recentFinder === "USER" && skipUser;
                const skinCheck = isSkin(this.recentName) && skipSkins;
                const profitCheck = this.recentProfit > skipMinProfit;
                const percentCheck = this.recentPercent > skipMinPercent;
                const priceCheck = this.recentPrice > skipMinPrice;
                let useSkipOnFlip = (profitCheck || skinCheck || finderCheck || percentCheck || priceCheck || useSkip) && this.fromCoflSocket;
                this.fromCoflSocket = false;
                // Removed debug logging during buying flow
                firstGui = Date.now();
                webhook.setBuySpeed(firstGui);
                let item = (await this.itemLoad(31))?.name;
                if (item === 'gold_nugget') {
                    if (useSkipOnFlip) {
                        packets.click(31, windowID, 371);
                        bot.betterClick(31, 0, 0);
                        packets.click(11, nextWindowID, 159);
                        this.recentlySkipped = true;
                        if (useSkip) {
                            logmc(`§6[§bTPM§6] §cUsed skip because you have skip always enabled in config`);
                            return;
                        }
                        let skipReasons = [];
                        if (finderCheck) skipReasons.push('it was a user flip');
                        if (profitCheck) skipReasons.push('it was over skip min profit');
                        if (skinCheck) skipReasons.push('it was a skin');
                        if (percentCheck) skipReasons.push('it was over skip min percentage');
                        if (priceCheck) skipReasons.push('it was over min price');
                        logmc(`§6[§bTPM§6] §8Used skip because ${skipReasons.join(' and ')}. You can change this in your config`);
                        return;
                    } else if (slow) {
                        logmc(`§6[§bTPM§6]§6 Waiting ${slow}ms for ${formatNumber(this.recentProfit)}!`)
                        await sleep(slow);//So this is for cofl delay stuff
                        if (!bot.currentWindow || bot.currentWindow.id !== windowID) {
                            // Removed debug logging during buying flow
                            logmc(`§6[§bTPM§6]§c So we waited right but now there's no GUI open or a different GUI is open :(`);
                            return;
                        }
                        packets.click(31, windowID, 371);
                        bot.betterClick(31, 0, 0);
                    }
                }

                this.recentlySkipped = false;

                switch (item) {
                    case "bed":
                        logmc(`§6[§bTPM§6]§6 Found a bed!`)
                        if (!bedSpam && !this.bedFailed && !this.currentlyTimingBed) this.bedFailed = true;//Sometimes beds aren't timed idk why but this should be a good failsafe
                        this.initBedSpam();
                        break;
                    case null:
                    case undefined:
                    case "potato":
                        logmc(`§6[§bTPM§6]§c Potatoed :(`);
                        bot.betterWindowClose();
                        state.set(null);
                        state.setAction(firstGui);
                        break;
                    case "feather":
                        const secondItem = (await this.itemLoad(31, true))?.name;
                        if (secondItem === 'potato') {
                            logmc(`§6[§bTPM§6]§c Potatoed :(`)
                            bot.betterWindowClose();
                            state.set(null);
                            state.setAction(firstGui);
                            break;
                        } else if (secondItem !== 'gold_block') {
                            // Removed debug logging during buying flow
                            bot.betterWindowClose();
                            state.set(null);
                            state.setAction(firstGui);
                            break;
                        }
                    case "gold_block":
                        if (coop) {
                            await bot.waitForTicks(15);
                            const lore = getSlotLore(bot.currentWindow?.slots?.[13]);
                            // Removed expensive debug logging during buying flow
                            if (!lore) {
                                logmc(`§6[§bTPM§6] §cNot claiming sold auction because I can't find the lore :( so idk if you sold it or your coop.`);
                                // Removed expensive JSON.stringify debug logging
                                if (state.get() !== "getting ready") state.set(null);
                                state.setAction(firstGui);
                                bot.betterWindowClose();
                                break;
                            }
                            const found = lore.find(line => {
                                const result = noColorCodes(line)?.includes(bot.username);
                                // Removed debug logging during lore parsing (happens frequently)
                                return result;
                            });
                            if (found) {
                                bot.betterClick(31);
                            } else {
                                logmc("§6[§bTPM§6] §cItem was sold by coop! Not claiming.");
                                bot.betterWindowClose();
                            }
                        } else {
                            bot.betterClick(31);
                        }
                        if (state.get() !== "getting ready") state.set(null);
                        state.setAction(firstGui);
                        break;
                    case "poisonous_potato":
                        logmc(`§6[§bTPM§6]§c Too poor to buy it :(`);
                        bot.betterWindowClose();
                        state.set(null);
                        state.setAction(firstGui);
                        break;
                    case "stained_glass_pane":
                        if (state.get() === 'delisting') {
                            this.bot.betterClick(33);
                            debug(`clicked delist`);
                        } else if (state.get() == "expired") {//This means that it didn't actually expire but it thinks that it did
                            const slot = bot.currentWindow.slots[13];
                            const lore = getSlotLore(slot);
                            const endsInTime = lore.find(line => line.includes('Ends in:'));
                            const endTime = normalTime(endsInTime);
                            setTimeout(() => {//Remove auctions when they expire
                                state.queueAdd(this.relist.getItemUuid(slot), "expired", 4);
                            }, endTime)
                            bot.betterWindowClose();
                            state.set(null);
                            state.setAction(firstGui);
                            sendDiscord({
                                title: 'Funny story',
                                color: 13320532,
                                fields: [
                                    {
                                        name: '',
                                        value: `Ok so like super funny story. Remember that auction that I just told you expired so like it kinda actually didn't expire ikr and like hypixel rounds numbers so like TPM thought it expired cause yk the round numbers make the time not exact and like it didn't actually expire so like yea sorry about that it'll send a new expire thingy when it actually does unless the rounding thing happens again yk ok have a good day and gl flipping man`,
                                    }
                                ],
                                thumbnail: {
                                    url: bot.head,
                                },
                                footer: {
                                    text: `TPM Rewrite`,
                                    icon_url: 'https://media.discordapp.net/attachments/1303439738283495546/1304912521609871413/3c8b469c8faa328a9118bddddc6164a3.png?ex=67311dfd&is=672fcc7d&hm=8a14479f3801591c5a26dce82dd081bd3a0e5c8f90ed7e43d9140006ff0cb6ab&=&format=webp&quality=lossless&width=888&height=888',
                                }
                            }, useItemImage ? bot.head : null, false, bot.username);
                        } else {
                            bot.betterWindowClose();
                            state.set(null);
                            state.setAction(firstGui);
                        }
                        break;
                    case "gold_nugget":
                        if (state.get() === "expired") {
                            state.setAction();
                            state.set(null);
                            this.relist.declineSoldAuction();
                        }
                        break;
                    default:
                        error(`Weird item ${item} found. Idk man`);
                        error(JSON.stringify(bot.currentWindow.slots[31]));
                        bot.betterWindowClose();
                        state.set(null);
                        state.setAction(firstGui);
                        break;
                }

            } else if (windowName === '{"italic":false,"extra":[{"text":"Confirm Purchase"}],"text":""}') {
                if (!this.recentlySkipped) packets.click(11, windowID, -1);
                let confirmAt = Date.now() - firstGui;
                state.setAction(firstGui);
                logmc(`§6[§bTPM§6] §3Confirm at ${confirmAt}ms`);
                await bot.waitForTicks(3);
                while (getWindowName(bot.currentWindow) === 'Confirm Purchase') {//Sometimes click doesn't register
                    bot.betterClick(11, 0, 0);
                    await bot.waitForTicks(5);
                }
                state.set(null);
            } else if (windowName === '{"italic":false,"extra":[{"text":"Auction View"}],"text":""}') {//failsafe if they have normal auctions turned on
                let item = (await this.itemLoad(29))?.name;
                if (item === "gold_nugget" && state.get() == 'expired') {
                    bot.betterClick(29);
                    this.relist.declineSoldAuction();
                    return;
                }
                bot.betterWindowClose();
                if (state.get() !== "getting ready") state.set(null);
                logmc(`§6[§bTPM§6] §cPlease turn off normal auctions!`);
            }
            await sleep(500);
            webhook.sendInventory();
        })

        ws.on('flip', (data) => {
            let currentTime = Date.now();
            const currentState = state.get();
            const stateCheck = currentState === null;
            const lastUpdated = currentTime - state.getTime() > delay;
            const windowCheck = !bot.currentWindow;
            const ready = windowCheck && lastUpdated && stateCheck;
            let auctionID = data.id;
            if (ready) packets.sendMessage(`/viewauction ${auctionID}`);//Put this earlier so that it doesn't get slowed down (but it's kinda ugly :( )
            const { finder, vol, purchaseAt, target, startingBid, tag, itemName, profitPerc } = data;//I hate this :(
            let weirdItemName = stripItemName(itemName);
            let profit = IHATETAXES(target) - startingBid;
            let ending = new Date(normalizeDate(purchaseAt)).getTime();
            let bed = 'NUGGET';
            if (ready) {
                logmc(`§6[§bTPM§6] §6Opening ${itemName}`);
                this.recentlySkipped = false;
                this.currentOpen = auctionID;
                this.recentProfit = profit;
                this.recentFinder = finder;
                this.recentName = itemName;
                this.recentPercent = profitPerc;
                this.recentPrice = startingBid;
                this.bedFailed = false;
                this.fromCoflSocket = true;
                // Removed debug logging during buying flow
                state.set('buying');
                state.setAction(currentTime);
                if (currentTime < ending) {
                    this.timeBed(ending, auctionID);
                    bed = 'BED';
                }
            } else if (currentState !== 'moving' && currentState !== 'getting ready') {
                let reasons = [];
                if (!windowCheck) reasons.push(`${getWindowName(bot.currentWindow)} is open`);
                if (!stateCheck) reasons.push(`state is ${currentState}`);
                if (!lastUpdated) reasons.push(`last action was too recent`);
                state.queueAdd({ finder, profit, tag, itemName, auctionID, startingBid }, 'buying', 0);
                logmc(`§6[§bTPM§6] §3${itemName}§3 added to pipeline because ${reasons.join(' and ')}!`);
            } else {
                logmc(`§6[§bTPM§6] §cCan't open flips while ${currentState} :(`);
            }
            webhook.objectAdd(weirdItemName, startingBid, target, profit, auctionID, bed, finder, itemName, tag, vol, profitPerc);
            // Removed debug logging during flip processing
            if (flipsWebhook) {
                sendDiscord({
                    title: 'Flip Found',
                    color: 9929727,
                    fields: [
                        {
                            name: '',
                            value: `${nicerFinders(finder)}: [\`${noColorCodes(itemName)}\`](https://sky.coflnet.com/a/${auctionID}) \`${formatNumber(startingBid)}\` ⇨ \`${formatNumber(target)}\` (\`${formatNumber(profit)}\` profit) [${bed}] \`${vol}\` volume`,
                        }
                    ],
                    thumbnail: {
                        url: bot.head,
                    },
                    footer: {
                        text: `TPM Rewrite - Found by ${nicerFinders(finder)}`,
                        icon_url: 'https://media.discordapp.net/attachments/1303439738283495546/1304912521609871413/3c8b469c8faa328a9118bddddc6164a3.png?ex=67311dfd&is=672fcc7d&hm=8a14479f3801591c5a26dce82dd081bd3a0e5c8f90ed7e43d9140006ff0cb6ab&=&format=webp&quality=lossless&width=888&height=888',
                    }
                }, bot.head, false, "TPM", null, true)
            }
        })

    }

    async itemLoad(slot, alreadyLoaded = false) {
        return new Promise((resolve) => {
            const { bot } = this;
            let index = 1;
            let found = false;
            const first = bot.currentWindow?.slots[slot]?.name;
            const interval = alreadyLoaded ? setInterval(() => {
                const check = bot.currentWindow?.slots[slot];
                if (check?.name !== first) {
                    clearInterval(interval);
                    found = true;
                    resolve(check);
                    // Removed debug logging during item loading (time-critical)
                }
                index++
            }, 1) : setInterval(() => {
                const check = bot.currentWindow?.slots[slot];
                if (check) {
                    clearInterval(interval);
                    found = true;
                    resolve(check);
                    // Removed debug logging during item loading (time-critical)
                }
                index++
            }, 1);

            setTimeout(() => {
                if (found) return;
                // Removed debug logging during item loading timeout
                clearInterval(interval);
                resolve(null);
            }, delay * 3);
        });
    }

    //ok so the reason we have a fake price is so that I don't have to recode the whole price thing from queue yk yk
    openExternalFlip(ahid, profit, finder, itemname, tag, price = null, fakePrice = null) {//queue and buy from discord (maybe webpage in future?) ONLY CALL IF READY!!!
        this.packets.sendMessage(`/viewauction ${ahid}`);
        this.recentFinder = finder;
        this.recentProfit = profit;
        this.currentOpen = ahid;
        this.bedFailed = true;
        this.recentName = itemname;
        this.recentlySkipped = false;

        if (price) {//For queue flips, don't include price
            this.webhook.objectAdd(stripItemName(itemname), price, null, null, ahid, 'EXTERNAL', finder, itemname, tag);
            this.recentPercent = profit / price * 100;
            this.recentPrice = price;
        } else {
            this.recentPercent = profit / fakePrice * 100;
            this.recentPrice = fakePrice;
        }
    }

    async timeBed(ending, currentID) {
        this.currentlyTimingBed = true;
        let pings = [];
        let dontTime = false;
        await this.bot.waitForTicks(5);
        for (let i = 0; i < 3; i++) {
            try {
                var currentPing = await this.clickPing();
            } catch (e) {
                dontTime = true;
            }
            if (currentPing) pings.push(currentPing);
            await this.bot.waitForTicks(1);
        }
        if (!dontTime) {

            const ping = Math.floor(pings.reduce((acc, num) => acc + num, 0) / pings.length);

            console.log(`Ping: ${ping}ms`);

            const firstTick = await betterOnce(this.psuedoTicks, 'psuedoTick');
            console.log(`Got psuedo tick! at ${firstTick}`);
            console.log(`Time until end ${ending - firstTick - ping}`);
            const waittingTicks = Math.floor((ending - firstTick - ping) / 50) - 1;
            console.log("Waiting ticks:", waittingTicks);

            debug(`Timing bed ${currentID} ${ending - firstTick - ping}`);

            const check = () => getWindowName(this.bot.currentWindow)?.includes('BIN Auction View') && this.currentOpen === currentID && !bedSpam;
            console.log(`Tick % 50`, firstTick % 50);
            try {
                await this.bot.waitForTicks(waittingTicks);
                let newDifference = ending - Date.now();
                console.log(`Done waiting for almost all ticks!! It's now ${newDifference} before the ending`)
                if (newDifference > 100) {
                    console.log(`Still need to wait more. Waiting another ${Math.floor((newDifference) / 50) - 1} ticks`);
                    await this.waitPsuedoTicks(Math.floor(newDifference / 50) - 1);
                    newDifference = ending - Date.now();
                    console.log(`Did all the waitting! It's now ${newDifference} before the ending`);
                }
                if (!check()) sex
                this.bot.betterClick(31, 0, 0);
                await this.waitPsuedoTicks(1);
                if (!check()) sex
                this.bot.betterClick(31, 0, 0);
                await this.waitPsuedoTicks(1);
                if (!check()) sex
                this.bot.betterClick(31, 0, 0);
                await this.waitPsuedoTicks(1);
                if (!check()) sex
                this.bot.betterClick(31, 0, 0);
            } catch { }
        }

        await sleep(5000);
        const windowName = getWindowName(this.bot.currentWindow)
        if (windowName?.includes('BIN Auction View') && this.currentOpen === currentID && this.state.get() === 'buying') {
            this.bot.closeWindow(this.bot.currentWindow);
            this.state.set(null);
            if (!bedSpam) logmc(`§6[§bTPM§6] §cBed timing failed and we had to abort the auction :( Please lower your waittime if this continues or turn on bedspam`);
        } else if (!windowName && this.currentOpen === currentID && this.state.get() === 'buying') {
            logmc(`§6[§bTPM§6] §cWindow somehow closed after bed (possibly died)`);
            this.state.set(null);
        } else {
            debug(`Timed ${currentID} correctly. CurrentOpen: ${this.currentOpen}. Window Name: ${windowName}`);
        }

        this.currentlyTimingBed = false;

    }

    waitPsuedoTicks(ticks, timeout = 5_000) {
        return new Promise(async (resolve, reject) => {
            let amount = 0;
            try {
                await betterOnce(this.psuedoTicks, "psuedoTick", () => {
                    amount++;
                    console.log(`Got new psuedoTick, now at ${amount}`);
                    if (amount == ticks) return true;//betterOnce is now over yay
                }, timeout)
            } catch (e) {
                console.error(`Error waiting ${ticks} `, e);
                reject(e);
            }
            resolve();
        })
    }

    initBedSpam() {
        let undefinedCount = 0;
        const bedSpam = setInterval(() => {
            const window = this.bot.currentWindow;
            const item = window?.slots[31]?.name;
            if (item == undefined) {
                undefinedCount++
                if (undefinedCount == 5) {
                    clearInterval(bedSpam);
                    debug(`Clearing bed spam because of undefined count`, this.bedFailed, config.bedSpam, this.currentlyTimingBed, getWindowName(window), item);
                }
                return;
            }
            if (item == "gold_nugget") {//idk man sometimes it happens
                this.bot.betterClick(31, 0, 0);
                undefinedCount++
                return;
            } else if (item == "potato") {
                this.bot.betterWindowClose();
                this.state.set(null);
                this.state.setAction();
            }
            if ((!this.bedFailed && !config.bedSpam && this.currentlyTimingBed) || getWindowName(window) !== 'BIN Auction View' || item !== 'bed') {
                clearInterval(bedSpam);
                debug('Clearing bed spam', this.bedFailed, config.bedSpam, this.currentlyTimingBed, getWindowName(window), item);
                return;
            };
            this.bot.betterClick(31, 0, 0);
        }, clickDelay)
    }

    initQueue() {
        // Use low-priority queue processing to avoid interfering with buying operations
        this.queueProcessing = false;
        this.queueTimer = null;

        const processQueue = async () => {
            // Prevent overlapping queue processing
            if (this.queueProcessing) return;
            this.queueProcessing = true;

            try {
                const current = this.state.getHighest();
                if (!current) {
                    this.queueProcessing = false;
                    this.scheduleNextQueueCheck();
                    return;
                }

                const currentTime = Date.now();
                if (!this.bot.currentWindow && currentTime > this.state.getTime() + delay && !this.state.get()) {
                    await this.processQueueItem(current, currentTime);
                }
            } catch (e) {
                error('Queue processing error:', e);
            } finally {
                this.queueProcessing = false;
                this.scheduleNextQueueCheck();
            }
        };

        // Start queue processing with lower priority
        this.scheduleNextQueueCheck = () => {
            if (this.queueTimer) clearTimeout(this.queueTimer);
            // Use setImmediate for lower priority, then setTimeout for regular interval
            setImmediate(() => {
                this.queueTimer = setTimeout(processQueue, delay);
            });
        };

        this.scheduleNextQueueCheck();
    }

    async processQueueItem(current, currentTime) {
        switch (current.state) {
            case "buying": {
                const { finder, profit, itemName, auctionID, price } = current.action;
                this.openExternalFlip(auctionID, profit, finder, itemName, null, price);
                logmc(`§6[§bTPM§6] Opening ${itemName}§6 from queue (${formatNumber(profit)} profit)!`);
                break;
            }
            case "claiming":
                this.bot.chat(current.action);
                break;
            case "listing": {
                const { profit, finder, itemName, tag, auctionID, price, weirdItemName } = current.action;
                const shouldList = this.relist.checkRelist(profit, finder, itemName, tag, auctionID, price, weirdItemName, true);
                if (!shouldList) {
                    if (shouldList == 'remove') this.state.queueRemove();
                    return;
                }
                this.relist.listAuction(auctionID, price, profit, weirdItemName, tag);
                break;
            }
            case "listingNoName": {
                const { auctionID, price, time, inv } = current.action;
                if (!this.relist.externalListCheck()) return;
                const { relist: relistObject } = this.webhook.getObjects();
                try {
                    var { weirdItemName, pricePaid, tag } = relistObject[auctionID]; //ew var
                } catch {
                    var weirdItemName = auctionID; //ew var
                    var pricePaid = 0; //ew var
                }
                let profit = IHATETAXES(price) - pricePaid;
                this.relist.listAuction(auctionID, price, profit, weirdItemName, tag, time || null, true, inv);
                current.state = 'listing';
                break;
            }
            case "delisting": {
                const { auctionID, itemUuid } = current.action;
                const { relist: relistObject } = this.webhook.getObjects();
                try {
                    var { weirdItemName } = relistObject[auctionID]; //ew var
                } catch {
                    var weirdItemName = auctionID; //ew var
                }
                this.relist.delistAuction(itemUuid, auctionID, weirdItemName);
                break;
            }
            case "death": {
                this.state.set('DEAAAATHHH');
                // Removed debug logging
                this.bot.quit();
                // this.coflSocket.closeSocket();
                this.coflSocket.kill();
                this.killPusedoTicks();
                this.stopQueueProcessing();
                removeIgn(this.ign);
                this.bot.removeAllListeners();
                try {
                    await betterOnce(this.bot, "end");//Ok so the await here like lowkey breaks everyything so I remove the old queue before
                } catch (e) {
                    for (let i = 0; i < 10; i++) {
                        this.bot.end();//just spam it!
                    }
                    // Removed debug logging
                }
                logmc(`§6[§bTPM§6] §c${this.ign} is now dead. May he rest in peace.`)
                break;
            }
            case "externalBuying": {
                const { finder, profit, itemName, auctionID, startingBid, tag } = current.action;
                this.openExternalFlip(auctionID, profit, finder, itemName, tag, startingBid);
                current.state = 'buying';
                break;
            }
            case "bids": {
                this.relist.checkBids();
                break;
            }
            case "expired": {
                const itemUuid = current.action;
                this.relist.removeExpiredAuction(itemUuid);
                break;
            }
            case "bank": {
                const { amount, withdraw, personal } = current.action;
                this.bank.coins(withdraw, personal, amount);
                break;
            }
        }
        this.state.setAction(currentTime);
        this.state.set(current.state);
        this.state.queueRemove();
        // Removed debug logging during queue processing
    }

    async clickPing() {
        this.bot.betterClick(31);
        const clickTime = Date.now();
        await betterOnce(this.bot._client, 'chat', msg => {
            if (!this.bot.currentWindow) return;
            return msg.message == `{"italic":false,"extra":[{"color":"red","text":""},{"color":"red","text":"This BIN sale is still in its grace period!"}],"text":""}`
        })
        const ping = Date.now() - clickTime
        logmc(`§6[§bTPM§6] §cResponse time from click to grace: ${Date.now() - clickTime}ms`)
        return ping;
    }

    setFromCoflSocket(newState = false) {
        this.fromCoflSocket = newState;
    }

    stopQueueProcessing() {
        if (this.queueTimer) {
            clearTimeout(this.queueTimer);
            this.queueTimer = null;
        }
        this.queueProcessing = false;
    }

    slowDown(profit) {
        for (let i = 0; i < delayTime.length; i += 3) {
            let lowerBound = normalNumber(delayTime[i]);
            let upperBound = normalNumber(delayTime[i + 1]);
            let speed = normalNumber(delayTime[i + 2]);

            if (profit >= lowerBound && profit < upperBound) {
                return speed;
            }
        }
        return 0;
    }

}

module.exports = AutoBuy;