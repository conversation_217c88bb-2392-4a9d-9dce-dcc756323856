const { Worker } = require('worker_threads');
const EventEmitter = require('events');
const path = require('path');

class PsuedoTicks {

    constructor(bot) {
        this.bot = bot;
        this.ticks = new EventEmitter();
        this.worker = null;

        this.initUpdateTimer();
    }

    initUpdateTimer() {
        const workerPath = path.join(__dirname, '', 'PsuedoTicksWorker.js');
        this.worker = new Worker(workerPath);
        this.worker.on('message', (message) => {
            if (message.event === 'psuedoTick') {
                this.ticks.emit('psuedoTick', message.time);
                // console.log(`Main thread received psuedo tick:`, message.time);
            }
        });

        this.worker.on('error', (e) => {
            console.error(`Worker error`, e);
        })

        this.worker.on('exit', (code) => {
            console.error(`Worker exited with code ${code}`);
        });

        this.bot.on('time', () => {
            // console.log(`Got time at ${Date.now()}`);
            this.worker.postMessage({ action: 'restart', time: Date.now() });
        });
    }

    getTicks() {
        return this.ticks;//Allow other classes to access PsuedoTicks event emitter
    }

    terminateWorker() {//haha terminator this prob never getting used let's be honest guys oh wait when the bot stops 
        if (this.worker) {
            this.worker.terminate();
            this.worker = null;
        }
    }

}

module.exports = PsuedoTicks;