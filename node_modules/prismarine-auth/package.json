{"name": "prismarine-auth", "version": "2.5.1", "description": "Authentication library for Microsoft, Xbox Live, and Minecraft with caching support", "main": "index.js", "scripts": {"mocha": "mocha test/*.test.js --reporter spec --exit", "test": "npm run mocha", "pretest": "npm run lint", "lint": "standard", "fix": "standard --fix"}, "repository": {"type": "git", "url": "git+https://github.com/PrismarineJS/prismarine-auth.git"}, "keywords": ["prismarine", "template"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/PrismarineJS/prismarine-auth/issues"}, "homepage": "https://github.com/PrismarineJS/prismarine-auth#readme", "devDependencies": {"chai": "^4.3.4", "chai-as-promised": "^7.1.1", "mocha": "^10.0.0", "prismarine-auth": "file:.", "standard": "^17.0.0"}, "dependencies": {"@azure/msal-node": "^2.0.2", "@xboxreplay/xboxlive-auth": "^3.3.3", "debug": "^4.3.3", "node-fetch": "^2.6.1", "smart-buffer": "^4.1.0", "uuid-1345": "^1.0.2"}}