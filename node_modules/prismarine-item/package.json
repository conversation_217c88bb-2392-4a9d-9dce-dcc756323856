{"name": "prismarine-item", "version": "1.15.0", "description": "Represent a minecraft item with its associated data", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "mocha --reporter spec --exit", "pretest": "npm run lint", "fix": "standard --fix", "lint": "standard"}, "repository": {"type": "git", "url": "git+https://github.com/PrismarineJS/prismarine-item.git"}, "devDependencies": {"@types/node": "^22.3.0", "expect": "^29.1.2", "mocha": "^10.0.0", "prismarine-item": "file:.", "standard": "^17.0.0"}, "keywords": ["minecraft", "item", "prismarine"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/PrismarineJS/prismarine-item/issues"}, "homepage": "https://github.com/PrismarineJS/prismarine-item#readme", "dependencies": {"prismarine-nbt": "^2.0.0", "prismarine-registry": "^1.4.0"}}