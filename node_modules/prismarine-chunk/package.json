{"name": "prismarine-chunk", "version": "1.36.0", "description": "A class to hold chunk data for prismarine", "main": "index.js", "types": "./types/index.d.ts", "scripts": {"test": "mocha --reporter spec --exit", "fix": "standard --fix", "lint": "standard", "pretest": "npm run lint"}, "repository": {"type": "git", "url": "https://github.com/PrismarineJS/prismarine-chunk.git"}, "keywords": ["minecraft", "voxel", "chunk", "world"], "contributors": ["<PERSON> <<EMAIL>> (http://will.xyz/)", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> (flynnn)", "mhsjlw", "horn<PERSON>", "Karang", "<PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "bugs": {"url": "https://github.com/PrismarineJS/prismarine-chunk/issues"}, "homepage": "https://github.com/PrismarineJS/prismarine-chunk", "devDependencies": {"@types/node": "^22.7.5", "expect": "^29.1.0", "mocha": "^10.0.0", "prismarine-chunk": "file:.", "standard": "^17.0.0-2", "typescript": "^5.0.4"}, "dependencies": {"prismarine-biome": "^1.2.0", "prismarine-block": "^1.14.1", "prismarine-nbt": "^2.2.1", "prismarine-registry": "^1.1.0", "smart-buffer": "^4.1.0", "uint4": "^0.1.2", "vec3": "^0.1.3", "xxhash-wasm": "^0.4.2"}, "engines": {"node": ">=14"}}