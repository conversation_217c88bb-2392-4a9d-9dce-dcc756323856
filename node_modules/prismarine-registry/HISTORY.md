## History

### 1.10.0
* [Update chat_type handler (#42)](https://github.com/PrismarineJS/prismarine-registry/commit/065405410bcb13b00597c18ccc78a3749daafb23) (thanks @SuperGamerTron)
* [Disable bedrock test.](https://github.com/PrismarineJS/prismarine-registry/commit/09e80105f354af3405f18c965370621f785136d5) (thanks @rom1504)

### 1.9.0
* [Create commands.yml](https://github.com/PrismarineJS/prismarine-registry/commit/1252f261d171ac1398bfebbd124cd5b79477d684) (thanks @rom1504)
* [Remove debug logging (#40)](https://github.com/PrismarineJS/prismarine-registry/commit/711af2a979af8f76ce9c4f4c7c23701eaf2cb613) (thanks @extremeheat)
* [Increase timeout for bedrock more.](https://github.com/PrismarineJS/prismarine-registry/commit/ae86b03449a007dc5a1ae2d3354201f0df28c588) (thanks @rom1504)

### 1.8.0

* pc 1.20.5
* throw error on no data
* bedrock

### 1.7.0

* Parse minecraft:raw chat type

### 1.6.0

* chat for 1.19.2

### 1.5.0
* Expose dimensions in pc1.19 codec

### 1.4.0
* Add handling for new pc chat formatting, expose new `chatFormattingById`/`chatFormattingByName` (#16)

### 1.3.0

* update dimension codec functions for pc1.19 (#14)

### 1.2.0

* Bump mcdata

### 1.1.0

* Convert network biome schema to mcData schema

### 1.0.0

* initial implementation
