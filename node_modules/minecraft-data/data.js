module.exports =
{
  'pc': {
    '0.30c': {
      get blocks () { return require("./minecraft-data/data/pc/0.30c/blocks.json") },
      get protocol () { return require("./minecraft-data/data/pc/0.30c/protocol.json") },
      get version () { return require("./minecraft-data/data/pc/0.30c/version.json") },
      proto: __dirname + '/minecraft-data/data/pc/0.30c/proto.yml'
    },
    '1.7': {
      get attributes () { return require("./minecraft-data/data/pc/1.7/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.7/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.7/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.7/biomes.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.7/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.7/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.7/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.8/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.7/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.7/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.7/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.7/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.7/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.7/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.7/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.7/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.7/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.7/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.7/proto.yml'
    },
    '1.8': {
      get attributes () { return require("./minecraft-data/data/pc/1.8/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.8/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.8/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.8/biomes.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.8/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.8/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.8/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.8/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.8/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.8/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.8/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.8/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.8/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.8/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.8/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.8/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.8/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.7/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.8/proto.yml'
    },
    '15w40b': {
      get attributes () { return require("./minecraft-data/data/pc/1.9/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.9/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.9/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.9/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.9/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.9/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.9/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.9/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.9/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.9/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.9/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/15w40b/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.9/windows.json") },
      get version () { return require("./minecraft-data/data/pc/15w40b/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.9/language.json") },
      get particles () { return require("./minecraft-data/data/pc/15w40b/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.7/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/15w40b/proto.yml'
    },
    '1.9': {
      get attributes () { return require("./minecraft-data/data/pc/1.9/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.9/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.9/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.9/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.9/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.9/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.9/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.9/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.9/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.9/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.9/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.9/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.9/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.9/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.9/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.9/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.9/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.7/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.9/proto.yml'
    },
    '1.9.1-pre2': {
      get attributes () { return require("./minecraft-data/data/pc/1.9/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.9/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.9/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.9/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.9/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.9/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.9/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.9/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.9/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.9/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.9/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.9.1-pre2/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.9/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.9.1-pre2/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.9/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.9/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.9/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.7/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.9.1-pre2/proto.yml'
    },
    '1.9.2': {
      get attributes () { return require("./minecraft-data/data/pc/1.9/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.9/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.9/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.9/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.9/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.9/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.9/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.9/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.9/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.9/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.9/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.9.2/protocol.json") },
      get protocolComments () { return require("./minecraft-data/data/pc/1.9.2/protocolComments.json") },
      get windows () { return require("./minecraft-data/data/pc/1.9/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.9.2/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.9/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.9/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.9/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.7/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.9.2/proto.yml'
    },
    '1.9.4': {
      get attributes () { return require("./minecraft-data/data/pc/1.9/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.9/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.9/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.9/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.9/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.9/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.9/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.9/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.9/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.9/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.9/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.9.4/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.9/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.9.4/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.9/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.9/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.9/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.7/mapIcons.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.9.4/sounds.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.9.4/proto.yml'
    },
    '16w20a': {
      get attributes () { return require("./minecraft-data/data/pc/1.9/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.9/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.9/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.9/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.9/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.9/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.9/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.9/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.9/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.9/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.9/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/16w20a/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.9/windows.json") },
      get version () { return require("./minecraft-data/data/pc/16w20a/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.9/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.9/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.10/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.7/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/16w20a/proto.yml'
    },
    '1.10-pre1': {
      get attributes () { return require("./minecraft-data/data/pc/1.9/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.9/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.9/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.9/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.9/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.9/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.9/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.9/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.9/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.9/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.9/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.10-pre1/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.9/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.10-pre1/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.9/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.9/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.10/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.7/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.10-pre1/proto.yml'
    },
    '1.10': {
      get attributes () { return require("./minecraft-data/data/pc/1.10/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.10/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.10/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.10/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.10/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.10/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.10/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.10/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.10/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.10/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.10/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.10/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.10/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.10/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.10/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.10/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.7/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.10/proto.yml'
    },
    '1.10.1': {
      get attributes () { return require("./minecraft-data/data/pc/1.10/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.10/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.10/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.10/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.10/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.10/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.10/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.10/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.10/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.10/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.10/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.10/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.10.1/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.10/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.10/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.10/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.7/mapIcons.json") }
    },
    '1.10.2': {
      get attributes () { return require("./minecraft-data/data/pc/1.10/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.10/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.10/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.10/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.10/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.10/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.10/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.10/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.10/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.10/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.10/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.10/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.10.2/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.10/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.10/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.10/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.7/mapIcons.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.10.2/sounds.json") }
    },
    '16w35a': {
      get attributes () { return require("./minecraft-data/data/pc/1.10/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.10/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.10/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.10/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.10/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.10/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.10/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.10/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.10/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.10/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/16w35a/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.10/windows.json") },
      get version () { return require("./minecraft-data/data/pc/16w35a/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.10/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.10/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.10/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.7/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/16w35a/proto.yml'
    },
    '1.11': {
      get attributes () { return require("./minecraft-data/data/pc/1.11/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.11/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.11/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.11/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.11/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.11/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.11/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.11/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.11/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.11/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.11/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.11/protocol.json") },
      get protocolComments () { return require("./minecraft-data/data/pc/1.11.2/protocolComments.json") },
      get windows () { return require("./minecraft-data/data/pc/1.11/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.11/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.11/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.11/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.11/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.11/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.11/proto.yml'
    },
    '1.11.2': {
      get attributes () { return require("./minecraft-data/data/pc/1.11/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.11/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.11/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.11/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.11/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.11/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.11/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.11/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.11/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.11/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.11/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.11/protocol.json") },
      get protocolComments () { return require("./minecraft-data/data/pc/1.11.2/protocolComments.json") },
      get windows () { return require("./minecraft-data/data/pc/1.11/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.11.2/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.11/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.11/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.11/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.11/mapIcons.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.11.2/sounds.json") }
    },
    '17w15a': {
      get attributes () { return require("./minecraft-data/data/pc/1.11/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.11/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.11/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.11/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.11/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.11/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.11/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.11/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.11/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.11/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.11/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/17w15a/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.11/windows.json") },
      get version () { return require("./minecraft-data/data/pc/17w15a/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.11/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.11/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.11/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.11/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/17w15a/proto.yml'
    },
    '17w18b': {
      get attributes () { return require("./minecraft-data/data/pc/1.11/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.11/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.11/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.11/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.11/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.11/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.11/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.11/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.11/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.11/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.11/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/17w18b/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.11/windows.json") },
      get version () { return require("./minecraft-data/data/pc/17w18b/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.11/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.11/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.11/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.11/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/17w18b/proto.yml'
    },
    '1.12-pre4': {
      get attributes () { return require("./minecraft-data/data/pc/1.11/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.11/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.11/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.11/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.11/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.11/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.11/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.11/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.11/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.11/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.11/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.12-pre4/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.11/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.12-pre4/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.11/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.11/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.11/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.11/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.12-pre4/proto.yml'
    },
    '1.12': {
      get attributes () { return require("./minecraft-data/data/pc/1.12/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.12/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.12/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.12/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.12/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.12/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.12/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.12/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.12/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.12/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.12/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.12/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.12/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.12/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.12/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.12/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.12/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.11/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.12/proto.yml'
    },
    '1.12.1': {
      get attributes () { return require("./minecraft-data/data/pc/1.12/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.12/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.12/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.12/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.12/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.12/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.12/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.12/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.12/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.12/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.12/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.12.1/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.12/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.12.1/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.12/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.12/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.12/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.11/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.12.1/proto.yml'
    },
    '1.12.2': {
      get attributes () { return require("./minecraft-data/data/pc/1.12/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.12/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.12/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.12/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.12/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.12/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.12/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.12/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.12/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.12/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.12/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.12.2/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.12/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.12.2/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.12/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.12/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.12/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.11/mapIcons.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.12.2/sounds.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.12.2/proto.yml'
    },
    '17w50a': {
      get attributes () { return require("./minecraft-data/data/pc/1.12/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.12/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.12/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.12/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.12/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.12/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.12/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.12/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.12/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.12/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.12/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/17w50a/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.12/windows.json") },
      get version () { return require("./minecraft-data/data/pc/17w50a/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.12/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.12/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/17w50a/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.11/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/17w50a/proto.yml'
    },
    '1.13': {
      get attributes () { return require("./minecraft-data/data/pc/1.13/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.13/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.13/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.13/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.13/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.13/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.13/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.13/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.13/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.13/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.13/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.13/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.13/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.13/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.13/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.13.2/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.13/particles.json") },
      get commands () { return require("./minecraft-data/data/pc/1.13/commands.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.13/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.13/proto.yml'
    },
    '1.13.1': {
      get attributes () { return require("./minecraft-data/data/pc/1.13/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.13/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.13/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.13/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.13/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.13/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.13/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.13/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.13/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.13/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.13/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.13.1/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.13/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.13.1/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.13/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.13.2/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.13/particles.json") },
      get commands () { return require("./minecraft-data/data/pc/1.13.1/commands.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.13/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.13.1/proto.yml'
    },
    '1.13.2-pre1': {
      get attributes () { return require("./minecraft-data/data/pc/1.13/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.13/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.13/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.13/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.13/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.13/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.13/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.13/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.13/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.13/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.13/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.13.2-pre1/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.13/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.13.2-pre1/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.13/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.13.2/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.13/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.13/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.13.2-pre1/proto.yml'
    },
    '1.13.2-pre2': {
      get attributes () { return require("./minecraft-data/data/pc/1.13/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.13/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.13/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.13/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.13/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.13/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.13/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.13/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.13/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.13/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.13/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.13.2-pre2/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.13/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.13.2-pre2/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.13/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.13.2/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.13/particles.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.13/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.13.2-pre2/proto.yml'
    },
    '1.13.2': {
      get attributes () { return require("./minecraft-data/data/pc/1.13/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.13.2/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.13.2/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.13.2/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.13.2/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.13.2/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.13.2/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.13.2/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.13.2/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.13.2/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.13.2/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.13.2/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.13.2/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.13.2/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.13.2/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.13.2/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.13/particles.json") },
      get commands () { return require("./minecraft-data/data/pc/1.13.2/commands.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.13/mapIcons.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.13.2/sounds.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.13.2/proto.yml'
    },
    '1.14': {
      get attributes () { return require("./minecraft-data/data/pc/1.14/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.14.4/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.14/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.14/biomes.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.13.2/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.14.4/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.14/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.14/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.14.4/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.14.4/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.14/entities.json") },
      get language () { return require("./minecraft-data/data/pc/1.14/language.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.14/protocol.json") },
      get version () { return require("./minecraft-data/data/pc/1.14/version.json") },
      get windows () { return require("./minecraft-data/data/pc/1.14.4/windows.json") },
      get foods () { return require("./minecraft-data/data/pc/1.14.4/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.14/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.14.4/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.14.4/entityLoot.json") },
      get commands () { return require("./minecraft-data/data/pc/1.14/commands.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.13/mapIcons.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.14/sounds.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.14/proto.yml'
    },
    '1.14.1': {
      get attributes () { return require("./minecraft-data/data/pc/1.14/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.14.4/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.14.4/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.14/biomes.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.13.2/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.14.4/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.14/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.14/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.14.4/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.14.4/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.14/entities.json") },
      get language () { return require("./minecraft-data/data/pc/1.14/language.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.14.1/protocol.json") },
      get version () { return require("./minecraft-data/data/pc/1.14.1/version.json") },
      get windows () { return require("./minecraft-data/data/pc/1.14.4/windows.json") },
      get foods () { return require("./minecraft-data/data/pc/1.14.4/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.14/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.14.4/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.14.4/entityLoot.json") },
      get commands () { return require("./minecraft-data/data/pc/1.14.1/commands.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.13/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.14.1/proto.yml'
    },
    '1.14.3': {
      get attributes () { return require("./minecraft-data/data/pc/1.14/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.14.4/blocks.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.13.2/enchantments.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.14.4/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.14/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.14.4/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.14/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.14/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.14.4/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.14.4/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.14/entities.json") },
      get language () { return require("./minecraft-data/data/pc/1.14/language.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.14.3/protocol.json") },
      get version () { return require("./minecraft-data/data/pc/1.14.3/version.json") },
      get windows () { return require("./minecraft-data/data/pc/1.14.4/windows.json") },
      get foods () { return require("./minecraft-data/data/pc/1.14.4/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.14/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.14.4/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.14.4/entityLoot.json") },
      get commands () { return require("./minecraft-data/data/pc/1.14.3/commands.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.13/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.14.3/proto.yml'
    },
    '1.14.4': {
      get attributes () { return require("./minecraft-data/data/pc/1.14/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.14.4/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.14.4/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.14.4/biomes.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.13.2/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.14.4/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.14.4/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.14.4/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.14.4/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.14.4/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.14.4/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.14.4/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.14.4/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.14.4/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.14.4/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.14.4/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.14/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.14.4/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.14.4/entityLoot.json") },
      get commands () { return require("./minecraft-data/data/pc/1.14.4/commands.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.13/mapIcons.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.14.4/sounds.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.14.4/proto.yml'
    },
    '1.15': {
      get attributes () { return require("./minecraft-data/data/pc/1.15/attributes.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.15/blockCollisionShapes.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.15/protocol.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.13.2/enchantments.json") },
      get version () { return require("./minecraft-data/data/pc/1.15/version.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.15.2/blocks.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.15/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.15.2/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.15.2/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.15.2/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.15.2/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.15.2/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.15.2/entities.json") },
      get windows () { return require("./minecraft-data/data/pc/1.15.2/windows.json") },
      get language () { return require("./minecraft-data/data/pc/1.15.2/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.15.2/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.15/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.15.2/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.15.2/entityLoot.json") },
      get commands () { return require("./minecraft-data/data/pc/1.15/commands.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.13/mapIcons.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.15/sounds.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.15/proto.yml'
    },
    '1.15.1': {
      get attributes () { return require("./minecraft-data/data/pc/1.15/attributes.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.15.1/protocol.json") },
      get version () { return require("./minecraft-data/data/pc/1.15.1/version.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.15.2/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.15.2/blockCollisionShapes.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.13.2/enchantments.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.15/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.15.2/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.15.2/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.15.2/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.15.2/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.15.2/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.15.2/entities.json") },
      get windows () { return require("./minecraft-data/data/pc/1.15.2/windows.json") },
      get language () { return require("./minecraft-data/data/pc/1.15.2/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.15.2/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.15/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.15.2/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.15.2/entityLoot.json") },
      get commands () { return require("./minecraft-data/data/pc/1.15.1/commands.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.13/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.15.1/proto.yml'
    },
    '1.15.2': {
      get attributes () { return require("./minecraft-data/data/pc/1.15/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.15.2/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.15.2/blockCollisionShapes.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.13.2/enchantments.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.15.2/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.15.2/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.15.2/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.15.2/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.15.2/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.15.2/materials.json") },
      get entities () { return require("./minecraft-data/data/pc/1.15.2/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.15.2/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.15.2/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.15.2/version.json") },
      get language () { return require("./minecraft-data/data/pc/1.15.2/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.15.2/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.15/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.15.2/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.15.2/entityLoot.json") },
      get commands () { return require("./minecraft-data/data/pc/1.15.2/commands.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.13/mapIcons.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.15.2/sounds.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.15.2/proto.yml'
    },
    '20w13b': {
      get attributes () { return require("./minecraft-data/data/pc/1.16/attributes.json") },
      get protocol () { return require("./minecraft-data/data/pc/20w13b/protocol.json") },
      get version () { return require("./minecraft-data/data/pc/20w13b/version.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.16.1/blocks.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.13.2/enchantments.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.16/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.16.1/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.16.1/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.16.1/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.16.1/instruments.json") },
      get entities () { return require("./minecraft-data/data/pc/1.16.1/entities.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.16.1/blockCollisionShapes.json") },
      get materials () { return require("./minecraft-data/data/pc/1.16.1/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.16.1/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.16.1/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/20w13b/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.16.1/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.16.1/entityLoot.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.16/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/20w13b/proto.yml'
    },
    '20w14a': {
      get attributes () { return require("./minecraft-data/data/pc/1.16/attributes.json") },
      get protocol () { return require("./minecraft-data/data/pc/20w13b/protocol.json") },
      get version () { return require("./minecraft-data/data/pc/20w14a/version.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.16.1/blocks.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.16/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.16.1/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.13.2/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.16.1/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.16.1/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.16.1/instruments.json") },
      get entities () { return require("./minecraft-data/data/pc/1.16.1/entities.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.16.1/blockCollisionShapes.json") },
      get materials () { return require("./minecraft-data/data/pc/1.16.1/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.16.1/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.16.1/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/20w13b/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.16.1/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.16.1/entityLoot.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.16/mapIcons.json") }
    },
    '1.16-rc1': {
      get attributes () { return require("./minecraft-data/data/pc/1.16/attributes.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.16-rc1/protocol.json") },
      get version () { return require("./minecraft-data/data/pc/1.16-rc1/version.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.16.1/blocks.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.16/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.16.1/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.13.2/enchantments.json") },
      get items () { return require("./minecraft-data/data/pc/1.16.1/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.16.1/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.16.1/instruments.json") },
      get entities () { return require("./minecraft-data/data/pc/1.16.1/entities.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.16.1/blockCollisionShapes.json") },
      get materials () { return require("./minecraft-data/data/pc/1.16.1/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.16.1/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.16.1/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.16/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.16.1/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.16.1/entityLoot.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.16/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.16-rc1/proto.yml'
    },
    '1.16': {
      get attributes () { return require("./minecraft-data/data/pc/1.16/attributes.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.16/protocol.json") },
      get version () { return require("./minecraft-data/data/pc/1.16/version.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.16.1/blocks.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.16.1/biomes.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.13.2/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.16.1/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.16.1/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.16.1/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.16.1/instruments.json") },
      get entities () { return require("./minecraft-data/data/pc/1.16.1/entities.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.16.1/blockCollisionShapes.json") },
      get materials () { return require("./minecraft-data/data/pc/1.16.1/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.16.1/language.json") },
      get foods () { return require("./minecraft-data/data/pc/1.16.1/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.16/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.16.1/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.16.1/entityLoot.json") },
      get commands () { return require("./minecraft-data/data/pc/1.16/commands.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.16/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.16/mapIcons.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.16/sounds.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.16/proto.yml'
    },
    '1.16.1': {
      get attributes () { return require("./minecraft-data/data/pc/1.16/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.16.1/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.16.1/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.16.1/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.16.1/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.16.1/items.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.16.1/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.16.1/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.16.1/materials.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.13.2/enchantments.json") },
      get language () { return require("./minecraft-data/data/pc/1.16.1/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.16.1/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.16.1/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.16.1/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.16.1/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.16/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.16.1/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.16.1/entityLoot.json") },
      get commands () { return require("./minecraft-data/data/pc/1.16.1/commands.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.16/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.16/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.16.1/proto.yml'
    },
    '1.16.2': {
      get attributes () { return require("./minecraft-data/data/pc/1.16/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.16.2/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.16.1/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.16.2/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.16.1/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.16.2/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.13.2/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.16.2/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.16.1/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.16.2/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.16.1/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.16.2/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.16.2/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.16.2/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.16.1/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.16/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.16.2/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.16.2/entityLoot.json") },
      get commands () { return require("./minecraft-data/data/pc/1.16.2/commands.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.16.2/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.16/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.16.2/proto.yml'
    },
    '1.16.3': {
      get attributes () { return require("./minecraft-data/data/pc/1.16/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.16.2/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.16.1/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.16.2/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.16.1/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.16.2/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.13.2/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.16.2/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.16.1/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.16.2/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.16.1/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.16.2/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.16.2/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.16.3/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.16.1/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.16/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.16.2/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.16.2/entityLoot.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.16.2/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.16/mapIcons.json") }
    },
    '1.16.4': {
      get attributes () { return require("./minecraft-data/data/pc/1.16/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.16.2/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.16.1/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.16.2/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.16.1/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.16.2/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.16.4/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.16.2/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.16.1/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.16.2/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.16.1/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.16.2/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.16.2/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.16.4/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.16.1/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.16/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.16.2/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.16.2/entityLoot.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.16.2/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.16/mapIcons.json") }
    },
    '1.16.5': {
      get attributes () { return require("./minecraft-data/data/pc/1.16/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.16.2/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.16.1/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.16.2/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.16.1/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.16.2/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.16.4/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.16.2/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.16.1/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.16.2/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.16.1/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.16.2/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.16.2/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.16.5/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.16.1/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.16/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.16.2/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.16.2/entityLoot.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.16.2/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.16/mapIcons.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.16/sounds.json") }
    },
    '21w07a': {
      get attributes () { return require("./minecraft-data/data/pc/1.16/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.16.2/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.16.1/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.16.2/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.16.1/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.16.2/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.16.4/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.16.2/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.16.1/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.16.2/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.16.1/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.16.2/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/21w07a/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/21w07a/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.16.1/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.16/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.16.2/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.16.2/entityLoot.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.16.2/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.16.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.16/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/21w07a/proto.yml'
    },
    '1.17': {
      get attributes () { return require("./minecraft-data/data/pc/1.17/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.17/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.17/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.17/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.17/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.17/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.17/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.16.1/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.17/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.17/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.17/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.17/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.17/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.17/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.17/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.17/entityLoot.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.17/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.17/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.16/mapIcons.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.17/sounds.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.17/proto.yml'
    },
    '1.17.1': {
      get attributes () { return require("./minecraft-data/data/pc/1.17/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.17/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.17/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.17/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.17/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.17/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.17/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.16.1/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.17/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.17/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.17.1/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.17.1/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.17/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.17/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.17/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.17/entityLoot.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.17/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.17/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.16/mapIcons.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.17/sounds.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.17.1/proto.yml'
    },
    '1.18': {
      get attributes () { return require("./minecraft-data/data/pc/1.17/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.18/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.17/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.18/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.18/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.17/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.18/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.16.1/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.18/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.18/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.18/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.18/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.18/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.17/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.18/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.18/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.18/entityLoot.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.18/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.17/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.16/mapIcons.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.18/sounds.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.18/proto.yml'
    },
    '1.18.1': {
      get attributes () { return require("./minecraft-data/data/pc/1.17/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.18/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.17/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.18/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.18/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.17/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.18/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.16.1/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.18/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.18/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.18/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.18/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.18.1/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.17/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.18/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.18/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.18/entityLoot.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.18/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.17/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.16/mapIcons.json") }
    },
    '1.18.2': {
      get attributes () { return require("./minecraft-data/data/pc/1.17/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.18/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.17/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.18/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.18/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.17/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.18/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.16.1/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.18/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.18/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.18/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.18.2/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.18.2/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.17/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.18/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.18/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.18/entityLoot.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.18.2/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.17/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.16/mapIcons.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.18/sounds.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.18.2/proto.yml'
    },
    '1.19': {
      get attributes () { return require("./minecraft-data/data/pc/1.17/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.19/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.19/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.19/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.19/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.19/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.19/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.19/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.19/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.19/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.19/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.19/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.19/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.19/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.19/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.19/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.19/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.19/entityLoot.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.19/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.19/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.16/mapIcons.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.19/sounds.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.19/proto.yml'
    },
    '1.19.2': {
      get attributes () { return require("./minecraft-data/data/pc/1.17/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.19.2/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.19.2/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.19.2/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.19.2/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.19.2/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.19.2/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.19/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.19.2/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.19.2/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.19.2/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.19.2/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.19.2/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.19.2/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.19.2/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.19.2/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.19/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.19/entityLoot.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.19.2/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.19.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.16/mapIcons.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.19.2/sounds.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.19.2/proto.yml'
    },
    '1.19.3': {
      get attributes () { return require("./minecraft-data/data/pc/1.17/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.19.3/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.19.3/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.19.3/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.19.3/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.19.3/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.19.3/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.19.3/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.19.3/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.19.3/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.19.3/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.19.3/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.19.3/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.19.3/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.19.3/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.19.3/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.19/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.19/entityLoot.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.19.2/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.19.3/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.16/mapIcons.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.19.3/proto.yml'
    },
    '1.19.4': {
      get attributes () { return require("./minecraft-data/data/pc/1.17/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.19.4/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.19.4/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.19.4/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.19.4/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.19.4/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.19.4/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.19.4/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.19.4/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.19.4/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.19.4/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.19.4/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.19.4/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.19.4/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.19.4/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.19.4/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.19/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.19/entityLoot.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.19.2/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.19.4/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.16/mapIcons.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.19.2/sounds.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.19.4/proto.yml'
    },
    '1.20': {
      get attributes () { return require("./minecraft-data/data/pc/1.17/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.20/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.20/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.20/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.20/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.20/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.20/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.20/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.20/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.20/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.20/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.20/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.20/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.20/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.20/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.20/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.20/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.20/entityLoot.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.20/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.20/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.16/mapIcons.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.20.1/sounds.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.20/proto.yml'
    },
    '1.20.1': {
      get attributes () { return require("./minecraft-data/data/pc/1.17/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.20/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.20/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.20/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.20/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.20/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.20/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.20/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.20/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.20/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.20/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.20/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.20/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.20.1/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.20/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.20/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.20/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.20/entityLoot.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.20/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.20/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.16/mapIcons.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.20.1/sounds.json") }
    },
    '1.20.2': {
      get attributes () { return require("./minecraft-data/data/pc/1.17/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.20.2/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.20.2/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.20.2/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.20.2/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.20.2/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.20.2/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.20.2/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.20.2/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.20.2/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.20.2/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.20.2/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.20.2/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.20.2/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.20.2/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.20/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.20/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.20/entityLoot.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.20.2/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.20.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.20.2/mapIcons.json") },
      get commands () { return require("./minecraft-data/data/pc/1.20.2/commands.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.20.2/sounds.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.20.2/proto.yml'
    },
    '1.20.3': {
      get attributes () { return require("./minecraft-data/data/pc/1.17/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.20.3/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.20.3/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.20.2/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.20.2/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.20.3/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.20.2/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.20.3/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.20.2/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.20.3/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.20.3/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.20.3/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.20.3/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.20.3/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.20.3/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.20.3/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.20/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.20/entityLoot.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.20.2/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.20.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.20.2/mapIcons.json") },
      get commands () { return require("./minecraft-data/data/pc/1.20.3/commands.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.20.3/proto.yml'
    },
    '1.20.4': {
      get attributes () { return require("./minecraft-data/data/pc/1.17/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.20.4/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.20.3/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.20.2/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.20.2/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.20.3/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.20.2/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.20.3/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.20.2/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.20.3/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.20.3/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.20.3/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.20.3/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.20.4/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.20.3/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.20.3/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.20/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.20/entityLoot.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.20.2/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.20.2/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.20.2/mapIcons.json") },
      get commands () { return require("./minecraft-data/data/pc/1.20.3/commands.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.20.4/sounds.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.20.3/proto.yml'
    },
    '1.20.5': {
      get attributes () { return require("./minecraft-data/data/pc/1.17/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.20.5/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.20.5/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.20.5/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.20.5/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.20.5/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.20.5/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.20.5/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.20.5/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.20.5/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.20.5/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.20.5/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.20.5/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.20.5/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.20.5/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.20.5/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.20/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.20/entityLoot.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.20.5/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.20.5/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.20.2/mapIcons.json") },
      get commands () { return require("./minecraft-data/data/pc/1.20.3/commands.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.20.4/sounds.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.20.5/proto.yml'
    },
    '1.20.6': {
      get attributes () { return require("./minecraft-data/data/pc/1.17/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.20.5/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.20.5/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.20.5/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.20.5/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.20.5/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.20.5/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.20.5/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.20.5/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.20.5/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.20.5/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.20.5/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.20.5/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.20.6/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.20.5/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.20.5/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.20/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.20/entityLoot.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.20.5/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.20.5/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.20.2/mapIcons.json") },
      get commands () { return require("./minecraft-data/data/pc/1.20.3/commands.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.20.4/sounds.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.20.5/proto.yml'
    },
    '1.21': {
      get attributes () { return require("./minecraft-data/data/pc/1.21.1/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.21.1/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.20.5/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.20.5/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.20.5/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.21.1/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.21.1/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.21.1/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.20.5/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.21.1/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.21.1/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.20.5/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.21.1/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.21/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.21.1/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.20.5/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.20/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.20/entityLoot.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.20.5/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.21.1/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.20.2/mapIcons.json") },
      get commands () { return require("./minecraft-data/data/pc/1.20.3/commands.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.21.1/sounds.json") },
      proto: __dirname + '/minecraft-data/data/pc/latest/proto.yml'
    },
    '1.21.1': {
      get attributes () { return require("./minecraft-data/data/pc/1.21.1/attributes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.21.1/blocks.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.20.5/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.20.5/biomes.json") },
      get effects () { return require("./minecraft-data/data/pc/1.20.5/effects.json") },
      get items () { return require("./minecraft-data/data/pc/1.21.1/items.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.21.1/enchantments.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.21.1/recipes.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.20.5/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.21.1/materials.json") },
      get language () { return require("./minecraft-data/data/pc/1.21.1/language.json") },
      get entities () { return require("./minecraft-data/data/pc/1.20.5/entities.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.21.1/protocol.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      get version () { return require("./minecraft-data/data/pc/1.21.1/version.json") },
      get foods () { return require("./minecraft-data/data/pc/1.21.1/foods.json") },
      get particles () { return require("./minecraft-data/data/pc/1.20.5/particles.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.20/blockLoot.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.20/entityLoot.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.20.5/loginPacket.json") },
      get tints () { return require("./minecraft-data/data/pc/1.21.1/tints.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.20.2/mapIcons.json") },
      get commands () { return require("./minecraft-data/data/pc/1.20.3/commands.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.21.1/sounds.json") },
      proto: __dirname + '/minecraft-data/data/pc/1.21.1/proto.yml'
    },
    '1.21.3': {
      get attributes () { return require("./minecraft-data/data/pc/1.21.3/attributes.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/pc/1.21.3/blockCollisionShapes.json") },
      get blocks () { return require("./minecraft-data/data/pc/1.21.3/blocks.json") },
      get blockLoot () { return require("./minecraft-data/data/pc/1.20/blockLoot.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.21.3/biomes.json") },
      get commands () { return require("./minecraft-data/data/pc/1.20.3/commands.json") },
      get effects () { return require("./minecraft-data/data/pc/1.20.5/effects.json") },
      get enchantments () { return require("./minecraft-data/data/pc/1.21.1/enchantments.json") },
      get entities () { return require("./minecraft-data/data/pc/1.21.3/entities.json") },
      get entityLoot () { return require("./minecraft-data/data/pc/1.20/entityLoot.json") },
      get foods () { return require("./minecraft-data/data/pc/1.21.1/foods.json") },
      get instruments () { return require("./minecraft-data/data/pc/1.20.5/instruments.json") },
      get items () { return require("./minecraft-data/data/pc/1.21.3/items.json") },
      get language () { return require("./minecraft-data/data/pc/1.21.3/language.json") },
      get loginPacket () { return require("./minecraft-data/data/pc/1.20.5/loginPacket.json") },
      get mapIcons () { return require("./minecraft-data/data/pc/1.20.2/mapIcons.json") },
      get materials () { return require("./minecraft-data/data/pc/1.21.3/materials.json") },
      get particles () { return require("./minecraft-data/data/pc/1.21.3/particles.json") },
      get protocol () { return require("./minecraft-data/data/pc/1.21.3/protocol.json") },
      get recipes () { return require("./minecraft-data/data/pc/1.21.3/recipes.json") },
      get sounds () { return require("./minecraft-data/data/pc/1.21.3/sounds.json") },
      get tints () { return require("./minecraft-data/data/pc/1.21.3/tints.json") },
      get version () { return require("./minecraft-data/data/pc/1.21.3/version.json") },
      get windows () { return require("./minecraft-data/data/pc/1.16.1/windows.json") },
      proto: __dirname + '/minecraft-data/data/pc/latest/proto.yml'
    }
  },
  'bedrock': {
    '0.14': {
      get blocks () { return require("./minecraft-data/data/bedrock/0.14/blocks.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.8/biomes.json") },
      get items () { return require("./minecraft-data/data/bedrock/0.14/items.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/0.14/protocol.json") },
      get version () { return require("./minecraft-data/data/bedrock/0.14/version.json") }
    },
    '0.15': {
      get blocks () { return require("./minecraft-data/data/bedrock/0.15/blocks.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.8/biomes.json") },
      get items () { return require("./minecraft-data/data/bedrock/0.15/items.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/0.15/protocol.json") },
      get version () { return require("./minecraft-data/data/bedrock/0.15/version.json") }
    },
    '1.0': {
      get blocks () { return require("./minecraft-data/data/bedrock/1.0/blocks.json") },
      get biomes () { return require("./minecraft-data/data/pc/1.8/biomes.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.0/items.json") },
      get version () { return require("./minecraft-data/data/bedrock/1.0/version.json") }
    },
    '1.16.201': {
      get attributes () { return require("./minecraft-data/data/bedrock/1.16.201/attributes.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.16.201/protocol.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.16.201/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.16.201/types.yml',
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.16.201/enchantments.json") },
      get version () { return require("./minecraft-data/data/bedrock/1.16.201/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.16.201/language.json") }
    },
    '1.16.210': {
      get attributes () { return require("./minecraft-data/data/bedrock/1.16.201/attributes.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.16.210/protocol.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.16.210/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.16.210/types.yml',
      get enchantments () { return require("./minecraft-data/data/bedrock/1.16.201/enchantments.json") },
      get version () { return require("./minecraft-data/data/bedrock/1.16.210/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.16.210/language.json") }
    },
    '1.16.220': {
      get attributes () { return require("./minecraft-data/data/bedrock/1.16.201/attributes.json") },
      get blocks () { return require("./minecraft-data/data/bedrock/1.16.220/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.16.220/blockStates.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.17.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.18.11/entities.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.16.201/enchantments.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.16.220/protocol.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.16.220/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.16.220/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.16.220/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.16.220/language.json") }
    },
    '1.17.0': {
      get attributes () { return require("./minecraft-data/data/bedrock/1.16.201/attributes.json") },
      get blocks () { return require("./minecraft-data/data/bedrock/1.17.0/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.17.0/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.17.0/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.17.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.18.11/entities.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.16.201/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.17.0/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.17.0/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.17.0/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.17.0/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.17.0/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.17.0/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.17.0/language.json") }
    },
    '1.17.10': {
      get attributes () { return require("./minecraft-data/data/bedrock/1.16.201/attributes.json") },
      get blocks () { return require("./minecraft-data/data/bedrock/1.17.10/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.17.10/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.17.10/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.17.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.18.11/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.17.10/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.17.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.16.201/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.17.10/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.17.10/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.17.10/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.17.10/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.17.10/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.17.10/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.17.10/language.json") }
    },
    '1.17.30': {
      get attributes () { return require("./minecraft-data/data/bedrock/1.16.201/attributes.json") },
      get blocks () { return require("./minecraft-data/data/bedrock/1.17.10/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.17.10/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.17.10/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.17.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.18.11/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.17.10/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.17.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.16.201/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.17.30/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.17.10/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.17.10/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.17.30/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.17.30/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.17.30/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.17.30/language.json") }
    },
    '1.17.40': {
      get attributes () { return require("./minecraft-data/data/bedrock/1.16.201/attributes.json") },
      get blocks () { return require("./minecraft-data/data/bedrock/1.17.40/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.17.40/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.17.40/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.17.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.18.11/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.17.10/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.17.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.16.201/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.17.40/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.17.10/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.17.10/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.17.40/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.17.40/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.17.40/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.17.40/language.json") }
    },
    '1.18.0': {
      get attributes () { return require("./minecraft-data/data/bedrock/1.16.201/attributes.json") },
      get blocks () { return require("./minecraft-data/data/bedrock/1.17.40/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.17.40/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.17.40/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.18.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.18.11/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.18.0/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.18.0/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.16.201/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.18.0/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.18.0/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.18.0/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.18.0/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.18.0/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.18.0/version.json") },
      get entityLoot () { return require("./minecraft-data/data/bedrock/1.18.0/entityLoot.json") },
      get blockLoot () { return require("./minecraft-data/data/bedrock/1.18.0/blockLoot.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.18.0/language.json") }
    },
    '1.18.11': {
      get attributes () { return require("./minecraft-data/data/bedrock/1.16.201/attributes.json") },
      get blocks () { return require("./minecraft-data/data/bedrock/1.18.11/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.18.11/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.18.11/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.18.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.18.11/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.18.11/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.18.11/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.16.201/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.18.11/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.18.11/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.18.11/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.18.11/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.18.11/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.18.11/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.18.11/language.json") }
    },
    '1.18.30': {
      get attributes () { return require("./minecraft-data/data/bedrock/1.16.201/attributes.json") },
      get blocks () { return require("./minecraft-data/data/bedrock/1.18.30/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.18.30/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.18.30/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.18.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.18.11/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.18.30/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.18.30/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.16.201/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.18.30/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.18.30/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.18.30/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.18.30/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.18.30/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.18.30/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.18.30/language.json") }
    },
    '1.19.1': {
      get attributes () { return require("./minecraft-data/data/bedrock/1.16.201/attributes.json") },
      get blocks () { return require("./minecraft-data/data/bedrock/1.19.1/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.19.1/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.19.1/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.19.1/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.19.1/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.19.1/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.1/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.19.1/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.19.1/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.19.1/blocksJ2B.json") },
      get blockMappings () { return require("./minecraft-data/data/bedrock/1.19.1/blockMappings.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.19.1/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.19.1/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.19.1/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.19.1/language.json") }
    },
    '1.19.10': {
      get attributes () { return require("./minecraft-data/data/bedrock/1.16.201/attributes.json") },
      get blocks () { return require("./minecraft-data/data/bedrock/1.19.1/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.19.1/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.19.1/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.19.1/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.19.1/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.19.10/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.19.10/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.19.1/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.19.1/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.19.10/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.19.10/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.19.10/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.19.10/language.json") }
    },
    '1.19.20': {
      get attributes () { return require("./minecraft-data/data/bedrock/1.16.201/attributes.json") },
      get blocks () { return require("./minecraft-data/data/bedrock/1.19.1/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.19.1/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.19.1/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.19.1/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.19.1/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.19.10/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.19.20/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.19.1/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.19.1/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.19.20/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.19.20/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.19.20/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.19.20/language.json") }
    },
    '1.19.21': {
      get attributes () { return require("./minecraft-data/data/bedrock/1.16.201/attributes.json") },
      get blocks () { return require("./minecraft-data/data/bedrock/1.19.1/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.19.1/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.19.1/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.19.1/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.19.1/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.19.10/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.19.21/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.19.1/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.19.1/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.19.21/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.19.21/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.19.21/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.19.20/language.json") }
    },
    '1.19.30': {
      get attributes () { return require("./minecraft-data/data/bedrock/1.16.201/attributes.json") },
      get blocks () { return require("./minecraft-data/data/bedrock/1.19.1/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.19.1/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.19.1/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.19.1/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.19.1/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.19.10/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.19.30/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.19.1/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.19.1/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.19.30/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.19.30/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.19.30/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.19.30/language.json") }
    },
    '1.19.40': {
      get attributes () { return require("./minecraft-data/data/bedrock/1.16.201/attributes.json") },
      get blocks () { return require("./minecraft-data/data/bedrock/1.19.1/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.19.1/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.19.1/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.19.1/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.19.1/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.19.10/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.19.40/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.19.1/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.19.1/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.19.40/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.19.40/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.19.40/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.19.40/language.json") }
    },
    '1.19.50': {
      get attributes () { return require("./minecraft-data/data/bedrock/1.16.201/attributes.json") },
      get blocks () { return require("./minecraft-data/data/bedrock/1.19.1/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.19.1/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.19.1/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.19.1/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.19.1/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.19.10/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.19.50/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.19.1/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.19.1/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.19.50/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.19.50/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.19.50/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.19.50/language.json") }
    },
    '1.19.60': {
      get attributes () { return require("./minecraft-data/data/bedrock/1.16.201/attributes.json") },
      get blocks () { return require("./minecraft-data/data/bedrock/1.19.1/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.19.1/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.19.1/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.19.1/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.19.1/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.19.10/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.19.60/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.19.1/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.19.1/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.19.60/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.19.60/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.19.60/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.19.60/language.json") }
    },
    '1.19.62': {
      get attributes () { return require("./minecraft-data/data/bedrock/1.16.201/attributes.json") },
      get blocks () { return require("./minecraft-data/data/bedrock/1.19.1/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.19.1/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.19.1/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.19.1/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.19.1/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.19.10/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.19.62/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.19.1/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.19.1/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.19.62/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.19.62/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.19.62/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.19.60/language.json") }
    },
    '1.19.63': {
      get attributes () { return require("./minecraft-data/data/bedrock/1.16.201/attributes.json") },
      get blocks () { return require("./minecraft-data/data/bedrock/1.19.1/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.19.1/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.19.1/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.19.1/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.19.1/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.19.10/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.19.62/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.19.1/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.19.1/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.19.62/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.19.62/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.19.63/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.19.60/language.json") }
    },
    '1.19.70': {
      get blocks () { return require("./minecraft-data/data/bedrock/1.19.1/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.19.1/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.19.1/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.19.1/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.19.1/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.19.10/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.19.70/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.19.1/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.19.1/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.19.70/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.19.70/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.19.70/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.19.70/language.json") }
    },
    '1.19.80': {
      get blocks () { return require("./minecraft-data/data/bedrock/1.19.80/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.19.80/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.19.1/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.19.1/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.19.1/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.19.10/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.19.80/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.19.1/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.19.1/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.19.80/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.19.80/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.19.80/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.19.80/language.json") }
    },
    '1.20.0': {
      get blocks () { return require("./minecraft-data/data/bedrock/1.20.0/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.20.0/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.20.0/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.20.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.19.1/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.20.0/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.20.0/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.20.0/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.20.0/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.20.0/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.20.0/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.20.0/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.20.0/language.json") }
    },
    '1.20.10': {
      get blocks () { return require("./minecraft-data/data/bedrock/1.20.10/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.20.10/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.20.0/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.20.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.19.1/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.20.0/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.20.10/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.20.0/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.20.0/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.20.10/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.20.10/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.20.10/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.20.10/language.json") }
    },
    '1.20.15': {
      get blocks () { return require("./minecraft-data/data/bedrock/1.20.10/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.20.10/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.20.0/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.20.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.19.1/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.20.0/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.20.10/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.20.0/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.20.0/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.20.10/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.20.10/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.20.15/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.20.10/language.json") }
    },
    '1.20.30': {
      get blocks () { return require("./minecraft-data/data/bedrock/1.20.30/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.20.30/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.20.0/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.20.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.19.1/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.20.0/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.20.30/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.20.0/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.20.0/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.20.30/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.20.30/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.20.30/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.20.10/language.json") }
    },
    '1.20.40': {
      get blocks () { return require("./minecraft-data/data/bedrock/1.20.40/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.20.40/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.20.0/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.20.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.19.1/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.20.0/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.20.40/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.20.0/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.20.0/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.20.40/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.20.40/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.20.40/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.20.10/language.json") }
    },
    '1.20.50': {
      get blocks () { return require("./minecraft-data/data/bedrock/1.20.50/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.20.50/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.20.50/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.20.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.19.1/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.20.0/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.20.50/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.20.50/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.20.50/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.20.50/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.20.50/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.20.50/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.20.10/language.json") }
    },
    '1.20.61': {
      get blocks () { return require("./minecraft-data/data/bedrock/1.20.61/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.20.61/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.20.61/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.20.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.19.1/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.20.0/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.20.61/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.20.61/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.20.61/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.20.61/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.20.61/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.20.61/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.20.10/language.json") }
    },
    '1.20.71': {
      get blocks () { return require("./minecraft-data/data/bedrock/1.20.71/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.20.71/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.20.71/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.20.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.19.1/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.20.0/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.20.71/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.16.201/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.20.71/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.20.71/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.20.71/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.20.71/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.20.71/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.20.10/language.json") }
    },
    '1.20.80': {
      get blocks () { return require("./minecraft-data/data/bedrock/1.20.71/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.20.71/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.20.71/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.20.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.19.1/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.20.0/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.20.80/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.20.80/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.20.71/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.20.71/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.20.80/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.20.80/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.20.80/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.20.10/language.json") }
    },
    '1.21.0': {
      get blocks () { return require("./minecraft-data/data/bedrock/1.21.0/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.21.0/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.21.0/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.20.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.21.0/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.21.0/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.21.0/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.20.80/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.21.0/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.21.0/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.21.0/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.21.0/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.21.0/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.20.10/language.json") }
    },
    '1.21.2': {
      get blocks () { return require("./minecraft-data/data/bedrock/1.21.0/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.21.0/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.21.0/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.20.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.21.0/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.21.0/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.21.2/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.20.80/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.21.0/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.21.0/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.21.2/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.21.2/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.21.2/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.20.10/language.json") }
    },
    '1.21.20': {
      get blocks () { return require("./minecraft-data/data/bedrock/1.21.0/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.21.0/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.21.0/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.20.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.21.0/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.21.0/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.21.20/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.20.80/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.21.0/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.21.0/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.21.20/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.21.20/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.21.20/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.20.10/language.json") }
    },
    '1.21.30': {
      get blocks () { return require("./minecraft-data/data/bedrock/1.21.0/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.21.0/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.21.0/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.20.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.21.0/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.21.0/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.21.30/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.20.80/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.21.0/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.21.0/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.21.30/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/1.21.30/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.21.30/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.20.10/language.json") }
    },
    '1.21.42': {
      get blocks () { return require("./minecraft-data/data/bedrock/1.21.0/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.21.0/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.21.0/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.20.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.21.0/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.21.0/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.21.42/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.20.80/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.21.0/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.21.0/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/1.21.42/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/latest/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.21.42/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.20.10/language.json") }
    },
    '1.21.50': {
      get blocks () { return require("./minecraft-data/data/bedrock/1.21.0/blocks.json") },
      get blockStates () { return require("./minecraft-data/data/bedrock/1.21.0/blockStates.json") },
      get blockCollisionShapes () { return require("./minecraft-data/data/bedrock/1.21.0/blockCollisionShapes.json") },
      get biomes () { return require("./minecraft-data/data/bedrock/1.20.0/biomes.json") },
      get entities () { return require("./minecraft-data/data/bedrock/1.21.0/entities.json") },
      get items () { return require("./minecraft-data/data/bedrock/1.21.0/items.json") },
      get recipes () { return require("./minecraft-data/data/bedrock/1.19.10/recipes.json") },
      get instruments () { return require("./minecraft-data/data/bedrock/1.17.0/instruments.json") },
      get materials () { return require("./minecraft-data/data/pc/1.17/materials.json") },
      get enchantments () { return require("./minecraft-data/data/bedrock/1.19.1/enchantments.json") },
      get effects () { return require("./minecraft-data/data/pc/1.17/effects.json") },
      get protocol () { return require("./minecraft-data/data/bedrock/1.21.50/protocol.json") },
      get windows () { return require("./minecraft-data/data/bedrock/1.16.201/windows.json") },
      get steve () { return require("./minecraft-data/data/bedrock/1.20.80/steve.json") },
      get blocksB2J () { return require("./minecraft-data/data/bedrock/1.21.0/blocksB2J.json") },
      get blocksJ2B () { return require("./minecraft-data/data/bedrock/1.21.0/blocksJ2B.json") },
      proto: __dirname + '/minecraft-data/data/bedrock/latest/proto.yml',
      types: __dirname + '/minecraft-data/data/bedrock/latest/types.yml',
      get version () { return require("./minecraft-data/data/bedrock/1.21.50/version.json") },
      get language () { return require("./minecraft-data/data/bedrock/1.20.10/language.json") }
    }
  }
}
