{"name": "minecraft-data", "version": "3.80.0", "description": "Provide easy access to minecraft data in node.js", "main": "index.js", "tonicExampleFilename": "example.js", "scripts": {"generate:types": "node typings/generate-typings.js", "generate:data": "node ./bin/generate_data.js", "test:types": "tsc typings/test-typings && node typings/test-typings.js", "test": "npm run generate:types && npm run generate:data && npm run lint && npm run test:types && mocha", "lint": "standard", "prepare": "npm run generate:data && npm run generate:types", "fix": "standard --fix"}, "standard": {"ignore": ["minecraft-data/**/*.js"]}, "repository": {"type": "git", "url": "**************:PrismarineJS/node-minecraft-data.git"}, "keywords": ["minecraft", "data", "node.js"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/PrismarineJS/node-minecraft-data/issues"}, "devDependencies": {"minecraft-data": ".", "@types/node": "^22.1.0", "json-schema-to-typescript": "11.0.5", "minecraft-packets": "^1.4.0", "mocha": "^10.0.0", "standard": "^17.0.0", "typescript": "~5.6.3"}, "dependencies": {}}