name: Update Helper
on:
  workflow_dispatch:
  schedule:
    - cron: "0 */2 * * *"

jobs:
  helper:
    name: update-checker
    runs-on: ubuntu-latest
    steps:
    - name: Checkout repository
      uses: actions/checkout@master
    - name: Set up Node.js
      uses: actions/setup-node@master
      with:
        node-version: 18.0.0
    - name: Install Github Actions toolkit
      run: npm i gh-helpers
      working-directory: .github/helper-bot
    # The env vars contain the relevant trigger information, so we don't need to pass it
    - name: Runs helper
      run: cd .github/helper-bot && node index.js
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
