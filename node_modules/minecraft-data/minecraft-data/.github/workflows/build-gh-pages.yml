name: Repository Dispatch
on:
  repository_dispatch:
    types: [node-mcData-release]
jobs:
  myEvent:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: 0
          ref: web
      - name: Use Node.js 18.x
        uses: actions/setup-node@v1
        with:
          node-version: 18.x
      - name: Run tool
        run: |
            npm install
            git config user.name 'rom1504bot'
            git config user.email '<EMAIL>'
            npm run gh-publish
