name: bedrock-ci

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 14

    steps:
    - name: Use Node.js 18.x
      uses: actions/setup-node@v1
      with:
        node-version: 18.x
    - name: Checkout node-minecraft-data
      uses: actions/checkout@v2
      with:
        repository: PrismarineJS/node-minecraft-data
        submodules: recursive
        path: node-mcdata
    # Erase the submodule for minecraft-data with a checkout of the current minecraft-data repo
    - run: rm -rf node-mcdata/minecraft-data
    - name: Checkout minecraft-data
      uses: actions/checkout@v2
      with:
        path: node-mcdata/minecraft-data
    # Now install local node-minecraft-data. Running "prepare" should gen data and types
    - name: Install minecraft-data
      run: |
        cd node-mcdata
        npm install
        npm run prepare
        npm link
    - name: Checkout bedrock-protocol
      uses: actions/checkout@v2
      with:
        repository: PrismarineJS/bedrock-protocol
        path: bedrock-protocol
    - run: ls -R .
    # I forget the correct install order, do both
    - name: Install bedrock-protocol
      run: |
        cd bedrock-protocol
        npm install ../node-mcdata
        npm i
        npm install ../node-mcdata
    - name: Running bedrock-protocol tests
      run: npm test
      working-directory: bedrock-protocol
