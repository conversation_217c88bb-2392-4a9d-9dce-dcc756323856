{"types": {"varint": "native", "varlong": "native", "optvarint": "varint", "pstring": "native", "buffer": "native", "u8": "native", "u16": "native", "u32": "native", "u64": "native", "i8": "native", "i16": "native", "i32": "native", "i64": "native", "bool": "native", "f32": "native", "f64": "native", "UUID": "native", "option": "native", "entityMetadataLoop": "native", "topBitSetTerminatedArray": "native", "bitfield": "native", "bitflags": "native", "container": "native", "switch": "native", "void": "native", "array": "native", "restBuffer": "native", "anonymousNbt": "native", "anonOptionalNbt": "native", "registryEntryHolder": "native", "registryEntryHolderSet": "native", "ByteArray": ["buffer", {"countType": "varint"}], "string": ["pstring", {"countType": "varint"}], "vec2f": ["container", [{"name": "x", "type": "f32"}, {"name": "y", "type": "f32"}]], "vec3f": ["container", [{"name": "x", "type": "f32"}, {"name": "y", "type": "f32"}, {"name": "z", "type": "f32"}]], "vec4f": ["container", [{"name": "x", "type": "f32"}, {"name": "y", "type": "f32"}, {"name": "z", "type": "f32"}, {"name": "w", "type": "f32"}]], "vec3f64": ["container", [{"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}]], "IDSet": ["registryEntryHolderSet", {"base": {"name": "name", "type": "string"}, "otherwise": {"name": "ids", "type": "varint"}}], "ContainerID": "varint", "SoundEvent": ["container", [{"name": "soundName", "type": "string"}, {"name": "fixedRange", "type": ["option", "f32"]}]], "RecipeDisplay": ["container", [{"name": "type", "type": ["mapper", {"type": "varint", "mappings": {"0": "crafting_shapeless", "1": "crafting_shaped", "2": "furnace", "3": "stonecutter", "4": "smithing"}}]}, {"name": "data", "type": ["switch", {"compareTo": "type", "fields": {"crafting_shapeless": ["container", [{"name": "ingredients", "type": ["array", {"countType": "varint", "type": "SlotDisplay"}]}, {"name": "result", "type": "SlotDisplay"}, {"name": "craftingStation", "type": "SlotDisplay"}]], "crafting_shaped": ["container", [{"name": "width", "type": "varint"}, {"name": "height", "type": "varint"}, {"name": "ingredients", "type": ["array", {"countType": "varint", "type": "SlotDisplay"}]}, {"name": "result", "type": "SlotDisplay"}, {"name": "craftingStation", "type": "SlotDisplay"}]], "furnace": ["container", [{"name": "ingredient", "type": "SlotDisplay"}, {"name": "fuel", "type": "SlotDisplay"}, {"name": "result", "type": "SlotDisplay"}, {"name": "craftingStation", "type": "SlotDisplay"}, {"name": "duration", "type": "varint"}, {"name": "experience", "type": "f32"}]], "stonecutter": ["container", [{"name": "ingredient", "type": "SlotDisplay"}, {"name": "result", "type": "SlotDisplay"}, {"name": "craftingStation", "type": "SlotDisplay"}]], "smithing": ["container", [{"name": "template", "type": "SlotDisplay"}, {"name": "base", "type": "SlotDisplay"}, {"name": "addition", "type": "SlotDisplay"}, {"name": "result", "type": "SlotDisplay"}, {"name": "craftingStation", "type": "SlotDisplay"}]]}}]}]], "SlotDisplay": ["container", [{"name": "type", "type": ["mapper", {"type": "varint", "mappings": {"0": "empty", "1": "any_fuel", "2": "item", "3": "item_stack", "4": "tag", "5": "smithing_trim", "6": "with_remainder", "7": "composite"}}]}, {"name": "data", "type": ["switch", {"compareTo": "type", "fields": {"empty": "void", "any_fuel": "void", "item": "varint", "item_stack": "Slot", "tag": "string", "simthing_trim": ["container", [{"name": "base", "type": "SlotDisplay"}, {"name": "material", "type": "SlotDisplay"}, {"name": "pattern", "type": "SlotDisplay"}]], "with_remainder": ["container", [{"name": "input", "type": "SlotDisplay"}, {"name": "remainder", "type": "SlotDisplay"}]], "composite": ["array", {"countType": "varint", "type": "SlotDisplay"}]}}]}]], "SlotComponentType": ["mapper", {"type": "varint", "mappings": {"0": "custom_data", "1": "max_stack_size", "2": "max_damage", "3": "damage", "4": "unbreakable", "5": "custom_name", "6": "item_name", "7": "item_model", "8": "lore", "9": "rarity", "10": "enchantments", "11": "can_place_on", "12": "can_break", "13": "attribute_modifiers", "14": "custom_model_data", "15": "hide_additional_tooltip", "16": "hide_tooltip", "17": "repair_cost", "18": "creative_slot_lock", "19": "enchantment_glint_override", "20": "intangible_projectile", "21": "food", "22": "consumable", "23": "use_remainder", "24": "use_cooldown", "25": "damage_resistant", "26": "tool", "27": "enchantable", "28": "equippable", "29": "repairable", "30": "glider", "31": "tooltip_style", "32": "death_protection", "33": "stored_enchantments", "34": "dyed_color", "35": "map_color", "36": "map_id", "37": "map_decorations", "38": "map_post_processing", "39": "charged_projectiles", "40": "bundle_contents", "41": "potion_contents", "42": "suspicious_stew_effects", "43": "writable_book_content", "44": "written_book_content", "45": "trim", "46": "debug_stick_state", "47": "entity_data", "48": "bucket_entity_data", "49": "block_entity_data", "50": "instrument", "51": "ominous_bottle_amplifier", "52": "jukebox_playable", "53": "recipes", "54": "lodestone_tracker", "55": "firework_explosion", "56": "fireworks", "57": "profile", "58": "note_block_sound", "59": "banner_patterns", "60": "base_color", "61": "pot_decorations", "62": "container", "63": "block_state", "64": "bees", "65": "lock", "66": "container_loot"}}], "SlotComponent": ["container", [{"name": "type", "type": "SlotComponentType"}, {"name": "data", "type": ["switch", {"compareTo": "type", "fields": {"custom_data": "anonymousNbt", "max_stack_size": "varint", "max_damage": "varint", "damage": "varint", "unbreakable": "bool", "custom_name": "anonymousNbt", "item_name": "anonymousNbt", "item_model": "string", "lore": ["array", {"countType": "varint", "type": "anonOptionalNbt"}], "rarity": ["mapper", {"type": "varint", "mappings": {"0": "common", "1": "uncommon", "2": "rare", "3": "epic"}}], "enchantments": ["container", [{"name": "enchantments", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "id", "type": "varint"}, {"name": "level", "type": "varint"}]]}]}, {"name": "showTooltip", "type": "bool"}]], "can_place_on": ["container", [{"name": "predicates", "type": ["array", {"countType": "varint", "type": "BlockPredicate"}]}, {"name": "showTooltip", "type": "bool"}]], "can_break": ["container", [{"name": "predicates", "type": ["array", {"countType": "varint", "type": "BlockPredicate"}]}, {"name": "showTooltip", "type": "bool"}]], "attribute_modifiers": ["container", [{"name": "attributes", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "typeId", "type": "varint"}, {"name": "name", "type": "string"}, {"name": "value", "type": "f64"}, {"name": "operation", "type": ["mapper", {"type": "varint", "mappings": {"0": "add", "1": "multiply_base", "2": "multiply_total"}}]}, {"name": "slot", "type": ["mapper", {"type": "varint", "mappings": {"0": "any", "1": "main_hand", "2": "off_hand", "3": "hand", "4": "feet", "5": "legs", "6": "chest", "7": "head", "8": "armor", "9": "body"}}]}]]}]}, {"name": "showTooltip", "type": "bool"}]], "custom_model_data": "varint", "hide_additional_tooltip": "void", "hide_tooltip": "void", "repair_cost": "varint", "creative_slot_lock": "void", "enchantment_glint_override": "bool", "intangible_projectile": "void", "food": ["container", [{"name": "nutrition", "type": "varint"}, {"name": "saturationModifier", "type": "f32"}, {"name": "canAlwaysEat", "type": "bool"}]], "consumable": ["container", [{"name": "consume_seconds", "type": "f32"}, {"name": "animation", "type": ["mapper", {"type": "varint", "mappings": {"0": "none", "1": "eat", "2": "drink", "3": "block", "4": "bow", "5": "spear", "6": "crossbow", "7": "spyglass", "8": "toot_horn", "9": "brush"}}]}, {"name": "sound", "type": ["registryEntryHolder", {"baseName": "soundId", "otherwise": {"name": "data", "type": "SoundEvent"}}]}, {"name": "makes_particles", "type": "bool"}, {"name": "effects", "type": ["array", {"countType": "varint", "type": "ConsumeEffect"}]}]], "use_remainder": "Slot", "use_cooldown": ["container", [{"name": "seconds", "type": "f32"}, {"name": "cooldownGroup", "type": ["option", "string"]}]], "damage_resistant": "string", "tool": ["container", [{"name": "rules", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "blocks", "type": "IDSet"}, {"name": "speed", "type": ["option", "f32"]}, {"name": "correctDropForBlocks", "type": ["option", "bool"]}]]}]}, {"name": "defaultMiningSpeed", "type": "f32"}, {"name": "damagePerBlock", "type": "varint"}]], "enchantable": "varint", "equippable": ["container", [{"name": "slot", "type": ["mapper", {"type": "varint", "mappings": {"0": "main_hand", "1": "off_hand", "2": "feet", "3": "legs", "4": "chest", "5": "head", "6": "body"}}]}, {"name": "sound", "type": ["registryEntryHolder", {"baseName": "soundId", "otherwise": {"name": "data", "type": "SoundEvent"}}]}, {"name": "model", "type": ["option", "string"]}, {"name": "cameraOverlay", "type": ["option", "string"]}, {"name": "allowedEntities", "type": ["option", "IDSet"]}, {"name": "dispensable", "type": "bool"}, {"name": "swappable", "type": "bool"}, {"name": "damageable", "type": "bool"}]], "repairable": ["container", [{"name": "items", "type": "IDSet"}]], "tooltip_style": "string", "death_protection": ["container", [{"name": "effects", "type": ["array", {"countType": "varint", "type": "ConsumeEffect"}]}]], "stored_enchantments": ["container", [{"name": "enchantments", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "id", "type": "varint"}, {"name": "level", "type": "varint"}]]}]}, {"name": "showInTooltip", "type": "bool"}]], "dyed_color": ["container", [{"name": "color", "type": "varint"}, {"name": "showTooltip", "type": "bool"}]], "map_color": "varint", "map_id": "varint", "map_decorations": "anonOptionalNbt", "map_post_processing": "varint", "charged_projectiles": ["container", [{"name": "projectiles", "type": ["array", {"countType": "varint", "type": "Slot"}]}]], "bundle_contents": ["container", [{"name": "contents", "type": ["array", {"countType": "varint", "type": "Slot"}]}]], "potion_contents": ["container", [{"name": "potionId", "type": ["option", "varint"]}, {"name": "customColor", "type": ["option", "varint"]}, {"name": "customEffects", "type": ["array", {"countType": "varint", "type": "PotionEffect"}]}, {"name": "customName", "type": "string"}]], "suspicious_stew_effects": ["container", [{"name": "effects", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "effect", "type": "varint"}, {"name": "duration", "type": "varint"}]]}]}]], "writable_book_content": ["container", [{"name": "pages", "type": ["array", {"countType": "varint", "type": "BookPage"}]}]], "written_book_content": ["container", [{"name": "rawTitle", "type": "string"}, {"name": "filteredTitle", "type": ["option", "string"]}, {"name": "author", "type": "string"}, {"name": "generation", "type": "varint"}, {"name": "pages", "type": ["array", {"countType": "varint", "type": "BookPage"}]}, {"name": "resolved", "type": "bool"}]], "trim": ["container", [{"name": "materialType", "type": "varint"}, {"anon": true, "type": ["switch", {"compareTo": "materialType", "fields": {"0": ["container", [{"name": "assetName", "type": "string"}, {"name": "ingredientId", "type": "varint"}, {"name": "itemModelIndex", "type": "f32"}, {"name": "numberOfOverrides", "type": "optvarint"}, {"name": "override", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "armorMaterialType", "type": "varint"}, {"name": "overridenAssetName", "type": "string"}]]}]}, {"name": "description", "type": "string"}]]}}]}, {"name": "trimPatternType", "type": "varint"}, {"anon": true, "type": ["switch", {"compareTo": "trimPatternType", "fields": {"0": ["container", [{"name": "assetName", "type": "string"}, {"name": "templateItem", "type": "varint"}, {"name": "description", "type": "string"}, {"name": "decal", "type": "bool"}]]}}]}, {"name": "showInTooltip", "type": "bool"}]], "debug_stick_state": "anonymousNbt", "entity_data": "anonymousNbt", "bucket_entity_data": "anonymousNbt", "block_entity_data": "anonymousNbt", "instrument": ["container", [{"name": "instrumentType", "type": "varint"}, {"anon": true, "type": ["switch", {"compareTo": "instrumentType", "fields": {"0": ["container", [{"name": "soundEvent", "type": "string"}, {"name": "useDuration", "type": "f32"}, {"name": "range", "type": "f32"}]]}}]}]], "ominous_bottle_amplifier": "varint", "jukebox_playable": ["container", [{"name": "directMode", "type": "bool"}, {"anon": true, "type": ["switch", {"compareTo": "directMode", "fields": {"true": ["container", [{"name": "jukeboxSongName", "type": "string"}, {"name": "jukeboxSongType", "type": "varint"}, {"anon": true, "type": ["switch", {"compareTo": "jukeboxSongType", "fields": {"0": ["container", [{"name": "soundEvent", "type": ["container", [{"name": "soundEventType", "type": "varint"}, {"anon": true, "type": ["switch", {"compareTo": "soundEventType", "fields": {"0": ["container", [{"name": "soundName", "type": "string"}, {"name": "fixedRange", "type": ["option", "f32"]}]]}}]}]]}]]}}]}, {"name": "description", "type": "anonymousNbt"}, {"name": "duration", "type": "f32"}, {"name": "output", "type": "varint"}]], "false": ["container", [{"name": "songLocation", "type": "string"}]]}}]}, {"name": "showInTooltip", "type": "bool"}]], "recipes": "anonymousNbt", "lodestone_tracker": ["container", [{"name": "globalPosition", "type": ["option", ["container", [{"name": "dimension", "type": "string"}, {"name": "position", "type": "position"}]]]}, {"name": "tracked", "type": "bool"}]], "firework_explosion": "FireworkExplosion", "fireworks": ["container", [{"name": "flightDuration", "type": "varint"}, {"name": "explosions", "type": ["array", {"countType": "varint", "type": "FireworkExplosion"}]}]], "profile": ["container", [{"name": "<PERSON><PERSON><PERSON>", "type": "bool"}, {"name": "name", "type": "string"}, {"name": "hasUniqueId", "type": "bool"}, {"name": "uniqueId", "type": "UUID"}, {"name": "properties", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "property", "type": "string"}, {"name": "value", "type": "string"}, {"name": "hasSignature", "type": "bool"}, {"name": "signature", "type": "string"}]]}]}]], "note_block_sound": "string", "banner_patterns": ["container", [{"name": "layers", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "patternType", "type": "varint"}, {"anon": true, "type": ["switch", {"compareTo": "patternType", "fields": {"0": ["container", [{"name": "assetId", "type": "string"}, {"name": "<PERSON><PERSON><PERSON>", "type": "string"}]]}}]}, {"name": "color", "type": "varint"}]]}]}]], "base_color": "varint", "pot_decorations": ["container", [{"name": "decorations", "type": ["array", {"countType": "varint", "type": "varint"}]}]], "container": ["container", [{"name": "contents", "type": ["array", {"countType": "varint", "type": "Slot"}]}]], "block_state": ["container", [{"name": "properties", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "property", "type": "string"}, {"name": "value", "type": "string"}]]}]}]], "bees": ["container", [{"name": "bees", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "nbtData", "type": "anonymousNbt"}, {"name": "ticksInHive", "type": "varint"}, {"name": "minTicksInHive", "type": "varint"}]]}]}]], "lock": "anonymousNbt", "container_loot": "anonymousNbt"}}]}]], "Slot": ["container", [{"name": "itemCount", "type": "varint"}, {"anon": true, "type": ["switch", {"compareTo": "itemCount", "fields": {"0": "void"}, "default": ["container", [{"name": "item", "type": "varint"}, {"name": "addedComponentCount", "type": "varint"}, {"name": "removedComponentCount", "type": "varint"}, {"name": "components", "type": ["array", {"count": "addedComponentCount", "type": "SlotComponent"}]}, {"name": "removeComponents", "type": ["array", {"count": "removedComponentCount", "type": ["container", [{"name": "type", "type": "SlotComponentType"}]]}]}]]}]}]], "FireworkExplosion": ["container", [{"name": "shape", "type": ["mapper", {"type": "varint", "mappings": {"0": "small_ball", "1": "large_ball", "2": "star", "3": "creeper", "4": "burst"}}]}, {"name": "colors", "type": ["array", {"countType": "varint", "type": "i32"}]}, {"name": "fadeColors", "type": ["array", {"countType": "varint", "type": "i32"}]}, {"name": "hasTrail", "type": "bool"}, {"name": "hasTwinkle", "type": "bool"}]], "BookPage": ["container", [{"name": "content", "type": "string"}, {"name": "filteredContent", "type": ["option", "string"]}]], "EffectDetail": ["container", [{"name": "amplifier", "type": "varint"}, {"name": "duration", "type": "varint"}, {"name": "ambient", "type": "bool"}, {"name": "showParticles", "type": "bool"}, {"name": "showIcon", "type": "bool"}, {"name": "hiddenEffect", "type": ["option", "EffectDetail"]}]], "PotionEffect": ["container", [{"name": "id", "type": "varint"}, {"name": "details", "type": "EffectDetail"}]], "ConsumeEffect": ["container", [{"name": "type", "type": ["mapper", {"type": "varint", "mappings": {"0": "apply_effects", "1": "remove_effects", "2": "clear_all_effects", "3": "teleport_randomly", "4": "play_sound"}}]}, {"anon": true, "type": ["switch", {"compareTo": "type", "fields": {"apply_effects": ["container", [{"name": "effects", "type": ["array", {"countType": "varint", "type": "PotionEffect"}]}, {"name": "probability", "type": "f32"}]], "remove_effects": ["container", [{"name": "effects", "type": "IDSet"}]], "clear_all_effects": "void", "teleport_randomly": ["container", [{"name": "diameter", "type": "f32"}]], "play_sound": ["container", [{"name": "sound", "type": ["registryEntryHolder", {"baseName": "soundId", "otherwise": {"name": "data", "type": "SoundEvent"}}]}]]}}]}]], "BlockProperty": ["container", [{"name": "name", "type": "string"}, {"name": "isExactMatch", "type": "bool"}, {"name": "exactValue", "type": ["option", "string"]}, {"name": "minValue", "type": ["option", "string"]}, {"name": "maxValue", "type": ["option", "string"]}]], "BlockPredicate": ["container", [{"name": "blockSet", "type": ["option", ["registryEntryHolderSet", {"base": {"name": "name", "type": "string"}, "otherwise": {"name": "blockIds", "type": "varint"}}]]}, {"name": "properties", "type": ["option", ["array", {"countType": "varint", "type": "BlockProperty"}]]}, {"name": "nbt", "type": "anonOptionalNbt"}]], "Particle": ["container", [{"name": "type", "type": ["mapper", {"type": "varint", "mappings": {"0": "angry_villager", "1": "block", "2": "block_marker", "3": "bubble", "4": "cloud", "5": "crit", "6": "damage_indicator", "7": "dragon_breath", "8": "dripping_lava", "9": "falling_lava", "10": "landing_lava", "11": "dripping_water", "12": "falling_water", "13": "dust", "14": "dust_color_transition", "15": "effect", "16": "elder_guardian", "17": "enchanted_hit", "18": "enchant", "19": "end_rod", "20": "entity_effect", "21": "explosion_emitter", "22": "explosion", "23": "gust", "24": "small_gust", "25": "gust_emitter_large", "26": "gust_emitter_small", "27": "sonic_boom", "28": "falling_dust", "29": "firework", "30": "fishing", "31": "flame", "32": "infested", "33": "cherry_leaves", "34": "sculk_soul", "35": "sculk_charge", "36": "sculk_charge_pop", "37": "soul_fire_flame", "38": "soul", "39": "flash", "40": "happy_villager", "41": "composter", "42": "heart", "43": "instant_effect", "44": "item", "45": "vibration", "46": "trail", "47": "item_slime", "48": "item_cobweb", "49": "item_snowball", "50": "large_smoke", "51": "lava", "52": "mycelium", "53": "note", "54": "poof", "55": "portal", "56": "rain", "57": "smoke", "58": "white_smoke", "59": "sneeze", "60": "spit", "61": "squid_ink", "62": "sweep_attack", "63": "totem_of_undying", "64": "underwater", "65": "splash", "66": "witch", "67": "bubble_pop", "68": "current_down", "69": "bubble_column_up", "70": "nautilus", "71": "dolphin", "72": "campfire_cosy_smoke", "73": "campfire_signal_smoke", "74": "dripping_honey", "75": "falling_honey", "76": "landing_honey", "77": "falling_nectar", "78": "falling_spore_blossom", "79": "ash", "80": "crimson_spore", "81": "warped_spore", "82": "spore_blossom_air", "83": "dripping_obsidian_tear", "84": "falling_obsidian_tear", "85": "landing_obsidian_tear", "86": "reverse_portal", "87": "white_ash", "88": "small_flame", "89": "snowflake", "90": "dripping_dripstone_lava", "91": "falling_dripstone_lava", "92": "dripping_dripstone_water", "93": "falling_dripstone_water", "94": "glow_squid_ink", "95": "glow", "96": "wax_on", "97": "wax_off", "98": "electric_spark", "99": "scrape", "100": "shriek", "101": "egg_crack", "102": "dust_plume", "103": "trial_spawner_detected_player", "104": "trial_spawner_detected_player_ominous", "105": "vault_connection", "106": "dust_pillar", "107": "ominous_spawning", "108": "raid_omen", "109": "trial_omen", "110": "block_crumble"}}]}, {"name": "data", "type": ["switch", {"compareTo": "type", "fields": {"block": "varint", "block_marker": "varint", "falling_dust": "varint", "dust_pillar": "varint", "block_crumble": "varint", "dust": ["container", [{"name": "red", "type": "f32"}, {"name": "green", "type": "f32"}, {"name": "blue", "type": "f32"}, {"name": "scale", "type": "f32"}]], "dust_color_transition": ["container", [{"name": "fromRed", "type": "f32"}, {"name": "fromGreen", "type": "f32"}, {"name": "fromBlue", "type": "f32"}, {"name": "scale", "type": "f32"}, {"name": "toRed", "type": "f32"}, {"name": "<PERSON><PERSON><PERSON>", "type": "f32"}, {"name": "toBlue", "type": "f32"}]], "entity_effect": "i32", "item": "Slot", "sculk_charge": "f32", "shriek": "varint", "vibration": ["container", [{"name": "position_type", "type": ["mapper", {"type": "varint", "mappings": {"0": "block", "1": "entity"}}]}, {"name": "position", "type": ["switch", {"compareTo": "position_type", "fields": {"block": "position", "entity": ["container", [{"name": "entityId", "type": "varint"}, {"name": "entity_eye_height", "type": "f32"}]]}}]}, {"name": "ticks", "type": "varint"}]], "trail": ["container", [{"name": "target", "type": "vec3f64"}, {"name": "color", "type": "u8"}]]}}]}]], "ingredient": ["array", {"countType": "varint", "type": "Slot"}], "position": ["bitfield", [{"name": "x", "size": 26, "signed": true}, {"name": "z", "size": 26, "signed": true}, {"name": "y", "size": 12, "signed": true}]], "soundSource": ["mapper", {"type": "varint", "mappings": {"0": "master", "1": "music", "2": "record", "3": "weather", "4": "block", "5": "hostile", "6": "neutral", "7": "player", "8": "ambient", "9": "voice"}}], "packedChunkPos": ["container", [{"name": "z", "type": "i32"}, {"name": "x", "type": "i32"}]], "previousMessages": ["array", {"countType": "varint", "type": ["container", [{"name": "id", "type": "varint"}, {"name": "signature", "type": ["switch", {"compareTo": "id", "fields": {"0": ["buffer", {"count": 256}]}, "default": "void"}]}]]}], "entityMetadataEntry": ["container", [{"name": "key", "type": "u8"}, {"name": "type", "type": ["mapper", {"type": "varint", "mappings": {"0": "byte", "1": "int", "2": "long", "3": "float", "4": "string", "5": "component", "6": "optional_component", "7": "item_stack", "8": "boolean", "9": "rotations", "10": "block_pos", "11": "optional_block_pos", "12": "direction", "13": "optional_uuid", "14": "block_state", "15": "optional_block_state", "16": "compound_tag", "17": "particle", "18": "particles", "19": "villager_data", "20": "optional_unsigned_int", "21": "pose", "22": "cat_variant", "23": "wolf_variant", "24": "frog_variant", "25": "optional_global_pos", "26": "painting_variant", "27": "sniffer_state", "28": "armadillo_state", "29": "vector3", "30": "quaternion"}}]}, {"name": "value", "type": ["switch", {"compareTo": "type", "fields": {"byte": "i8", "int": "varint", "long": "varlong", "float": "f32", "string": "string", "component": "anonymousNbt", "optional_component": ["option", "anonymousNbt"], "item_stack": "Slot", "boolean": "bool", "rotations": ["container", [{"name": "pitch", "type": "f32"}, {"name": "yaw", "type": "f32"}, {"name": "roll", "type": "f32"}]], "block_pos": "position", "optional_block_pos": ["option", "position"], "direction": "varint", "optional_uuid": ["option", "UUID"], "block_state": "varint", "optional_block_state": "optvarint", "compound_tag": "anonymousNbt", "particle": "Particle", "particles": ["array", {"countType": "varint", "type": "Particle"}], "villager_data": ["container", [{"name": "villagerType", "type": "varint"}, {"name": "villagerProfession", "type": "varint"}, {"name": "level", "type": "varint"}]], "optional_unsigned_int": "optvarint", "pose": "varint", "cat_variant": "varint", "wolf_variant": ["registryEntryHolder", {"baseName": "variantId", "otherwise": {"name": "variantData", "type": "EntityMetadataWolfVariant"}}], "frog_variant": "varint", "optional_global_pos": ["option", "string"], "painting_variant": ["registryEntryHolder", {"baseName": "variantId", "otherwise": {"name": "variantData", "type": "EntityMetadataPaintingVariant"}}], "sniffer_state": "varint", "armadillo_state": "varint", "vector3": "vec3f", "quaternion": "vec4f"}}]}]], "EntityMetadataPaintingVariant": ["container", [{"name": "width", "type": "i32"}, {"name": "height", "type": "i32"}, {"name": "assetId", "type": "string"}, {"name": "title", "type": ["option", "anonymousNbt"]}, {"name": "author", "type": ["option", "anonymousNbt"]}]], "EntityMetadataWolfVariant": ["container", [{"name": "wildTexture", "type": "string"}, {"name": "tameTexture", "type": "string"}, {"name": "angryTexture", "type": "string"}, {"name": "biome", "type": "IDSet"}]], "entityMetadata": ["entityMetadataLoop", {"endVal": 255, "type": "entityMetadataEntry"}], "tags": ["array", {"countType": "varint", "type": ["container", [{"name": "tagName", "type": "string"}, {"name": "entries", "type": ["array", {"countType": "varint", "type": "varint"}]}]]}], "chunkBlockEntity": ["container", [{"anon": true, "type": ["bitfield", [{"name": "x", "size": 4, "signed": false}, {"name": "z", "size": 4, "signed": false}]]}, {"name": "y", "type": "i16"}, {"name": "type", "type": "varint"}, {"name": "nbtData", "type": "anonOptionalNbt"}]], "chat_session": ["option", ["container", [{"name": "uuid", "type": "UUID"}, {"name": "public<PERSON>ey", "type": ["container", [{"name": "expireTime", "type": "i64"}, {"name": "keyBytes", "type": ["buffer", {"countType": "varint"}]}, {"name": "keySignature", "type": ["buffer", {"countType": "varint"}]}]]}]]], "game_profile": ["container", [{"name": "name", "type": "string"}, {"name": "properties", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "key", "type": "string"}, {"name": "value", "type": "string"}, {"name": "signature", "type": ["option", "string"]}]]}]}]], "command_node": ["container", [{"name": "flags", "type": ["bitfield", [{"name": "unused", "size": 3, "signed": false}, {"name": "has_custom_suggestions", "size": 1, "signed": false}, {"name": "has_redirect_node", "size": 1, "signed": false}, {"name": "has_command", "size": 1, "signed": false}, {"name": "command_node_type", "size": 2, "signed": false}]]}, {"name": "children", "type": ["array", {"countType": "varint", "type": "varint"}]}, {"name": "redirectNode", "type": ["switch", {"compareTo": "flags/has_redirect_node", "fields": {"1": "varint"}, "default": "void"}]}, {"name": "extraNodeData", "type": ["switch", {"compareTo": "flags/command_node_type", "fields": {"0": "void", "1": ["container", [{"name": "name", "type": "string"}]], "2": ["container", [{"name": "name", "type": "string"}, {"name": "parser", "type": ["mapper", {"type": "varint", "mappings": {"0": "brigadier:bool", "1": "brigadier:float", "2": "brigadier:double", "3": "brigadier:integer", "4": "brigadier:long", "5": "brigadier:string", "6": "minecraft:entity", "7": "minecraft:game_profile", "8": "minecraft:block_pos", "9": "minecraft:column_pos", "10": "minecraft:vec3", "11": "minecraft:vec2", "12": "minecraft:block_state", "13": "minecraft:block_predicate", "14": "minecraft:item_stack", "15": "minecraft:item_predicate", "16": "minecraft:color", "17": "minecraft:component", "18": "minecraft:style", "19": "minecraft:message", "20": "minecraft:nbt", "21": "minecraft:nbt_tag", "22": "minecraft:nbt_path", "23": "minecraft:objective", "24": "minecraft:objective_criteria", "25": "minecraft:operation", "26": "minecraft:particle", "27": "minecraft:angle", "28": "minecraft:rotation", "29": "minecraft:scoreboard_slot", "30": "minecraft:score_holder", "31": "minecraft:swizzle", "32": "minecraft:team", "33": "minecraft:item_slot", "34": "minecraft:item_slots", "35": "minecraft:resource_location", "36": "minecraft:function", "37": "minecraft:entity_anchor", "38": "minecraft:int_range", "39": "minecraft:float_range", "40": "minecraft:dimension", "41": "minecraft:gamemode", "42": "minecraft:time", "43": "minecraft:resource_or_tag", "44": "minecraft:resource_or_tag_key", "45": "minecraft:resource", "46": "minecraft:resource_key", "47": "minecraft:template_mirror", "48": "minecraft:template_rotation", "49": "minecraft:heightmap", "50": "minecraft:loot_table", "51": "minecraft:loot_predicate", "52": "minecraft:loot_modifier", "53": "minecraft:uuid"}}]}, {"name": "properties", "type": ["switch", {"compareTo": "parser", "fields": {"brigadier:bool": "void", "brigadier:float": ["container", [{"name": "flags", "type": ["bitfield", [{"name": "unused", "size": 6, "signed": false}, {"name": "max_present", "size": 1, "signed": false}, {"name": "min_present", "size": 1, "signed": false}]]}, {"name": "min", "type": ["switch", {"compareTo": "flags/min_present", "fields": {"1": "f32"}, "default": "void"}]}, {"name": "max", "type": ["switch", {"compareTo": "flags/max_present", "fields": {"1": "f32"}, "default": "void"}]}]], "brigadier:double": ["container", [{"name": "flags", "type": ["bitfield", [{"name": "unused", "size": 6, "signed": false}, {"name": "max_present", "size": 1, "signed": false}, {"name": "min_present", "size": 1, "signed": false}]]}, {"name": "min", "type": ["switch", {"compareTo": "flags/min_present", "fields": {"1": "f64"}, "default": "void"}]}, {"name": "max", "type": ["switch", {"compareTo": "flags/max_present", "fields": {"1": "f64"}, "default": "void"}]}]], "brigadier:integer": ["container", [{"name": "flags", "type": ["bitfield", [{"name": "unused", "size": 6, "signed": false}, {"name": "max_present", "size": 1, "signed": false}, {"name": "min_present", "size": 1, "signed": false}]]}, {"name": "min", "type": ["switch", {"compareTo": "flags/min_present", "fields": {"1": "i32"}, "default": "void"}]}, {"name": "max", "type": ["switch", {"compareTo": "flags/max_present", "fields": {"1": "i32"}, "default": "void"}]}]], "brigadier:long": ["container", [{"name": "flags", "type": ["bitfield", [{"name": "unused", "size": 6, "signed": false}, {"name": "max_present", "size": 1, "signed": false}, {"name": "min_present", "size": 1, "signed": false}]]}, {"name": "min", "type": ["switch", {"compareTo": "flags/min_present", "fields": {"1": "i64"}, "default": "void"}]}, {"name": "max", "type": ["switch", {"compareTo": "flags/max_present", "fields": {"1": "i64"}, "default": "void"}]}]], "brigadier:string": ["mapper", {"type": "varint", "mappings": {"0": "SINGLE_WORD", "1": "QUOTABLE_PHRASE", "2": "GREEDY_PHRASE"}}], "minecraft:entity": ["bitfield", [{"name": "unused", "size": 6, "signed": false}, {"name": "onlyAllowPlayers", "size": 1, "signed": false}, {"name": "onlyAllowEntities", "size": 1, "signed": false}]], "minecraft:game_profile": "void", "minecraft:block_pos": "void", "minecraft:column_pos": "void", "minecraft:vec3": "void", "minecraft:vec2": "void", "minecraft:block_state": "void", "minecraft:block_predicate": "void", "minecraft:item_stack": "void", "minecraft:item_predicate": "void", "minecraft:color": "void", "minecraft:component": "void", "minecraft:message": "void", "minecraft:nbt": "void", "minecraft:nbt_path": "void", "minecraft:objective": "void", "minecraft:objective_criteria": "void", "minecraft:operation": "void", "minecraft:particle": "void", "minecraft:angle": "void", "minecraft:rotation": "void", "minecraft:scoreboard_slot": "void", "minecraft:score_holder": ["bitfield", [{"name": "unused", "size": 7, "signed": false}, {"name": "allowMultiple", "size": 1, "signed": false}]], "minecraft:swizzle": "void", "minecraft:team": "void", "minecraft:item_slot": "void", "minecraft:resource_location": "void", "minecraft:function": "void", "minecraft:entity_anchor": "void", "minecraft:int_range": "void", "minecraft:float_range": "void", "minecraft:dimension": "void", "minecraft:gamemode": "void", "minecraft:time": ["container", [{"name": "min", "type": "i32"}]], "minecraft:resource_or_tag": ["container", [{"name": "registry", "type": "string"}]], "minecraft:resource_or_tag_key": ["container", [{"name": "registry", "type": "string"}]], "minecraft:resource": ["container", [{"name": "registry", "type": "string"}]], "minecraft:resource_key": ["container", [{"name": "registry", "type": "string"}]], "minecraft:template_mirror": "void", "minecraft:template_rotation": "void", "minecraft:heightmap": "void", "minecraft:uuid": "void"}}]}, {"name": "suggestionType", "type": ["switch", {"compareTo": "../flags/has_custom_suggestions", "fields": {"1": "string"}, "default": "void"}]}]]}}]}]], "packet_common_cookie_request": ["container", [{"name": "cookie", "type": "string"}]], "packet_common_store_cookie": ["container", [{"name": "key", "type": "string"}, {"name": "value", "type": "ByteArray"}]], "packet_common_transfer": ["container", [{"name": "host", "type": "string"}, {"name": "port", "type": "varint"}]], "packet_common_cookie_response": ["container", [{"name": "key", "type": "string"}, {"name": "value", "type": "ByteArray"}]], "packet_common_select_known_packs": ["container", [{"name": "packs", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "namespace", "type": "string"}, {"name": "id", "type": "string"}, {"name": "version", "type": "string"}]]}]}]], "packet_common_custom_report_details": ["container", [{"name": "details", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "key", "type": "string"}, {"name": "value", "type": "string"}]]}]}]], "packet_common_remove_resource_pack": ["container", [{"name": "uuid", "type": ["option", "UUID"]}]], "packet_common_add_resource_pack": ["container", [{"name": "uuid", "type": "UUID"}, {"name": "url", "type": "string"}, {"name": "hash", "type": "string"}, {"name": "forced", "type": "bool"}, {"name": "promptMessage", "type": ["option", "anonymousNbt"]}]], "ServerLinkType": ["mapper", {"type": "varint", "mappings": {"0": "bug_report", "1": "community_guidelines", "2": "support", "3": "status", "4": "feedback", "5": "community", "6": "website", "7": "forums", "8": "news", "9": "announcements"}}], "packet_common_server_links": ["container", [{"name": "links", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "hasKnownType", "type": "bool"}, {"name": "knownType", "type": ["switch", {"compareTo": "hasKnownType", "fields": {"true": "ServerLinkType"}}]}, {"name": "unknownType", "type": ["switch", {"compareTo": "hasKnownType", "fields": {"false": "anonymousNbt"}}]}, {"name": "link", "type": "string"}]]}]}]]}, "handshaking": {"toClient": {"types": {"packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {}}]}]]}}, "toServer": {"types": {"packet_set_protocol": ["container", [{"name": "protocolVersion", "type": "varint"}, {"name": "serverHost", "type": "string"}, {"name": "serverPort", "type": "u16"}, {"name": "nextState", "type": "varint"}]], "packet_legacy_server_list_ping": ["container", [{"name": "payload", "type": "u8"}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "set_protocol", "0xfe": "legacy_server_list_ping"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"set_protocol": "packet_set_protocol", "legacy_server_list_ping": "packet_legacy_server_list_ping"}}]}]]}}}, "status": {"toClient": {"types": {"packet_server_info": ["container", [{"name": "response", "type": "string"}]], "packet_ping": ["container", [{"name": "time", "type": "i64"}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "server_info", "0x01": "ping"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"server_info": "packet_server_info", "ping": "packet_ping"}}]}]]}}, "toServer": {"types": {"packet_ping_start": ["container", []], "packet_ping": ["container", [{"name": "time", "type": "i64"}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "ping_start", "0x01": "ping"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"ping_start": "packet_ping_start", "ping": "packet_ping"}}]}]]}}}, "login": {"toClient": {"types": {"packet_disconnect": ["container", [{"name": "reason", "type": "string"}]], "packet_encryption_begin": ["container", [{"name": "serverId", "type": "string"}, {"name": "public<PERSON>ey", "type": ["buffer", {"countType": "varint"}]}, {"name": "verifyToken", "type": ["buffer", {"countType": "varint"}]}, {"name": "shouldAuthenticate", "type": "bool"}]], "packet_success": ["container", [{"name": "uuid", "type": "UUID"}, {"name": "username", "type": "string"}, {"name": "properties", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "name", "type": "string"}, {"name": "value", "type": "string"}, {"name": "signature", "type": ["option", "string"]}]]}]}]], "packet_compress": ["container", [{"name": "threshold", "type": "varint"}]], "packet_login_plugin_request": ["container", [{"name": "messageId", "type": "varint"}, {"name": "channel", "type": "string"}, {"name": "data", "type": "restBuffer"}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "disconnect", "0x01": "encryption_begin", "0x02": "success", "0x03": "compress", "0x04": "login_plugin_request", "0x05": "cookie_request"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"disconnect": "packet_disconnect", "encryption_begin": "packet_encryption_begin", "success": "packet_success", "compress": "packet_compress", "login_plugin_request": "packet_login_plugin_request", "cookie_request": "packet_common_cookie_request"}}]}]]}}, "toServer": {"types": {"packet_login_start": ["container", [{"name": "username", "type": "string"}, {"name": "playerUUID", "type": "UUID"}]], "packet_encryption_begin": ["container", [{"name": "sharedSecret", "type": ["buffer", {"countType": "varint"}]}, {"name": "verifyToken", "type": ["buffer", {"countType": "varint"}]}]], "packet_login_plugin_response": ["container", [{"name": "messageId", "type": "varint"}, {"name": "data", "type": ["option", "restBuffer"]}]], "packet_login_acknowledged": ["container", []], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "login_start", "0x01": "encryption_begin", "0x02": "login_plugin_response", "0x03": "login_acknowledged", "0x04": "cookie_response"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"login_start": "packet_login_start", "encryption_begin": "packet_encryption_begin", "login_plugin_response": "packet_login_plugin_response", "login_acknowledged": "packet_login_acknowledged", "cookie_response": "packet_common_cookie_response"}}]}]]}}}, "configuration": {"toClient": {"types": {"packet_custom_payload": ["container", [{"name": "channel", "type": "string"}, {"name": "data", "type": "restBuffer"}]], "packet_disconnect": ["container", [{"name": "reason", "type": "anonymousNbt"}]], "packet_finish_configuration": ["container", []], "packet_keep_alive": ["container", [{"name": "keepAliveId", "type": "i64"}]], "packet_ping": ["container", [{"name": "id", "type": "i32"}]], "packet_reset_chat": ["container", []], "packet_registry_data": ["container", [{"name": "id", "type": "string"}, {"name": "entries", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "key", "type": "string"}, {"name": "value", "type": ["option", "anonymousNbt"]}]]}]}]], "packet_feature_flags": ["container", [{"name": "features", "type": ["array", {"countType": "varint", "type": "string"}]}]], "packet_tags": ["container", [{"name": "tags", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "tagType", "type": "string"}, {"name": "tags", "type": "tags"}]]}]}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "cookie_request", "0x01": "custom_payload", "0x02": "disconnect", "0x03": "finish_configuration", "0x04": "keep_alive", "0x05": "ping", "0x06": "reset_chat", "0x07": "registry_data", "0x08": "remove_resource_pack", "0x09": "add_resource_pack", "0x0a": "store_cookie", "0x0b": "transfer", "0x0c": "feature_flags", "0x0d": "tags", "0x0e": "select_known_packs", "0x0f": "custom_report_details", "0x10": "server_links"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"cookie_request": "packet_common_cookie_request", "custom_payload": "packet_custom_payload", "disconnect": "packet_disconnect", "finish_configuration": "packet_finish_configuration", "keep_alive": "packet_keep_alive", "ping": "packet_ping", "reset_chat": "packet_reset_chat", "registry_data": "packet_registry_data", "remove_resource_pack": "packet_common_remove_resource_pack", "add_resource_pack": "packet_common_add_resource_pack", "store_cookie": "packet_common_store_cookie", "transfer": "packet_common_transfer", "feature_flags": "packet_feature_flags", "tags": "packet_tags", "select_known_packs": "packet_common_select_known_packs", "custom_report_details": "packet_common_custom_report_details", "server_links": "packet_common_server_links"}}]}]]}}, "toServer": {"types": {"packet_settings": ["container", [{"name": "locale", "type": "string"}, {"name": "viewDistance", "type": "i8"}, {"name": "chatFlags", "type": "varint"}, {"name": "chatColors", "type": "bool"}, {"name": "skinParts", "type": "u8"}, {"name": "mainHand", "type": "varint"}, {"name": "enableTextFiltering", "type": "bool"}, {"name": "enableServerListing", "type": "bool"}, {"name": "particles", "type": "varint"}]], "packet_custom_payload": ["container", [{"name": "channel", "type": "string"}, {"name": "data", "type": "restBuffer"}]], "packet_finish_configuration": ["container", []], "packet_keep_alive": ["container", [{"name": "keepAliveId", "type": "i64"}]], "packet_pong": ["container", [{"name": "id", "type": "i32"}]], "packet_resource_pack_receive": ["container", [{"name": "uuid", "type": "UUID"}, {"name": "result", "type": "varint"}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "settings", "0x01": "cookie_response", "0x02": "custom_payload", "0x03": "finish_configuration", "0x04": "keep_alive", "0x05": "pong", "0x06": "resource_pack_receive", "0x07": "select_known_packs", "0x08": "custom_report_details", "0x09": "server_links"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"settings": "packet_settings", "cookie_response": "packet_common_cookie_response", "custom_payload": "packet_custom_payload", "finish_configuration": "packet_finish_configuration", "keep_alive": "packet_keep_alive", "pong": "packet_pong", "resource_pack_receive": "packet_resource_pack_receive", "select_known_packs": "packet_common_select_known_packs", "custom_report_details": "packet_common_custom_report_details", "server_links": "packet_common_server_links"}}]}]]}}}, "play": {"toClient": {"types": {"SpawnInfo": ["container", [{"name": "dimension", "type": "varint"}, {"name": "name", "type": "string"}, {"name": "hashedSeed", "type": "i64"}, {"name": "gamemode", "type": ["mapper", {"type": "i8", "mappings": {"0": "survival", "1": "creative", "2": "adventure", "3": "spectator"}}]}, {"name": "previousGamemode", "type": "u8"}, {"name": "isDebug", "type": "bool"}, {"name": "is<PERSON><PERSON>", "type": "bool"}, {"name": "death", "type": ["option", ["container", [{"name": "dimensionName", "type": "string"}, {"name": "location", "type": "position"}]]]}, {"name": "portalCooldown", "type": "varint"}, {"name": "seaLevel", "type": "varint"}]], "packet_spawn_entity": ["container", [{"name": "entityId", "type": "varint"}, {"name": "objectUUID", "type": "UUID"}, {"name": "type", "type": "varint"}, {"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "pitch", "type": "i8"}, {"name": "yaw", "type": "i8"}, {"name": "head<PERSON><PERSON>", "type": "i8"}, {"name": "objectData", "type": "varint"}, {"name": "velocityX", "type": "i16"}, {"name": "velocityY", "type": "i16"}, {"name": "velocityZ", "type": "i16"}]], "packet_spawn_entity_experience_orb": ["container", [{"name": "entityId", "type": "varint"}, {"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "count", "type": "i16"}]], "packet_animation": ["container", [{"name": "entityId", "type": "varint"}, {"name": "animation", "type": "u8"}]], "packet_statistics": ["container", [{"name": "entries", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "categoryId", "type": "varint"}, {"name": "statisticId", "type": "varint"}, {"name": "value", "type": "varint"}]]}]}]], "packet_acknowledge_player_digging": ["container", [{"name": "sequenceId", "type": "varint"}]], "packet_block_break_animation": ["container", [{"name": "entityId", "type": "varint"}, {"name": "location", "type": "position"}, {"name": "destroyStage", "type": "i8"}]], "packet_tile_entity_data": ["container", [{"name": "location", "type": "position"}, {"name": "action", "type": "varint"}, {"name": "nbtData", "type": "anonOptionalNbt"}]], "packet_block_action": ["container", [{"name": "location", "type": "position"}, {"name": "byte1", "type": "u8"}, {"name": "byte2", "type": "u8"}, {"name": "blockId", "type": "varint"}]], "packet_block_change": ["container", [{"name": "location", "type": "position"}, {"name": "type", "type": "varint"}]], "packet_boss_bar": ["container", [{"name": "entityUUID", "type": "UUID"}, {"name": "action", "type": "varint"}, {"name": "title", "type": ["switch", {"compareTo": "action", "fields": {"0": "anonymousNbt", "3": "anonymousNbt"}, "default": "void"}]}, {"name": "health", "type": ["switch", {"compareTo": "action", "fields": {"0": "f32", "2": "f32"}, "default": "void"}]}, {"name": "color", "type": ["switch", {"compareTo": "action", "fields": {"0": "varint", "4": "varint"}, "default": "void"}]}, {"name": "dividers", "type": ["switch", {"compareTo": "action", "fields": {"0": "varint", "4": "varint"}, "default": "void"}]}, {"name": "flags", "type": ["switch", {"compareTo": "action", "fields": {"0": "u8", "5": "u8"}, "default": "void"}]}]], "packet_difficulty": ["container", [{"name": "difficulty", "type": "u8"}, {"name": "difficultyLocked", "type": "bool"}]], "packet_chunk_batch_finished": ["container", [{"name": "batchSize", "type": "varint"}]], "packet_chunk_batch_start": ["container", []], "packet_chunk_biomes": ["container", [{"name": "biomes", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "position", "type": "packedChunkPos"}, {"name": "data", "type": "ByteArray"}]]}]}]], "packet_clear_titles": ["container", [{"name": "reset", "type": "bool"}]], "packet_tab_complete": ["container", [{"name": "transactionId", "type": "varint"}, {"name": "start", "type": "varint"}, {"name": "length", "type": "varint"}, {"name": "matches", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "match", "type": "string"}, {"name": "tooltip", "type": ["option", "anonymousNbt"]}]]}]}]], "packet_declare_commands": ["container", [{"name": "nodes", "type": ["array", {"countType": "varint", "type": "command_node"}]}, {"name": "rootIndex", "type": "varint"}]], "packet_close_window": ["container", [{"name": "windowId", "type": "ContainerID"}]], "packet_window_items": ["container", [{"name": "windowId", "type": "ContainerID"}, {"name": "stateId", "type": "varint"}, {"name": "items", "type": ["array", {"countType": "varint", "type": "Slot"}]}, {"name": "carriedItem", "type": "Slot"}]], "packet_craft_progress_bar": ["container", [{"name": "windowId", "type": "ContainerID"}, {"name": "property", "type": "i16"}, {"name": "value", "type": "i16"}]], "packet_set_slot": ["container", [{"name": "windowId", "type": "ContainerID"}, {"name": "stateId", "type": "varint"}, {"name": "slot", "type": "i16"}, {"name": "item", "type": "Slot"}]], "packet_set_cooldown": ["container", [{"name": "cooldownGroup", "type": "string"}, {"name": "cooldownTicks", "type": "varint"}]], "packet_chat_suggestions": ["container", [{"name": "action", "type": "varint"}, {"name": "entries", "type": ["array", {"countType": "varint", "type": "string"}]}]], "packet_custom_payload": ["container", [{"name": "channel", "type": "string"}, {"name": "data", "type": "restBuffer"}]], "packet_damage_event": ["container", [{"name": "entityId", "type": "varint"}, {"name": "sourceTypeId", "type": "varint"}, {"name": "sourceCauseId", "type": "varint"}, {"name": "sourceDirectId", "type": "varint"}, {"name": "sourcePosition", "type": ["option", "vec3f64"]}]], "packet_debug_sample": ["container", [{"name": "sample", "type": ["array", {"countType": "varint", "type": "i64"}]}, {"name": "type", "type": "varint"}]], "packet_hide_message": ["container", [{"name": "id", "type": "varint"}, {"name": "signature", "type": ["switch", {"compareTo": "id", "fields": {"0": ["buffer", {"count": 256}]}, "default": "void"}]}]], "packet_kick_disconnect": ["container", [{"name": "reason", "type": "anonymousNbt"}]], "ChatTypeParameterType": ["mapper", {"type": "varint", "mappings": {"0": "content", "1": "sender", "2": "target"}}], "ChatType": ["container", [{"name": "<PERSON><PERSON><PERSON>", "type": "string"}, {"name": "parameters", "type": ["array", {"countType": "varint", "type": "ChatTypeParameterType"}]}, {"name": "style", "type": "anonymousNbt"}]], "ChatTypes": ["container", [{"name": "registryIndex", "type": "varint"}, {"anon": true, "type": ["switch", {"compareTo": "registryIndex", "fields": {"0": ["container", [{"name": "chat", "type": "ChatType"}, {"name": "narration", "type": "ChatType"}]]}, "default": "void"}]}]], "packet_profileless_chat": ["container", [{"name": "message", "type": "anonymousNbt"}, {"name": "type", "type": "ChatTypes"}, {"name": "name", "type": "anonymousNbt"}, {"name": "target", "type": ["option", "anonymousNbt"]}]], "packet_entity_status": ["container", [{"name": "entityId", "type": "i32"}, {"name": "entityStatus", "type": "i8"}]], "packet_sync_entity_position": ["container", [{"name": "entityId", "type": "varint"}, {"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "dx", "type": "f64"}, {"name": "dy", "type": "f64"}, {"name": "dz", "type": "f64"}, {"name": "yaw", "type": "f32"}, {"name": "pitch", "type": "f32"}, {"name": "onGround", "type": "bool"}]], "packet_explosion": ["container", [{"name": "center", "type": "vec3f"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": ["option", "vec3f"]}, {"name": "explosionParticle", "type": "Particle"}, {"name": "soundId", "type": "varint"}, {"anon": true, "type": ["switch", {"compareTo": "soundId", "fields": {"0": ["container", [{"name": "soundName", "type": "string"}, {"name": "range", "type": ["option", "f32"]}]]}, "default": ["container", []]}]}]], "packet_unload_chunk": ["container", [{"name": "chunkZ", "type": "i32"}, {"name": "chunkX", "type": "i32"}]], "packet_game_state_change": ["container", [{"name": "reason", "type": "u8"}, {"name": "gameMode", "type": "f32"}]], "packet_open_horse_window": ["container", [{"name": "windowId", "type": "ContainerID"}, {"name": "nbSlots", "type": "varint"}, {"name": "entityId", "type": "i32"}]], "packet_hurt_animation": ["container", [{"name": "entityId", "type": "varint"}, {"name": "yaw", "type": "f32"}]], "packet_initialize_world_border": ["container", [{"name": "x", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "oldDiam<PERSON>", "type": "f64"}, {"name": "newDiameter", "type": "f64"}, {"name": "speed", "type": "varint"}, {"name": "portalTeleportBoundary", "type": "varint"}, {"name": "warningBlocks", "type": "varint"}, {"name": "warningTime", "type": "varint"}]], "packet_keep_alive": ["container", [{"name": "keepAliveId", "type": "i64"}]], "packet_map_chunk": ["container", [{"name": "x", "type": "i32"}, {"name": "z", "type": "i32"}, {"name": "heightmaps", "type": "anonymousNbt"}, {"name": "chunkData", "type": ["buffer", {"countType": "varint"}]}, {"name": "blockEntities", "type": ["array", {"countType": "varint", "type": "chunkBlockEntity"}]}, {"name": "skyLightMask", "type": ["array", {"countType": "varint", "type": "i64"}]}, {"name": "blockLightMask", "type": ["array", {"countType": "varint", "type": "i64"}]}, {"name": "emptySkyLightMask", "type": ["array", {"countType": "varint", "type": "i64"}]}, {"name": "emptyBlockLightMask", "type": ["array", {"countType": "varint", "type": "i64"}]}, {"name": "skyLight", "type": ["array", {"countType": "varint", "type": ["array", {"countType": "varint", "type": "u8"}]}]}, {"name": "blockLight", "type": ["array", {"countType": "varint", "type": ["array", {"countType": "varint", "type": "u8"}]}]}]], "packet_world_event": ["container", [{"name": "effectId", "type": "i32"}, {"name": "location", "type": "position"}, {"name": "data", "type": "i32"}, {"name": "global", "type": "bool"}]], "packet_world_particles": ["container", [{"name": "longDistance", "type": "bool"}, {"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "offsetX", "type": "f32"}, {"name": "offsetY", "type": "f32"}, {"name": "offsetZ", "type": "f32"}, {"name": "velocityOffset", "type": "f32"}, {"name": "amount", "type": "i32"}, {"name": "particle", "type": "Particle"}]], "packet_update_light": ["container", [{"name": "chunkX", "type": "varint"}, {"name": "chunkZ", "type": "varint"}, {"name": "skyLightMask", "type": ["array", {"countType": "varint", "type": "i64"}]}, {"name": "blockLightMask", "type": ["array", {"countType": "varint", "type": "i64"}]}, {"name": "emptySkyLightMask", "type": ["array", {"countType": "varint", "type": "i64"}]}, {"name": "emptyBlockLightMask", "type": ["array", {"countType": "varint", "type": "i64"}]}, {"name": "skyLight", "type": ["array", {"countType": "varint", "type": ["array", {"countType": "varint", "type": "u8"}]}]}, {"name": "blockLight", "type": ["array", {"countType": "varint", "type": ["array", {"countType": "varint", "type": "u8"}]}]}]], "packet_login": ["container", [{"name": "entityId", "type": "i32"}, {"name": "isHardcore", "type": "bool"}, {"name": "worldNames", "type": ["array", {"countType": "varint", "type": "string"}]}, {"name": "maxPlayers", "type": "varint"}, {"name": "viewDistance", "type": "varint"}, {"name": "simulationDistance", "type": "varint"}, {"name": "reducedDebugInfo", "type": "bool"}, {"name": "enableRespawnScreen", "type": "bool"}, {"name": "doLimitedCrafting", "type": "bool"}, {"name": "worldState", "type": "SpawnInfo"}, {"name": "enforcesSecureChat", "type": "bool"}]], "packet_map": ["container", [{"name": "itemDamage", "type": "varint"}, {"name": "scale", "type": "i8"}, {"name": "locked", "type": "bool"}, {"name": "icons", "type": ["option", ["array", {"countType": "varint", "type": ["container", [{"name": "type", "type": "varint"}, {"name": "x", "type": "i8"}, {"name": "z", "type": "i8"}, {"name": "direction", "type": "u8"}, {"name": "displayName", "type": ["option", "anonymousNbt"]}]]}]]}, {"name": "columns", "type": "u8"}, {"name": "rows", "type": ["switch", {"compareTo": "columns", "fields": {"0": "void"}, "default": "u8"}]}, {"name": "x", "type": ["switch", {"compareTo": "columns", "fields": {"0": "void"}, "default": "u8"}]}, {"name": "y", "type": ["switch", {"compareTo": "columns", "fields": {"0": "void"}, "default": "u8"}]}, {"name": "data", "type": ["switch", {"compareTo": "columns", "fields": {"0": "void"}, "default": ["buffer", {"countType": "varint"}]}]}]], "packet_trade_list": ["container", [{"name": "windowId", "type": "ContainerID"}, {"name": "trades", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "inputItem1", "type": ["container", [{"name": "itemId", "type": "varint"}, {"name": "itemCount", "type": "varint"}, {"name": "addedComponentCount", "type": "varint"}, {"name": "components", "type": ["array", {"count": "addedComponentCount", "type": "SlotComponent"}]}]]}, {"name": "outputItem", "type": "Slot"}, {"name": "inputItem2", "type": ["option", ["container", [{"name": "itemId", "type": "varint"}, {"name": "itemCount", "type": "varint"}, {"name": "addedComponentCount", "type": "varint"}, {"name": "components", "type": ["array", {"count": "addedComponentCount", "type": "SlotComponent"}]}]]]}, {"name": "tradeDisabled", "type": "bool"}, {"name": "nbTradeUses", "type": "i32"}, {"name": "maximumNbTradeUses", "type": "i32"}, {"name": "xp", "type": "i32"}, {"name": "specialPrice", "type": "i32"}, {"name": "priceMultiplier", "type": "f32"}, {"name": "demand", "type": "i32"}]]}]}, {"name": "villagerLevel", "type": "varint"}, {"name": "experience", "type": "varint"}, {"name": "isRegularVillager", "type": "bool"}, {"name": "canRestock", "type": "bool"}]], "packet_rel_entity_move": ["container", [{"name": "entityId", "type": "varint"}, {"name": "dX", "type": "i16"}, {"name": "dY", "type": "i16"}, {"name": "dZ", "type": "i16"}, {"name": "onGround", "type": "bool"}]], "packet_entity_move_look": ["container", [{"name": "entityId", "type": "varint"}, {"name": "dX", "type": "i16"}, {"name": "dY", "type": "i16"}, {"name": "dZ", "type": "i16"}, {"name": "yaw", "type": "i8"}, {"name": "pitch", "type": "i8"}, {"name": "onGround", "type": "bool"}]], "packet_move_minecart": ["container", [{"name": "entityId", "type": "varint"}, {"name": "steps", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "position", "type": "vec3f"}, {"name": "movement", "type": "vec3f"}, {"name": "yaw", "type": "f32"}, {"name": "pitch", "type": "f32"}, {"name": "weight", "type": "f32"}]]}]}]], "packet_entity_look": ["container", [{"name": "entityId", "type": "varint"}, {"name": "yaw", "type": "i8"}, {"name": "pitch", "type": "i8"}, {"name": "onGround", "type": "bool"}]], "packet_vehicle_move": ["container", [{"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "yaw", "type": "f32"}, {"name": "pitch", "type": "f32"}]], "packet_open_book": ["container", [{"name": "hand", "type": "varint"}]], "packet_open_window": ["container", [{"name": "windowId", "type": "varint"}, {"name": "inventoryType", "type": "varint"}, {"name": "windowTitle", "type": "anonymousNbt"}]], "packet_open_sign_entity": ["container", [{"name": "location", "type": "position"}, {"name": "isFrontText", "type": "bool"}]], "packet_ping": ["container", [{"name": "id", "type": "i32"}]], "packet_ping_response": ["container", [{"name": "id", "type": "i64"}]], "packet_craft_recipe_response": ["container", [{"name": "windowId", "type": "ContainerID"}, {"name": "recipeDisplay", "type": "RecipeDisplay"}]], "packet_abilities": ["container", [{"name": "flags", "type": "i8"}, {"name": "flyingSpeed", "type": "f32"}, {"name": "walkingSpeed", "type": "f32"}]], "packet_player_chat": ["container", [{"name": "senderUuid", "type": "UUID"}, {"name": "index", "type": "varint"}, {"name": "signature", "type": ["option", ["buffer", {"count": 256}]]}, {"name": "plainMessage", "type": "string"}, {"name": "timestamp", "type": "i64"}, {"name": "salt", "type": "i64"}, {"name": "previousMessages", "type": "previousMessages"}, {"name": "unsignedChatContent", "type": ["option", "anonymousNbt"]}, {"name": "filterType", "type": "varint"}, {"name": "filterTypeMask", "type": ["switch", {"compareTo": "filterType", "fields": {"2": ["array", {"countType": "varint", "type": "i64"}]}, "default": "void"}]}, {"name": "type", "type": "ChatTypes"}, {"name": "networkName", "type": "anonymousNbt"}, {"name": "networkTargetName", "type": ["option", "anonymousNbt"]}]], "packet_end_combat_event": ["container", [{"name": "duration", "type": "varint"}]], "packet_enter_combat_event": ["container", []], "packet_death_combat_event": ["container", [{"name": "playerId", "type": "varint"}, {"name": "message", "type": "anonymousNbt"}]], "packet_player_remove": ["container", [{"name": "players", "type": ["array", {"countType": "varint", "type": "UUID"}]}]], "packet_player_info": ["container", [{"name": "action", "type": ["bitfield", [{"name": "unused", "size": 1, "signed": false}, {"name": "update_priority", "size": 1, "signed": false}, {"name": "update_display_name", "size": 1, "signed": false}, {"name": "update_latency", "size": 1, "signed": false}, {"name": "update_listed", "size": 1, "signed": false}, {"name": "update_game_mode", "size": 1, "signed": false}, {"name": "initialize_chat", "size": 1, "signed": false}, {"name": "add_player", "size": 1, "signed": false}]]}, {"name": "data", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "uuid", "type": "UUID"}, {"name": "player", "type": ["switch", {"compareTo": "../action/add_player", "fields": {"1": "game_profile"}, "default": "void"}]}, {"name": "chatSession", "type": ["switch", {"compareTo": "../action/initialize_chat", "fields": {"1": "chat_session"}, "default": "void"}]}, {"name": "gamemode", "type": ["switch", {"compareTo": "../action/update_game_mode", "fields": {"1": "varint"}, "default": "void"}]}, {"name": "listed", "type": ["switch", {"compareTo": "../action/update_listed", "fields": {"1": "varint"}, "default": "void"}]}, {"name": "latency", "type": ["switch", {"compareTo": "../action/update_latency", "fields": {"1": "varint"}, "default": "void"}]}, {"name": "displayName", "type": ["switch", {"compareTo": "../action/update_display_name", "fields": {"1": ["option", "anonymousNbt"]}, "default": "void"}]}, {"name": "listPriority", "type": ["switch", {"compareTo": "../action/update_priority", "fields": {"1": "varint"}, "default": "void"}]}]]}]}]], "packet_face_player": ["container", [{"name": "feet_eyes", "type": "varint"}, {"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "isEntity", "type": "bool"}, {"name": "entityId", "type": ["switch", {"compareTo": "isEntity", "fields": {"true": "varint"}, "default": "void"}]}, {"name": "entity_feet_eyes", "type": ["switch", {"compareTo": "isEntity", "fields": {"true": "varint"}, "default": "void"}]}]], "PositionUpdateRelatives": ["bitflags", {"type": "u32", "flags": ["x", "y", "z", "yaw", "pitch", "dx", "dy", "dz", "yaw<PERSON><PERSON><PERSON>"]}], "packet_position": ["container", [{"name": "teleportId", "type": "varint"}, {"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "dx", "type": "f64"}, {"name": "dy", "type": "f64"}, {"name": "dz", "type": "f64"}, {"name": "yaw", "type": "f32"}, {"name": "pitch", "type": "f32"}, {"name": "flags", "type": "PositionUpdateRelatives"}]], "packet_player_rotation": ["container", [{"name": "yaw", "type": "f32"}, {"name": "pitch", "type": "f32"}]], "packet_recipe_book_add": ["container", [{"name": "entries", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "recipe", "type": ["container", [{"name": "displayId", "type": "varint"}, {"name": "display", "type": "RecipeDisplay"}, {"name": "group", "type": "optvarint"}, {"name": "category", "type": ["mapper", {"type": "varint", "mappings": {"0": "crafting_building_blocks", "1": "crafting_redstone", "2": "crafting_equipment", "3": "crafting_misc", "4": "furnace_food", "5": "furnace_blocks", "6": "furnace_misc", "7": "blast_furnace_blocks", "8": "blast_furnace_misc", "9": "smoker_food", "10": "stonecutter", "11": "smithing", "12": "campfire"}}]}, {"name": "craftingRequirements", "type": ["option", ["array", {"countType": "varint", "type": "IDSet"}]]}]]}, {"name": "flags", "type": ["bitflags", {"type": "u8", "flags": ["notification", "highlight"]}]}]]}]}, {"name": "replace", "type": "bool"}]], "packet_recipe_book_remove": ["container", [{"name": "recipeIds", "type": ["array", {"countType": "varint", "type": "varint"}]}]], "packet_recipe_book_settings": ["container", [{"name": "craftingGuiOpen", "type": "bool"}, {"name": "craftingFilteringCraftable", "type": "bool"}, {"name": "smeltingGuiOpen", "type": "bool"}, {"name": "smeltingFilteringCraftable", "type": "bool"}, {"name": "blastGuiOpen", "type": "bool"}, {"name": "blastFilteringCraftable", "type": "bool"}, {"name": "smoker<PERSON>ui<PERSON>pen", "type": "bool"}, {"name": "smokerFilteringCraftable", "type": "bool"}]], "packet_entity_destroy": ["container", [{"name": "entityIds", "type": ["array", {"countType": "varint", "type": "varint"}]}]], "packet_remove_entity_effect": ["container", [{"name": "entityId", "type": "varint"}, {"name": "effectId", "type": "varint"}]], "packet_reset_score": ["container", [{"name": "entity_name", "type": "string"}, {"name": "objective_name", "type": ["option", "string"]}]], "packet_respawn": ["container", [{"name": "worldState", "type": "SpawnInfo"}, {"name": "copyMetadata", "type": "u8"}]], "packet_entity_head_rotation": ["container", [{"name": "entityId", "type": "varint"}, {"name": "headYaw", "type": "i8"}]], "packet_multi_block_change": ["container", [{"name": "chunkCoordinates", "type": ["bitfield", [{"name": "x", "size": 22, "signed": true}, {"name": "z", "size": 22, "signed": true}, {"name": "y", "size": 20, "signed": true}]]}, {"name": "records", "type": ["array", {"countType": "varint", "type": "varint"}]}]], "packet_select_advancement_tab": ["container", [{"name": "id", "type": ["option", "string"]}]], "packet_server_data": ["container", [{"name": "motd", "type": "anonymousNbt"}, {"name": "iconBytes", "type": ["option", "ByteArray"]}]], "packet_action_bar": ["container", [{"name": "text", "type": "anonymousNbt"}]], "packet_world_border_center": ["container", [{"name": "x", "type": "f64"}, {"name": "z", "type": "f64"}]], "packet_world_border_lerp_size": ["container", [{"name": "oldDiam<PERSON>", "type": "f64"}, {"name": "newDiameter", "type": "f64"}, {"name": "speed", "type": "varint"}]], "packet_world_border_size": ["container", [{"name": "diameter", "type": "f64"}]], "packet_world_border_warning_delay": ["container", [{"name": "warningTime", "type": "varint"}]], "packet_world_border_warning_reach": ["container", [{"name": "warningBlocks", "type": "varint"}]], "packet_camera": ["container", [{"name": "cameraId", "type": "varint"}]], "packet_update_view_position": ["container", [{"name": "chunkX", "type": "varint"}, {"name": "chunkZ", "type": "varint"}]], "packet_update_view_distance": ["container", [{"name": "viewDistance", "type": "varint"}]], "packet_set_cursor_item": ["container", [{"name": "contents", "type": ["option", "Slot"]}]], "packet_spawn_position": ["container", [{"name": "location", "type": "position"}, {"name": "angle", "type": "f32"}]], "packet_scoreboard_display_objective": ["container", [{"name": "position", "type": "varint"}, {"name": "name", "type": "string"}]], "packet_entity_metadata": ["container", [{"name": "entityId", "type": "varint"}, {"name": "metadata", "type": "entityMetadata"}]], "packet_attach_entity": ["container", [{"name": "entityId", "type": "i32"}, {"name": "vehicleId", "type": "i32"}]], "packet_entity_velocity": ["container", [{"name": "entityId", "type": "varint"}, {"name": "velocityX", "type": "i16"}, {"name": "velocityY", "type": "i16"}, {"name": "velocityZ", "type": "i16"}]], "packet_entity_equipment": ["container", [{"name": "entityId", "type": "varint"}, {"name": "equipments", "type": ["topBitSetTerminatedArray", {"type": ["container", [{"name": "slot", "type": "i8"}, {"name": "item", "type": "Slot"}]]}]}]], "packet_experience": ["container", [{"name": "experienceBar", "type": "f32"}, {"name": "level", "type": "varint"}, {"name": "totalExperience", "type": "varint"}]], "packet_update_health": ["container", [{"name": "health", "type": "f32"}, {"name": "food", "type": "varint"}, {"name": "foodSaturation", "type": "f32"}]], "packet_held_item_slot": ["container", [{"name": "slot", "type": "i8"}]], "packet_scoreboard_objective": ["container", [{"name": "name", "type": "string"}, {"name": "action", "type": "i8"}, {"name": "displayText", "type": ["switch", {"compareTo": "action", "fields": {"0": "anonymousNbt", "2": "anonymousNbt"}, "default": "void"}]}, {"name": "type", "type": ["switch", {"compareTo": "action", "fields": {"0": "varint", "2": "varint"}, "default": "void"}]}, {"name": "number_format", "type": ["switch", {"compareTo": "action", "fields": {"0": ["option", "varint"], "2": ["option", "varint"]}, "default": "void"}]}, {"name": "styling", "type": ["switch", {"compareTo": "action", "fields": {"0": ["switch", {"compareTo": "number_format", "fields": {"1": "anonymousNbt", "2": "anonymousNbt"}, "default": "void"}], "2": ["switch", {"compareTo": "number_format", "fields": {"1": "anonymousNbt", "2": "anonymousNbt"}, "default": "void"}]}, "default": "void"}]}]], "packet_set_passengers": ["container", [{"name": "entityId", "type": "varint"}, {"name": "passengers", "type": ["array", {"countType": "varint", "type": "varint"}]}]], "packet_set_player_inventory": ["container", [{"name": "slotId", "type": "varint"}, {"name": "contents", "type": ["option", "Slot"]}]], "packet_teams": ["container", [{"name": "team", "type": "string"}, {"name": "mode", "type": "i8"}, {"name": "name", "type": ["switch", {"compareTo": "mode", "fields": {"0": "anonymousNbt", "2": "anonymousNbt"}, "default": "void"}]}, {"name": "friendlyFire", "type": ["switch", {"compareTo": "mode", "fields": {"0": "i8", "2": "i8"}, "default": "void"}]}, {"name": "nameTagVisibility", "type": ["switch", {"compareTo": "mode", "fields": {"0": "string", "2": "string"}, "default": "void"}]}, {"name": "collisionRule", "type": ["switch", {"compareTo": "mode", "fields": {"0": "string", "2": "string"}, "default": "void"}]}, {"name": "formatting", "type": ["switch", {"compareTo": "mode", "fields": {"0": "varint", "2": "varint"}, "default": "void"}]}, {"name": "prefix", "type": ["switch", {"compareTo": "mode", "fields": {"0": "anonymousNbt", "2": "anonymousNbt"}, "default": "void"}]}, {"name": "suffix", "type": ["switch", {"compareTo": "mode", "fields": {"0": "anonymousNbt", "2": "anonymousNbt"}, "default": "void"}]}, {"name": "players", "type": ["switch", {"compareTo": "mode", "fields": {"0": ["array", {"countType": "varint", "type": "string"}], "3": ["array", {"countType": "varint", "type": "string"}], "4": ["array", {"countType": "varint", "type": "string"}]}, "default": "void"}]}]], "packet_scoreboard_score": ["container", [{"name": "itemName", "type": "string"}, {"name": "scoreName", "type": "string"}, {"name": "value", "type": "varint"}, {"name": "display_name", "type": ["option", "anonymousNbt"]}, {"name": "number_format", "type": ["option", "varint"]}, {"name": "styling", "type": ["switch", {"compareTo": "number_format", "fields": {"1": "anonymousNbt", "2": "anonymousNbt"}, "default": "void"}]}]], "packet_simulation_distance": ["container", [{"name": "distance", "type": "varint"}]], "packet_set_title_subtitle": ["container", [{"name": "text", "type": "anonymousNbt"}]], "packet_update_time": ["container", [{"name": "age", "type": "i64"}, {"name": "time", "type": "i64"}, {"name": "tickDayTime", "type": "bool"}]], "packet_set_title_text": ["container", [{"name": "text", "type": "anonymousNbt"}]], "packet_set_title_time": ["container", [{"name": "fadeIn", "type": "i32"}, {"name": "stay", "type": "i32"}, {"name": "fadeOut", "type": "i32"}]], "packet_entity_sound_effect": ["container", [{"name": "soundId", "type": "varint"}, {"name": "soundEvent", "type": ["switch", {"compareTo": "soundId", "fields": {"0": ["container", [{"name": "resource", "type": "string"}, {"name": "range", "type": ["option", "f32"]}]]}, "default": "void"}]}, {"name": "soundCategory", "type": "soundSource"}, {"name": "entityId", "type": "varint"}, {"name": "volume", "type": "f32"}, {"name": "pitch", "type": "f32"}, {"name": "seed", "type": "i64"}]], "packet_sound_effect": ["container", [{"name": "soundId", "type": "varint"}, {"name": "soundEvent", "type": ["switch", {"compareTo": "soundId", "fields": {"0": ["container", [{"name": "resource", "type": "string"}, {"name": "range", "type": ["option", "f32"]}]]}, "default": "void"}]}, {"name": "soundCategory", "type": "soundSource"}, {"name": "x", "type": "i32"}, {"name": "y", "type": "i32"}, {"name": "z", "type": "i32"}, {"name": "volume", "type": "f32"}, {"name": "pitch", "type": "f32"}, {"name": "seed", "type": "i64"}]], "packet_start_configuration": ["container", []], "packet_stop_sound": ["container", [{"name": "flags", "type": "i8"}, {"name": "source", "type": ["switch", {"compareTo": "flags", "fields": {"1": "varint", "3": "varint"}, "default": "void"}]}, {"name": "sound", "type": ["switch", {"compareTo": "flags", "fields": {"2": "string", "3": "string"}, "default": "void"}]}]], "packet_system_chat": ["container", [{"name": "content", "type": "anonymousNbt"}, {"name": "isActionBar", "type": "bool"}]], "packet_playerlist_header": ["container", [{"name": "header", "type": "anonymousNbt"}, {"name": "footer", "type": "anonymousNbt"}]], "packet_nbt_query_response": ["container", [{"name": "transactionId", "type": "varint"}, {"name": "nbt", "type": "anonOptionalNbt"}]], "packet_collect": ["container", [{"name": "collectedEntityId", "type": "varint"}, {"name": "collectorEntityId", "type": "varint"}, {"name": "pickupItemCount", "type": "varint"}]], "packet_entity_teleport": ["container", [{"name": "entityId", "type": "varint"}, {"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "yaw", "type": "i8"}, {"name": "pitch", "type": "i8"}, {"name": "onGround", "type": "bool"}]], "packet_set_ticking_state": ["container", [{"name": "tick_rate", "type": "f32"}, {"name": "is_frozen", "type": "bool"}]], "packet_step_tick": ["container", [{"name": "tick_steps", "type": "varint"}]], "packet_advancements": ["container", [{"name": "reset", "type": "bool"}, {"name": "advancementMapping", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "key", "type": "string"}, {"name": "value", "type": ["container", [{"name": "parentId", "type": ["option", "string"]}, {"name": "displayData", "type": ["option", ["container", [{"name": "title", "type": "anonymousNbt"}, {"name": "description", "type": "anonymousNbt"}, {"name": "icon", "type": "Slot"}, {"name": "frameType", "type": "varint"}, {"name": "flags", "type": ["bitfield", [{"name": "unused", "size": 29, "signed": false}, {"name": "hidden", "size": 1, "signed": false}, {"name": "show_toast", "size": 1, "signed": false}, {"name": "has_background_texture", "size": 1, "signed": false}]]}, {"name": "backgroundTexture", "type": ["switch", {"compareTo": "flags/has_background_texture", "fields": {"1": "string"}, "default": "void"}]}, {"name": "xCord", "type": "f32"}, {"name": "yCord", "type": "f32"}]]]}, {"name": "requirements", "type": ["array", {"countType": "varint", "type": ["array", {"countType": "varint", "type": "string"}]}]}, {"name": "sendsTelemtryData", "type": "bool"}]]}]]}]}, {"name": "identifiers", "type": ["array", {"countType": "varint", "type": "string"}]}, {"name": "progressMapping", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "key", "type": "string"}, {"name": "value", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "criterionIdentifier", "type": "string"}, {"name": "criterionProgress", "type": ["option", "i64"]}]]}]}]]}]}]], "packet_entity_update_attributes": ["container", [{"name": "entityId", "type": "varint"}, {"name": "properties", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "key", "type": ["mapper", {"type": "varint", "mappings": {"0": "generic.armor", "1": "generic.armor_toughness", "2": "generic.attack_damage", "3": "generic.attack_knockback", "4": "generic.attack_speed", "5": "player.block_break_speed", "6": "player.block_interaction_range", "7": "player.entity_interaction_range", "8": "generic.fall_damage_multiplier", "9": "generic.flying_speed", "10": "generic.follow_range", "11": "generic.gravity", "12": "generic.jump_strength", "13": "generic.knockback_resistance", "14": "generic.luck", "15": "generic.max_absorption", "16": "generic.max_health", "17": "generic.movement_speed", "18": "generic.safe_fall_distance", "19": "generic.scale", "20": "zombie.spawn_reinforcements", "21": "generic.step_height"}}]}, {"name": "value", "type": "f64"}, {"name": "modifiers", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "uuid", "type": "string"}, {"name": "amount", "type": "f64"}, {"name": "operation", "type": "i8"}]]}]}]]}]}]], "packet_entity_effect": ["container", [{"name": "entityId", "type": "varint"}, {"name": "effectId", "type": "varint"}, {"name": "amplifier", "type": "varint"}, {"name": "duration", "type": "varint"}, {"name": "flags", "type": "u8"}]], "packet_declare_recipes": ["container", [{"name": "recipes", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "name", "type": "string"}, {"name": "items", "type": ["array", {"countType": "varint", "type": "varint"}]}]]}]}, {"name": "stoneCutterRecipes", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "input", "type": "IDSet"}, {"name": "slotDisplay", "type": "SlotDisplay"}]]}]}]], "packet_tags": ["container", [{"name": "tags", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "tagType", "type": "string"}, {"name": "tags", "type": "tags"}]]}]}]], "packet_set_projectile_power": ["container", [{"name": "id", "type": "varint"}, {"name": "accelerationPower", "type": "f64"}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "bundle_delimiter", "0x01": "spawn_entity", "0x02": "spawn_entity_experience_orb", "0x03": "animation", "0x04": "statistics", "0x05": "acknowledge_player_digging", "0x06": "block_break_animation", "0x07": "tile_entity_data", "0x08": "block_action", "0x09": "block_change", "0x0a": "boss_bar", "0x0b": "difficulty", "0x0c": "chunk_batch_finished", "0x0d": "chunk_batch_start", "0x0e": "chunk_biomes", "0x0f": "clear_titles", "0x10": "tab_complete", "0x11": "declare_commands", "0x12": "close_window", "0x13": "window_items", "0x14": "craft_progress_bar", "0x15": "set_slot", "0x16": "cookie_request", "0x17": "set_cooldown", "0x18": "chat_suggestions", "0x19": "custom_payload", "0x1a": "damage_event", "0x1b": "debug_sample", "0x1c": "hide_message", "0x1d": "kick_disconnect", "0x1e": "profileless_chat", "0x1f": "entity_status", "0x20": "sync_entity_position", "0x21": "explosion", "0x22": "unload_chunk", "0x23": "game_state_change", "0x24": "open_horse_window", "0x25": "hurt_animation", "0x26": "initialize_world_border", "0x27": "keep_alive", "0x28": "map_chunk", "0x29": "world_event", "0x2a": "world_particles", "0x2b": "update_light", "0x2c": "login", "0x2d": "map", "0x2e": "trade_list", "0x2f": "rel_entity_move", "0x30": "entity_move_look", "0x31": "move_minecart", "0x32": "entity_look", "0x33": "vehicle_move", "0x34": "open_book", "0x35": "open_window", "0x36": "open_sign_entity", "0x37": "ping", "0x38": "ping_response", "0x39": "craft_recipe_response", "0x3a": "abilities", "0x3b": "player_chat", "0x3c": "end_combat_event", "0x3d": "enter_combat_event", "0x3e": "death_combat_event", "0x3f": "player_remove", "0x40": "player_info", "0x41": "face_player", "0x42": "position", "0x43": "player_rotation", "0x44": "recipe_book_add", "0x45": "recipe_book_remove", "0x46": "recipe_book_settings", "0x47": "entity_destroy", "0x48": "remove_entity_effect", "0x49": "reset_score", "0x4a": "remove_resource_pack", "0x4b": "add_resource_pack", "0x4c": "respawn", "0x4d": "entity_head_rotation", "0x4e": "multi_block_change", "0x4f": "select_advancement_tab", "0x50": "server_data", "0x51": "action_bar", "0x52": "world_border_center", "0x53": "world_border_lerp_size", "0x54": "world_border_size", "0x55": "world_border_warning_delay", "0x56": "world_border_warning_reach", "0x57": "camera", "0x58": "update_view_position", "0x59": "update_view_distance", "0x5a": "set_cursor_item", "0x5b": "spawn_position", "0x5c": "scoreboard_display_objective", "0x5d": "entity_metadata", "0x5e": "attach_entity", "0x5f": "entity_velocity", "0x60": "entity_equipment", "0x61": "experience", "0x62": "update_health", "0x63": "held_item_slot", "0x64": "scoreboard_objective", "0x65": "set_passengers", "0x66": "set_player_inventory", "0x67": "teams", "0x68": "scoreboard_score", "0x69": "simulation_distance", "0x6a": "set_title_subtitle", "0x6b": "update_time", "0x6c": "set_title_text", "0x6d": "set_title_time", "0x6e": "entity_sound_effect", "0x6f": "sound_effect", "0x70": "start_configuration", "0x71": "stop_sound", "0x72": "store_cookie", "0x73": "system_chat", "0x74": "playerlist_header", "0x75": "nbt_query_response", "0x76": "collect", "0x77": "entity_teleport", "0x78": "set_ticking_state", "0x79": "step_tick", "0x7a": "transfer", "0x7b": "advancements", "0x7c": "entity_update_attributes", "0x7d": "entity_effect", "0x7e": "declare_recipes", "0x7f": "tags", "0x80": "set_projectile_power", "0x81": "custom_report_details", "0x82": "server_links"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"bundle_delimiter": "void", "spawn_entity": "packet_spawn_entity", "spawn_entity_experience_orb": "packet_spawn_entity_experience_orb", "animation": "packet_animation", "statistics": "packet_statistics", "acknowledge_player_digging": "packet_acknowledge_player_digging", "block_break_animation": "packet_block_break_animation", "tile_entity_data": "packet_tile_entity_data", "block_action": "packet_block_action", "block_change": "packet_block_change", "boss_bar": "packet_boss_bar", "difficulty": "packet_difficulty", "chunk_batch_finished": "packet_chunk_batch_finished", "chunk_batch_start": "packet_chunk_batch_start", "chunk_biomes": "packet_chunk_biomes", "clear_titles": "packet_clear_titles", "tab_complete": "packet_tab_complete", "declare_commands": "packet_declare_commands", "close_window": "packet_close_window", "window_items": "packet_window_items", "craft_progress_bar": "packet_craft_progress_bar", "set_slot": "packet_set_slot", "cookie_request": "packet_common_cookie_request", "set_cooldown": "packet_set_cooldown", "chat_suggestions": "packet_chat_suggestions", "custom_payload": "packet_custom_payload", "damage_event": "packet_damage_event", "debug_sample": "packet_debug_sample", "hide_message": "packet_hide_message", "kick_disconnect": "packet_kick_disconnect", "profileless_chat": "packet_profileless_chat", "entity_status": "packet_entity_status", "sync_entity_position": "packet_sync_entity_position", "explosion": "packet_explosion", "unload_chunk": "packet_unload_chunk", "game_state_change": "packet_game_state_change", "open_horse_window": "packet_open_horse_window", "hurt_animation": "packet_hurt_animation", "initialize_world_border": "packet_initialize_world_border", "keep_alive": "packet_keep_alive", "map_chunk": "packet_map_chunk", "world_event": "packet_world_event", "world_particles": "packet_world_particles", "update_light": "packet_update_light", "login": "packet_login", "map": "packet_map", "trade_list": "packet_trade_list", "rel_entity_move": "packet_rel_entity_move", "entity_move_look": "packet_entity_move_look", "move_minecart": "packet_move_minecart", "entity_look": "packet_entity_look", "vehicle_move": "packet_vehicle_move", "open_book": "packet_open_book", "open_window": "packet_open_window", "open_sign_entity": "packet_open_sign_entity", "ping": "packet_ping", "ping_response": "packet_ping_response", "craft_recipe_response": "packet_craft_recipe_response", "abilities": "packet_abilities", "player_chat": "packet_player_chat", "end_combat_event": "packet_end_combat_event", "enter_combat_event": "packet_enter_combat_event", "death_combat_event": "packet_death_combat_event", "player_remove": "packet_player_remove", "player_info": "packet_player_info", "face_player": "packet_face_player", "position": "packet_position", "player_rotation": "packet_player_rotation", "recipe_book_add": "packet_recipe_book_add", "recipe_book_remove": "packet_recipe_book_remove", "recipe_book_settings": "packet_recipe_book_settings", "entity_destroy": "packet_entity_destroy", "remove_entity_effect": "packet_remove_entity_effect", "reset_score": "packet_reset_score", "remove_resource_pack": "packet_common_remove_resource_pack", "add_resource_pack": "packet_common_add_resource_pack", "respawn": "packet_respawn", "entity_head_rotation": "packet_entity_head_rotation", "multi_block_change": "packet_multi_block_change", "select_advancement_tab": "packet_select_advancement_tab", "server_data": "packet_server_data", "action_bar": "packet_action_bar", "world_border_center": "packet_world_border_center", "world_border_lerp_size": "packet_world_border_lerp_size", "world_border_size": "packet_world_border_size", "world_border_warning_delay": "packet_world_border_warning_delay", "world_border_warning_reach": "packet_world_border_warning_reach", "camera": "packet_camera", "update_view_position": "packet_update_view_position", "update_view_distance": "packet_update_view_distance", "set_cursor_item": "packet_set_cursor_item", "held_item_slot": "packet_held_item_slot", "spawn_position": "packet_spawn_position", "scoreboard_display_objective": "packet_scoreboard_display_objective", "entity_metadata": "packet_entity_metadata", "attach_entity": "packet_attach_entity", "entity_velocity": "packet_entity_velocity", "entity_equipment": "packet_entity_equipment", "experience": "packet_experience", "update_health": "packet_update_health", "scoreboard_objective": "packet_scoreboard_objective", "set_passengers": "packet_set_passengers", "set_player_inventory": "packet_set_player_inventory", "teams": "packet_teams", "scoreboard_score": "packet_scoreboard_score", "simulation_distance": "packet_simulation_distance", "set_title_subtitle": "packet_set_title_subtitle", "update_time": "packet_update_time", "set_title_text": "packet_set_title_text", "set_title_time": "packet_set_title_time", "entity_sound_effect": "packet_entity_sound_effect", "sound_effect": "packet_sound_effect", "start_configuration": "packet_start_configuration", "stop_sound": "packet_stop_sound", "store_cookie": "packet_common_store_cookie", "system_chat": "packet_system_chat", "playerlist_header": "packet_playerlist_header", "nbt_query_response": "packet_nbt_query_response", "collect": "packet_collect", "entity_teleport": "packet_entity_teleport", "set_ticking_state": "packet_set_ticking_state", "step_tick": "packet_step_tick", "transfer": "packet_common_transfer", "advancements": "packet_advancements", "entity_update_attributes": "packet_entity_update_attributes", "entity_effect": "packet_entity_effect", "declare_recipes": "packet_declare_recipes", "tags": "packet_tags", "set_projectile_power": "packet_set_projectile_power", "custom_report_details": "packet_common_custom_report_details", "server_links": "packet_common_server_links"}}]}]]}}, "toServer": {"types": {"packet_teleport_confirm": ["container", [{"name": "teleportId", "type": "varint"}]], "packet_query_block_nbt": ["container", [{"name": "transactionId", "type": "varint"}, {"name": "location", "type": "position"}]], "packet_select_bundle_item": ["container", [{"name": "slotId", "type": "varint"}, {"name": "selectedItemIndex", "type": "varint"}]], "packet_set_difficulty": ["container", [{"name": "new<PERSON>iff<PERSON><PERSON><PERSON>", "type": "u8"}]], "packet_message_acknowledgement": ["container", [{"name": "count", "type": "varint"}]], "packet_chat_command": ["container", [{"name": "command", "type": "string"}]], "packet_chat_command_signed": ["container", [{"name": "command", "type": "string"}, {"name": "timestamp", "type": "i64"}, {"name": "salt", "type": "i64"}, {"name": "argumentSignatures", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "argumentName", "type": "string"}, {"name": "signature", "type": ["buffer", {"count": 256}]}]]}]}, {"name": "messageCount", "type": "varint"}, {"name": "acknowledged", "type": ["buffer", {"count": 3}]}]], "packet_chat_message": ["container", [{"name": "message", "type": "string"}, {"name": "timestamp", "type": "i64"}, {"name": "salt", "type": "i64"}, {"name": "signature", "type": ["option", ["buffer", {"count": 256}]]}, {"name": "offset", "type": "varint"}, {"name": "acknowledged", "type": ["buffer", {"count": 3}]}]], "packet_chat_session_update": ["container", [{"name": "sessionUUID", "type": "UUID"}, {"name": "expireTime", "type": "i64"}, {"name": "public<PERSON>ey", "type": "ByteArray"}, {"name": "signature", "type": "ByteArray"}]], "packet_chunk_batch_received": ["container", [{"name": "chunksPerTick", "type": "f32"}]], "packet_client_command": ["container", [{"name": "actionId", "type": "varint"}]], "packet_tick_end": ["container", []], "packet_settings": ["container", [{"name": "locale", "type": "string"}, {"name": "viewDistance", "type": "i8"}, {"name": "chatFlags", "type": "varint"}, {"name": "chatColors", "type": "bool"}, {"name": "skinParts", "type": "u8"}, {"name": "mainHand", "type": "varint"}, {"name": "enableTextFiltering", "type": "bool"}, {"name": "enableServerListing", "type": "bool"}, {"name": "particleStatus", "type": ["mapper", {"type": "varint", "mappings": {"0": "all", "1": "decreased", "2": "minimal"}}]}]], "packet_tab_complete": ["container", [{"name": "transactionId", "type": "varint"}, {"name": "text", "type": "string"}]], "packet_configuration_acknowledged": ["container", []], "packet_enchant_item": ["container", [{"name": "windowId", "type": "ContainerID"}, {"name": "enchantment", "type": "i8"}]], "packet_window_click": ["container", [{"name": "windowId", "type": "ContainerID"}, {"name": "stateId", "type": "varint"}, {"name": "slot", "type": "i16"}, {"name": "mouseButton", "type": "i8"}, {"name": "mode", "type": "varint"}, {"name": "changedSlots", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "location", "type": "i16"}, {"name": "item", "type": "Slot"}]]}]}, {"name": "cursorItem", "type": "Slot"}]], "packet_close_window": ["container", [{"name": "windowId", "type": "ContainerID"}]], "packet_set_slot_state": ["container", [{"name": "slot_id", "type": "varint"}, {"name": "window_id", "type": "ContainerID"}, {"name": "state", "type": "bool"}]], "packet_custom_payload": ["container", [{"name": "channel", "type": "string"}, {"name": "data", "type": "restBuffer"}]], "packet_debug_sample_subscription": ["container", [{"name": "type", "type": "varint"}]], "packet_edit_book": ["container", [{"name": "hand", "type": "varint"}, {"name": "pages", "type": ["array", {"countType": "varint", "type": "string"}]}, {"name": "title", "type": ["option", "string"]}]], "packet_query_entity_nbt": ["container", [{"name": "transactionId", "type": "varint"}, {"name": "entityId", "type": "varint"}]], "packet_use_entity": ["container", [{"name": "target", "type": "varint"}, {"name": "mouse", "type": "varint"}, {"name": "x", "type": ["switch", {"compareTo": "mouse", "fields": {"2": "f32"}, "default": "void"}]}, {"name": "y", "type": ["switch", {"compareTo": "mouse", "fields": {"2": "f32"}, "default": "void"}]}, {"name": "z", "type": ["switch", {"compareTo": "mouse", "fields": {"2": "f32"}, "default": "void"}]}, {"name": "hand", "type": ["switch", {"compareTo": "mouse", "fields": {"0": "varint", "2": "varint"}, "default": "void"}]}, {"name": "sneaking", "type": "bool"}]], "packet_generate_structure": ["container", [{"name": "location", "type": "position"}, {"name": "levels", "type": "varint"}, {"name": "keepJigsaws", "type": "bool"}]], "packet_keep_alive": ["container", [{"name": "keepAliveId", "type": "i64"}]], "packet_lock_difficulty": ["container", [{"name": "locked", "type": "bool"}]], "MovementFlags": ["bitflags", {"type": "u8", "flags": ["onGround", "hasHorizontalCollision"]}], "packet_position": ["container", [{"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "flags", "type": "MovementFlags"}]], "packet_position_look": ["container", [{"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "yaw", "type": "f32"}, {"name": "pitch", "type": "f32"}, {"name": "flags", "type": "MovementFlags"}]], "packet_look": ["container", [{"name": "yaw", "type": "f32"}, {"name": "pitch", "type": "f32"}, {"name": "flags", "type": "MovementFlags"}]], "packet_flying": ["container", [{"name": "flags", "type": "MovementFlags"}]], "packet_vehicle_move": ["container", [{"name": "x", "type": "f64"}, {"name": "y", "type": "f64"}, {"name": "z", "type": "f64"}, {"name": "yaw", "type": "f32"}, {"name": "pitch", "type": "f32"}]], "packet_steer_boat": ["container", [{"name": "leftPaddle", "type": "bool"}, {"name": "rightPaddle", "type": "bool"}]], "packet_pick_item": ["container", [{"name": "slot", "type": "varint"}]], "packet_ping_request": ["container", [{"name": "id", "type": "i64"}]], "packet_craft_recipe_request": ["container", [{"name": "windowId", "type": "ContainerID"}, {"name": "recipeId", "type": "varint"}, {"name": "makeAll", "type": "bool"}]], "packet_abilities": ["container", [{"name": "flags", "type": "i8"}]], "packet_block_dig": ["container", [{"name": "status", "type": "varint"}, {"name": "location", "type": "position"}, {"name": "face", "type": "i8"}, {"name": "sequence", "type": "varint"}]], "packet_entity_action": ["container", [{"name": "entityId", "type": "varint"}, {"name": "actionId", "type": "varint"}, {"name": "jumpBoost", "type": "varint"}]], "packet_player_input": ["container", [{"name": "inputs", "type": ["bitflags", {"type": "u8", "flags": ["forward", "backward", "left", "right", "jump", "shift", "sprint"]}]}]], "packet_pong": ["container", [{"name": "id", "type": "i32"}]], "packet_recipe_book": ["container", [{"name": "bookId", "type": "varint"}, {"name": "bookOpen", "type": "bool"}, {"name": "filterActive", "type": "bool"}]], "packet_displayed_recipe": ["container", [{"name": "recipeId", "type": "varint"}]], "packet_name_item": ["container", [{"name": "name", "type": "string"}]], "packet_resource_pack_receive": ["container", [{"name": "uuid", "type": "UUID"}, {"name": "result", "type": "varint"}]], "packet_advancement_tab": ["container", [{"name": "action", "type": "varint"}, {"name": "tabId", "type": ["switch", {"compareTo": "action", "fields": {"0": "string", "1": "void"}}]}]], "packet_select_trade": ["container", [{"name": "slot", "type": "varint"}]], "packet_set_beacon_effect": ["container", [{"name": "primary_effect", "type": ["option", "varint"]}, {"name": "secondary_effect", "type": ["option", "varint"]}]], "packet_held_item_slot": ["container", [{"name": "slotId", "type": "i16"}]], "packet_update_command_block": ["container", [{"name": "location", "type": "position"}, {"name": "command", "type": "string"}, {"name": "mode", "type": "varint"}, {"name": "flags", "type": "u8"}]], "packet_update_command_block_minecart": ["container", [{"name": "entityId", "type": "varint"}, {"name": "command", "type": "string"}, {"name": "track_output", "type": "bool"}]], "packet_set_creative_slot": ["container", [{"name": "slot", "type": "i16"}, {"name": "item", "type": "Slot"}]], "packet_update_jigsaw_block": ["container", [{"name": "location", "type": "position"}, {"name": "name", "type": "string"}, {"name": "target", "type": "string"}, {"name": "pool", "type": "string"}, {"name": "finalState", "type": "string"}, {"name": "jointType", "type": "string"}, {"name": "selection_priority", "type": "varint"}, {"name": "placement_priority", "type": "varint"}]], "packet_update_structure_block": ["container", [{"name": "location", "type": "position"}, {"name": "action", "type": "varint"}, {"name": "mode", "type": "varint"}, {"name": "name", "type": "string"}, {"name": "offset_x", "type": "i8"}, {"name": "offset_y", "type": "i8"}, {"name": "offset_z", "type": "i8"}, {"name": "size_x", "type": "i8"}, {"name": "size_y", "type": "i8"}, {"name": "size_z", "type": "i8"}, {"name": "mirror", "type": "varint"}, {"name": "rotation", "type": "varint"}, {"name": "metadata", "type": "string"}, {"name": "integrity", "type": "f32"}, {"name": "seed", "type": "varint"}, {"name": "flags", "type": "u8"}]], "packet_update_sign": ["container", [{"name": "location", "type": "position"}, {"name": "isFrontText", "type": "bool"}, {"name": "text1", "type": "string"}, {"name": "text2", "type": "string"}, {"name": "text3", "type": "string"}, {"name": "text4", "type": "string"}]], "packet_arm_animation": ["container", [{"name": "hand", "type": "varint"}]], "packet_spectate": ["container", [{"name": "target", "type": "UUID"}]], "packet_block_place": ["container", [{"name": "hand", "type": "varint"}, {"name": "location", "type": "position"}, {"name": "direction", "type": "varint"}, {"name": "cursorX", "type": "f32"}, {"name": "cursorY", "type": "f32"}, {"name": "cursorZ", "type": "f32"}, {"name": "insideBlock", "type": "bool"}, {"name": "worldBorderHit", "type": "bool"}, {"name": "sequence", "type": "varint"}]], "packet_use_item": ["container", [{"name": "hand", "type": "varint"}, {"name": "sequence", "type": "varint"}, {"name": "rotation", "type": "vec2f"}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "teleport_confirm", "0x01": "query_block_nbt", "0x02": "select_bundle_item", "0x03": "set_difficulty", "0x04": "message_acknowledgement", "0x05": "chat_command", "0x06": "chat_command_signed", "0x07": "chat_message", "0x08": "chat_session_update", "0x09": "chunk_batch_received", "0x0a": "client_command", "0x0b": "tick_end", "0x0c": "settings", "0x0d": "tab_complete", "0x0e": "configuration_acknowledged", "0x0f": "enchant_item", "0x10": "window_click", "0x11": "close_window", "0x12": "set_slot_state", "0x13": "cookie_response", "0x14": "custom_payload", "0x15": "debug_sample_subscription", "0x16": "edit_book", "0x17": "query_entity_nbt", "0x18": "use_entity", "0x19": "generate_structure", "0x1a": "keep_alive", "0x1b": "lock_difficulty", "0x1c": "position", "0x1d": "position_look", "0x1e": "look", "0x1f": "flying", "0x20": "vehicle_move", "0x21": "steer_boat", "0x22": "pick_item", "0x23": "ping_request", "0x24": "craft_recipe_request", "0x25": "abilities", "0x26": "block_dig", "0x27": "entity_action", "0x28": "player_input", "0x29": "pong", "0x2a": "recipe_book", "0x2b": "displayed_recipe", "0x2c": "name_item", "0x2d": "resource_pack_receive", "0x2e": "advancement_tab", "0x2f": "select_trade", "0x30": "set_beacon_effect", "0x31": "held_item_slot", "0x32": "update_command_block", "0x33": "update_command_block_minecart", "0x34": "set_creative_slot", "0x35": "update_jigsaw_block", "0x36": "update_structure_block", "0x37": "update_sign", "0x38": "arm_animation", "0x39": "spectate", "0x3a": "block_place", "0x3b": "use_item"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"teleport_confirm": "packet_teleport_confirm", "query_block_nbt": "packet_query_block_nbt", "select_bundle_item": "packet_select_bundle_item", "set_difficulty": "packet_set_difficulty", "message_acknowledgement": "packet_message_acknowledgement", "chat_command": "packet_chat_command", "chat_command_signed": "packet_chat_command_signed", "chat_message": "packet_chat_message", "chat_session_update": "packet_chat_session_update", "chunk_batch_received": "packet_chunk_batch_received", "client_command": "packet_client_command", "tick_end": "packet_tick_end", "settings": "packet_settings", "tab_complete": "packet_tab_complete", "configuration_acknowledged": "packet_configuration_acknowledged", "enchant_item": "packet_enchant_item", "window_click": "packet_window_click", "close_window": "packet_close_window", "set_slot_state": "packet_set_slot_state", "cookie_response": "packet_common_cookie_response", "custom_payload": "packet_custom_payload", "edit_book": "packet_edit_book", "query_entity_nbt": "packet_query_entity_nbt", "use_entity": "packet_use_entity", "generate_structure": "packet_generate_structure", "keep_alive": "packet_keep_alive", "lock_difficulty": "packet_lock_difficulty", "position": "packet_position", "position_look": "packet_position_look", "look": "packet_look", "flying": "packet_flying", "vehicle_move": "packet_vehicle_move", "steer_boat": "packet_steer_boat", "pick_item": "packet_pick_item", "ping_request": "packet_ping_request", "craft_recipe_request": "packet_craft_recipe_request", "abilities": "packet_abilities", "block_dig": "packet_block_dig", "entity_action": "packet_entity_action", "player_input": "packet_player_input", "pong": "packet_pong", "recipe_book": "packet_recipe_book", "displayed_recipe": "packet_displayed_recipe", "name_item": "packet_name_item", "resource_pack_receive": "packet_resource_pack_receive", "advancement_tab": "packet_advancement_tab", "select_trade": "packet_select_trade", "set_beacon_effect": "packet_set_beacon_effect", "held_item_slot": "packet_held_item_slot", "update_command_block": "packet_update_command_block", "update_command_block_minecart": "packet_update_command_block_minecart", "set_creative_slot": "packet_set_creative_slot", "update_jigsaw_block": "packet_update_jigsaw_block", "update_structure_block": "packet_update_structure_block", "update_sign": "packet_update_sign", "arm_animation": "packet_arm_animation", "spectate": "packet_spectate", "block_place": "packet_block_place", "use_item": "packet_use_item"}}]}]]}}}}