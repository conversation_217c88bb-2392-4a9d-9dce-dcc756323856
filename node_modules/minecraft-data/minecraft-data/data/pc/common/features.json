[{"name": "usesPalettedChunks", "description": "the chunk format uses local palettes", "versions": ["1.9", "latest"]}, {"name": "mobSpawner", "description": "spawner is called mob_spawner", "versions": ["1.8", "1.12.2"]}, {"name": "spawner", "description": "spawner is called spawner", "versions": ["1.13", "latest"]}, {"name": "hasElytraFlying", "description": "the elytra exists and players can fly with it", "versions": ["1.9", "latest"]}, {"name": "fireworkMetadataVarInt7", "description": "firework attached entity metadata is at index 7 and is varint", "versions": ["1.11", "1.13.2"]}, {"name": "fireworkMetadataOptVarInt8", "description": "firework attached entity metadata is at index 8 and is optvarint", "versions": ["1.14", "1.16.5"]}, {"name": "fireworkMetadataOptVarInt9", "description": "firework attached entity metadata is at index 9 and is optvarint", "versions": ["1.17", "latest"]}, {"name": "fireworkNamePlural", "description": "the firework entity used for elytra boosting is named fireworks_rocket", "versions": ["1.11", "1.12.2"]}, {"name": "fireworkNameSingular", "description": "the firework entity used for elytra boosting is named firework_rocket", "versions": ["1.13", "latest"]}, {"name": "blockMetadata", "description": "block metadata is encoded in a separate metadata", "versions": ["1.8", "1.12.2"]}, {"name": "blockStateId", "description": "block metadata is encoded as state id", "versions": ["1.13", "latest"]}, {"name": "creativeSleepNearMobs", "description": "can sleep near mobs in creative", "versions": ["1.13", "latest"]}, {"name": "fixedPointPosition", "description": "Entity positions are represented with fixed point numbers", "versions": ["1.8", "1.8.9"]}, {"name": "doublePosition", "description": "Entity positions are represented with double", "versions": ["1.9", "latest"]}, {"name": "fixedPointDelta", "description": "Delta of position are represented with fixed point numbers", "versions": ["1.8", "1.8.9"]}, {"name": "fixedPointDelta128", "description": "Delta of position are represented with fixed point numbers times 128", "versions": ["1.9", "latest"]}, {"name": "customChannelMCPrefixed", "description": "custom channel are prefixed by MC|", "versions": ["1.8", "1.12.2"]}, {"name": "customChannelIdentifier", "description": "custom channel is an identifier starting in minecraft namespace", "versions": ["1.13", "latest"]}, {"name": "dimensionDataIsAvailable", "description": "dimensionData is available, providing an additional information about the current dimension", "versions": ["1.17", "latest"]}, {"name": "dimensionDataInCodec", "description": "dimensionData like world height is exclusively in the codec in login packet", "versions": ["1.19", "latest"]}, {"name": "useItemWithBlockPlace", "description": "use item is done with block place packet", "versions": ["1.8", "1.8.9"]}, {"name": "useItemWithOwnPacket", "description": "use item is done with its own packet", "versions": ["1.9", "latest"]}, {"name": "blockPlaceHasHeldItem", "description": "block_place packet has heldItem", "versions": ["1.8", "1.8.9"]}, {"name": "blockPlaceHasHandAndIntCursor", "description": "block_place packet has hand and int cursor", "versions": ["1.9", "1.10.2"]}, {"name": "blockPlaceHasHandAndFloatCursor", "description": "block_place packet has hand and float cursor", "versions": ["1.11", "1.13.2"]}, {"name": "blockPlaceHasInsideBlock", "description": "block_place packet has inside block", "versions": ["1.14", "latest"]}, {"name": "teleportUsesPositionPacket", "description": "teleport is done using position packet", "versions": ["1.8", "1.8.9"]}, {"name": "positionUpdateSentEveryTick", "description": "the position is sent every tick", "versions": ["1.8", "1.11.2"]}, {"name": "teleportUsesOwnPacket", "description": "teleport is done using its own packet", "versions": ["1.9", "latest"]}, {"name": "oneBlockForSeveralVariations", "description": "one block of several variations", "versions": ["1.8", "1.12.2"]}, {"name": "blockSchemeIsFlat", "description": "all variations of a packet have their own id", "versions": ["1.13", "latest"]}, {"name": "tabCompleteHasNoToolTip", "description": "tab complete doesn't have a tool tip", "versions": ["1.8", "1.12.2"]}, {"name": "tabCompleteHasAToolTip", "description": "tab complete has a tool tip", "versions": ["1.13", "latest"]}, {"name": "effectAreMinecraftPrefixed", "description": "effect are prefixed by minecraft:", "versions": ["1.8", "1.12.2"]}, {"name": "effectAreNotPrefixed", "description": "effect are not prefixed", "versions": ["1.13", "latest"]}, {"name": "itemsAreAlsoBlocks", "description": "items are also blocks", "versions": ["1.8", "1.12.2"]}, {"name": "itemsAreNotBlocks", "description": "items are not block", "versions": ["1.13", "latest"]}, {"name": "fishingBobberCorrectlyNamed", "description": "the fishing hook entity is named fishing_bobber", "versions": ["1.14", "latest"]}, {"name": "editBookIsPluginChannel", "description": "book editing is handled through plugin channels", "versions": ["1.8", "1.12.2"]}, {"name": "hasEditBookPacket", "description": "book editing is handled through a packet with the updated book item", "versions": ["1.13", "latest"]}, {"name": "editBookPacketUsesNbt", "description": "edit_book packet sends a new book item (with its NBT containing page data) to server", "versions": ["1.13", "1.17.1"]}, {"name": "clientUpdateBookIdWhenSign", "description": "when sending MC|BSign, item type should be written_book", "versions": ["1.8", "1.8.9"]}, {"name": "useMCTrSel", "description": "select trade through plugin channel MC|TrSel", "versions": ["1.8", "1.12.2"]}, {"name": "useMCTrList", "description": "receive trade list through plugin channel MC|TrList", "versions": ["1.8", "1.12.2"]}, {"name": "usetraderlist", "description": "receive trade list through plugin channel usetrader_list", "versions": ["1.13", "1.13.2"]}, {"name": "doesntHaveOffHandSlot", "description": "doesn't have an off-hand slot", "versions": ["1.8", "1.8.9"]}, {"name": "multiBlockChangeHasTrustEdges", "description": "multi block changes has trust edges field", "versions": ["1.16", "latest"]}, {"name": "dimensionIsAnInt", "description": "dimension is an int (the index of an enum)", "versions": ["1.8", "1.15.2"]}, {"name": "dimensionIsAString", "description": "dimension is a string (the dimension is the same as the world name)", "versions": ["1.16", "1.16.1"]}, {"name": "dimensionIsAWorld", "description": "dimension is an nbt compound", "versions": ["1.16.2", "latest"]}, {"name": "doesntHaveChestType", "description": "chests don't have a type property", "versions": ["1.8", "1.12.2"]}, {"name": "usesAdvCdm", "description": "packet MC|AdvCmd was incorrectly spelled in 1.8 as MC|AdvCdm", "versions": ["1.8", "1.8.9"]}, {"name": "usesAdvCmd", "description": "uses MC|AdvCmd to set command block information", "versions": ["1.9", "1.12.2"]}, {"name": "indexesVillagerRecipes", "description": "gives a index for each trade in a villagers metadata", "versions": ["1.8", "1.11.2"]}, {"name": "hasAttackCooldown", "description": "if there is a cooldown after attacks to deal full damage", "versions": ["1.9", "latest"]}, {"name": "usesLoginPacket", "description": "uses the login packet as defined in mcData", "versions": ["1.16", "latest"]}, {"name": "usesMultiblockSingleLong", "description": "in the multi_block_change packet is stored as a single number", "versions": ["1.16.2", "latest"]}, {"name": "usesMultiblock3DChunkCoords", "description": "in the multi_block_change packet, all 3 axis coords are defined", "versions": ["1.16.2", "latest"]}, {"name": "setBlockUsesMetadataNumber", "description": "the parameter metadata of the setblock command is a number", "versions": ["1.8", "1.12.2"]}, {"name": "useMCItemName", "description": "send item name for anvil using plugin channel MC|TrList", "versions": ["1.8", "1.12.2"]}, {"name": "selectingTradeMovesItems", "description": "selecting a trade automatically puts the required items into trading slots", "versions": ["1.14", "latest"]}, {"name": "resourcePackUsesHash", "description": "resource pack uses hash validation", "versions": ["1.8", "1.9.4"]}, {"name": "resourcePackUsesUUID", "description": "resource pack uses UUID identification", "versions": ["1.20.3", "latest"]}, {"name": "lessCharsInChat", "description": "max chars in chat", "versions": ["1.8", "1.10.2"]}, {"name": "teamUsesChatComponents", "description": "teams use chatcomponents for formatting", "versions": ["1.13", "latest"]}, {"name": "teamUsesScoreboard", "description": "teams use scoreboard_team packet", "versions": ["1.8", "1.8.9"]}, {"name": "enderCrystalNameEndsInErNoCaps", "description": "this is when the end_crystal's entity name is ender_crystal", "versions": ["1.11", "1.12.2"]}, {"name": "enderCrystalNameNoCapsWithUnderscore", "description": "this is when the end_crystal's entity name is end_crystal", "versions": ["1.14", "latest"]}, {"name": "entityNameUpperCaseNoUnderscore", "description": "this is when some entities names would be captialized and appended without underscores like 'Boat' or 'ArmorStand'", "versions": ["1.8", "1.10.2"]}, {"name": "entityNameLowerCaseNoUnderscore", "description": "this is when some entity names are lowercase and appended without underscores like 'armorstand' or 'endercrystal'", "versions": ["1.13", "1.13.2"]}, {"name": "transactionPacketExists", "description": "this is when the description packet existed", "versions": ["1.8", "1.16.5"]}, {"name": "stateIdUsed", "description": "starting in 1.17.1, actionId has been replaced with stateId", "versions": ["1.17.1", "latest"]}, {"name": "actionIdUsed", "description": "pre 1.17, actionId is used", "versions": ["1.8", "1.16.5"]}, {"name": "setSlotAsTransaction", "description": "use setslot as transaction instead of just hoping it'll work", "versions": ["1.17", "latest"]}, {"name": "armAnimationBeforeUse", "description": "arm animation packet sent before use entity packet", "versions": ["1.8", "1.8.9"]}, {"name": "tallWorld", "description": "world y defaults to starts at -64 and ends at 384", "versions": ["1.18", "latest"]}, {"name": "sendStringifiedSignText", "description": "sign text send when updating signs is send as stringified strings", "versions": ["1.8", "1.8.9"]}, {"name": "usesBlockStates", "versions": ["1.13", "latest"]}, {"name": "effectNamesMatchRegistryName", "versions": ["1.17", "latest"]}, {"name": "shieldSlot", "versions": ["1.9", "latest"]}, {"name": "village&pillageInventoryWindows", "versions": ["1.14", "latest"]}, {"name": "netherUpdateInventoryWindows", "versions": ["1.16", "latest"]}, {"name": "unloadChunkByEmptyChunk", "description": "Chunk unloading is done by sending an empty chunk", "versions": ["1.8", "1.8.9"]}, {"name": "unloadChunkDirect", "description": "Chunk unloading is done by sending directly an unload chunk packet", "versions": ["1.9", "latest"]}, {"name": "entityCamelCase", "description": "entity names are in camel case", "versions": ["1.8", "1.10.2"]}, {"name": "entitySnakeCase", "description": "entity name are in snake case", "versions": ["1.11", "latest"]}, {"name": "respawnIsPayload", "description": "respawn field is payload", "versions": ["1.8", "1.8.9"]}, {"name": "respawnIsActionId", "description": "respawn field is action id", "versions": ["1.9", "latest"]}, {"name": "attachStackEntity", "description": "attach is used to stack entities", "versions": ["1.8", "1.8.9"]}, {"name": "setPassengerStackEntity", "description": "set passengers is used to stack entities", "versions": ["1.9", "latest"]}, {"name": "theFlattening", "description": "many items got merged, separated or renamed", "versions": ["1.13", "latest"]}, {"name": "blockPlaceHasIntCursor", "description": "block_place packet has int cursor", "versions": ["1.8", "1.10.2"]}, {"name": "updateViewPosition", "description": "the client's chunk position must be updated to render chunks correctly", "versions": ["1.14", "latest"]}, {"name": "lightSentSeparately", "description": "chunk light data is sent in a separate packet", "versions": ["1.14", "latest"]}, {"name": "difficultySentSeparately", "description": "game difficulty is sent separately from the login packet", "versions": ["1.14", "latest"]}, {"name": "biomesSentSeparately", "description": "biomes sent in own packet", "versions": ["1.15", "1.17_major"]}, {"name": "acknowledgePlayerDigging", "description": "player digging packets should be responded to", "versions": ["1.14", "latest"]}, {"name": "multiTypeSigns", "description": "there are 6 types of signs based on the different trees", "versions": ["1.14", "latest"]}, {"name": "entityMetadataSentSeparately", "description": "entity metadata is sent separately from the spawn packets", "versions": ["1.15", "latest"]}, {"name": "attributeSnakeCase", "description": "entity attributes are in snake case", "versions": ["1.16", "latest"]}, {"name": "allEntityEquipmentInOne", "description": "entity equipment packet contains all equipment slots instead of just one", "versions": ["1.16", "latest"]}, {"name": "entityMCPrefixed", "description": "entity prefixed with minecraft: on this versions", "versions": ["1.12", "latest"]}, {"name": "nbtOnMetadata", "description": "in never versions its nbt but in 1.8 its on metadata", "versions": ["1.8", "1.8.9"]}, {"name": "theShulkerBoxes", "description": "added shulker boxes to the game", "versions": ["1.13", "latest"]}, {"name": "metadataIxOfItem", "description": "item.metadata[this_ix] will be the item that was dropped on the ground", "values": [{"value": 8, "versions": ["1.17_major", "latest"]}, {"value": 7, "versions": ["1.14_major", "1.16_major"]}, {"value": 6, "versions": ["1.10_major", "1.13_major"]}, {"value": 5, "version": "1.9_major"}, {"value": 8, "version": "1.8_major"}]}, {"name": "itemSerializationWillOnlyUsePresent", "description": "item serialization in [even] newer versions uses present field [exclusively] to show nullability rather than sending blockId as -1", "versions": ["1.14_major", "latest"]}, {"name": "itemSerializationAllowsPresent", "description": "item serialization in newer versions uses present field to show nullability rather or sending blockId as -1 can be used", "versions": ["1.13_major", "latest"]}, {"name": "itemSerializationUsesBlockId", "description": "item serialization in older versions uses blockId field to show nullability by setting blockId to -1", "versions": ["1.8_major", "1.12_major"]}, {"name": "saveDurabilityAsDamage", "description": "in newer versions, an nbt key called 'Damage' is used to store durability", "versions": ["1.13_major", "latest"]}, {"name": "nbtNameForEnchant", "description": "what the nbt key for enchants is", "values": [{"value": "Enchantments", "versions": ["1.13_major", "latest"]}, {"value": "ench", "versions": ["1.8_major", "1.12_major"]}]}, {"name": "booksUseStoredEnchantments", "description": "Enchanted books store enchantment data in a separate NBT tag called StoredEnchantments", "versions": ["1.8_major", "latest"]}, {"name": "typeOfValueForEnchantLevel", "description": "type of value that stores enchant lvl in the nbt", "values": [{"value": "string", "versions": ["1.13_major", "latest"]}, {"value": "short", "versions": ["1.8_major", "1.12_major"]}]}, {"name": "whereDurabilityIsSerialized", "description": "where the durability is saved in nbt", "values": [{"value": "Damage", "versions": ["1.13_major", "latest"]}, {"value": "metadata", "versions": ["1.8_major", "1.12_major"]}]}, {"name": "itemLoreIsAString", "description": "An item's custom Lore is stored as a string in NBT, in older versions it's a list of strings", "versions": ["1.14_major", "latest"]}, {"name": "spawnEggsUseInternalIdInNbt", "description": "in older versions, spawn eggs have a field in their nbt called 'internalId' which tells what entity they will spawn", "versions": ["1.8_major", "1.8_major"]}, {"name": "spawnEggsUseEntityTagInNbt", "description": "in older versions, spawn eggs have a key in their nbt called EntityTag which is an object with a field called id, which is an identifier like 'minecraft:cow' that tells the client what mob this egg will spawn", "versions": ["1.9_major", "1.12_major"]}, {"name": "spawnEggsHaveSpawnedEntityInName", "description": "in newer versions, spawn eggs have the entity they spawn in their name, ex: 'squid_spawn_egg'", "versions": ["1.13_major", "latest"]}, {"name": "signed<PERSON><PERSON>", "description": "starting in 1.19 chat messages generally carry a cryptographic signature, `packet_chat` has been replaced clientbound by `packet_player_chat` and `packet_system_chat` and serverbound by `packet_chat_message`", "versions": ["1.19_major", "latest"]}, {"name": "signatureEncryption", "description": "allows for usage of `signature` instead of verifyToken in serverbound packet_encryption_begin", "versions": ["1.19", "1.19.2"]}, {"name": "profileKeySignatureV2", "description": "uses `signatureV2` public key signature", "versions": ["1.19.2", "latest"]}, {"name": "clientboundChatHasSender", "description": "clientbound chat packet contains message sender's UUID", "versions": ["1.16_major", "latest"]}, {"name": "consolidatedEntitySpawnPacket", "description": "One packet to add living and non-living entities", "versions": ["1.19", "latest"]}, {"name": "chainedChatWithHashing", "description": "Signed messages are ordered and depend on previous messages, and message payloads are hashed before generating a signature", "version": "1.19.2"}, {"name": "useChatSessions", "description": "Users generate a session with a unique ID upon login. Chat messages are signed using this ID and an index that is increased with each message", "versions": ["1.19.3", "latest"]}, {"name": "playerInfoActionIsBitfield", "description": "The player_info packet may contain multiple actions in one packet. The actions field is a bitfield signifying what actions are included", "versions": ["1.19.3", "latest"]}, {"name": "clientsideChatFormatting", "description": "Chat messages are formatted on the client side", "versions": ["1.19_major", "latest"]}, {"name": "entityMetadataHasLong", "description": "There exists a serializer of type long for entity metadata", "versions": ["1.19.3", "latest"]}, {"name": "hasBundlePacket", "description": "Has a Bundle Packet to group packets for processing at once", "versions": ["1.19.4", "latest"]}, {"name": "mcDataHasEntityMetadata", "description": "Entity Metadata is defined in mcdata", "versions": ["1.19.4", "latest"]}, {"name": "multiSidedSigns", "description": "Signs can have text on the front and back", "versions": ["1.20", "latest"]}, {"name": "explicitMaxDurability", "description": "Items with maximum durability have explicit NBT data Damage:0", "versions": ["1.15_major", "latest"]}, {"name": "newLightingDataFormat", "description": "in 1.17, light encoding changed to handle new world height", "versions": ["1.17", "latest"]}, {"name": "hasConfigurationState", "description": "in 1.20.2, a new configuration state was added to allow configuration after login", "versions": ["1.20.2", "latest"]}, {"name": "unifiedPlayerAndEntitySpawnPacket", "description": "Players and entities spawn with the same spawn_entity packet", "versions": ["1.20.2", "latest"]}, {"name": "chatPacketsUseNbtComponents", "description": "Chat message packets use NBT to serialize chat components instead of JSON", "versions": ["1.20.3", "latest"]}, {"name": "seperateSignedChatCommandPacket", "description": "Signed chat commands use new `packet_chat_command_signed` packet", "versions": ["1.20.5", "latest"]}, {"name": "spawnRespawnWorldDataField", "description": "Spawn and respawn packet now use shared worldState type for their data, and dimensions are now integers", "versions": ["1.20.5", "latest"]}, {"name": "updatedParticlesPacket", "description": "Particle packet structure contains uses strings instead of integers in protocol", "versions": ["1.20.5", "latest"]}, {"name": "segmentedRegistryCodecData", "description": "Codec data is now split into multiple NBTs by ID instead of one", "versions": ["1.20.5", "latest"]}, {"name": "itemsWithComponents", "description": "New Item schema", "versions": ["1.20.5", "latest"]}, {"name": "hasExecuteCommand", "description": "Support the execute command", "versions": ["1.14", "latest"]}, {"name": "incrementedChatType", "description": "The chat type field in the `player_chat` packet now starts at 1 instead of 0", "versions": ["1.21", "latest"]}, {"name": "registryDataIsMandatory", "description": "The server needs to send the registry data to the client before sending the finish_configuration packet", "versions": ["1.21", "latest"]}]