{"types": {"string": ["pstring", {"countType": "varint"}], "ByteArray": ["buffer", {"countType": "varint"}], "SignedByteArray": ["buffer", {"countType": "zigzag32"}], "LittleString": ["pstring", {"countType": "li32"}], "LatinString": ["pstring", {"countType": "varint", "encoding": "latin1"}], "ShortArray": ["buffer", {"countType": "li16"}], "ShortString": ["pstring", {"countType": "li16"}], "varint64": "native", "zigzag32": "native", "zigzag64": "native", "uuid": "native", "byterot": "native", "bitflags": "native", "restBuffer": "native", "encapsulated": "native", "nbt": "native", "lnbt": "native", "nbtLoop": "native", "enum_size_based_on_values_len": "native", "MapInfo": "native", "BehaviourPackInfos": ["array", {"countType": "li16", "type": ["container", [{"name": "uuid", "type": "string"}, {"name": "version", "type": "string"}, {"name": "size", "type": "lu64"}, {"name": "content_key", "type": "string"}, {"name": "sub_pack_name", "type": "string"}, {"name": "content_identity", "type": "string"}, {"name": "has_scripts", "type": "bool"}]]}], "TexturePackInfos": ["array", {"countType": "li16", "type": ["container", [{"name": "uuid", "type": "string"}, {"name": "version", "type": "string"}, {"name": "size", "type": "lu64"}, {"name": "content_key", "type": "string"}, {"name": "sub_pack_name", "type": "string"}, {"name": "content_identity", "type": "string"}, {"name": "has_scripts", "type": "bool"}, {"name": "rtx_enabled", "type": "bool"}]]}], "ResourcePackIdVersions": ["array", {"countType": "varint", "type": ["container", [{"name": "uuid", "type": "string"}, {"name": "version", "type": "string"}, {"name": "name", "type": "string"}]]}], "ResourcePackIds": ["array", {"countType": "li16", "type": "string"}], "Experiment": ["container", [{"name": "name", "type": "string"}, {"name": "enabled", "type": "bool"}]], "Experiments": ["array", {"countType": "li32", "type": "Experiment"}], "GameMode": ["mapper", {"type": "zigzag32", "mappings": {"0": "survival", "1": "creative", "2": "adventure", "3": "survival_spectator", "4": "creative_spectator", "5": "fallback"}}], "GameRule": ["container", [{"name": "name", "type": "string"}, {"name": "editable", "type": "bool"}, {"name": "type", "type": ["mapper", {"type": "varint", "mappings": {"1": "bool", "2": "int", "3": "float"}}]}, {"name": "value", "type": ["switch", {"compareTo": "type", "fields": {"bool": "bool", "int": "zigzag32", "float": "lf32"}}]}]], "GameRules": ["array", {"countType": "varint", "type": "GameRule"}], "Blob": ["container", [{"name": "hash", "type": "lu64"}, {"name": "payload", "type": "ByteArray"}]], "BlockProperties": ["array", {"countType": "varint", "type": ["container", [{"name": "name", "type": "string"}, {"name": "state", "type": "nbt"}]]}], "Itemstates": ["array", {"countType": "varint", "type": ["container", [{"name": "name", "type": "string"}, {"name": "runtime_id", "type": "li16"}, {"name": "component_based", "type": "bool"}]]}], "ItemExtraDataWithBlockingTick": ["container", [{"name": "has_nbt", "type": ["mapper", {"type": "lu16", "mappings": {"0": "false", "65535": "true"}}]}, {"name": "nbt", "type": ["switch", {"compareTo": "has_nbt", "fields": {"true": ["container", [{"name": "version", "type": "u8"}, {"name": "nbt", "type": "lnbt"}]]}, "default": "void"}]}, {"name": "can_place_on", "type": ["array", {"countType": "li32", "type": "ShortString"}]}, {"name": "can_destroy", "type": ["array", {"countType": "li32", "type": "ShortString"}]}, {"name": "blocking_tick", "type": "li64"}]], "ItemExtraDataWithoutBlockingTick": ["container", [{"name": "has_nbt", "type": ["mapper", {"type": "lu16", "mappings": {"0": "false", "65535": "true"}}]}, {"name": "nbt", "type": ["switch", {"compareTo": "has_nbt", "fields": {"true": ["container", [{"name": "version", "type": "u8"}, {"name": "nbt", "type": "lnbt"}]]}, "default": "void"}]}, {"name": "can_place_on", "type": ["array", {"countType": "li32", "type": "ShortString"}]}, {"name": "can_destroy", "type": ["array", {"countType": "li32", "type": "ShortString"}]}]], "ItemLegacy": ["container", [{"name": "network_id", "type": "zigzag32"}, {"anon": true, "type": ["switch", {"compareTo": "network_id", "fields": {"0": "void"}, "default": ["container", [{"name": "count", "type": "lu16"}, {"name": "metadata", "type": "varint"}, {"name": "block_runtime_id", "type": "zigzag32"}, {"name": "extra", "type": ["switch", {"compareTo": "network_id", "fields": {"/ShieldItemID": ["encapsulated", {"lengthType": "varint", "type": "ItemExtraDataWithBlockingTick"}]}, "default": ["encapsulated", {"lengthType": "varint", "type": "ItemExtraDataWithoutBlockingTick"}]}]}]]}]}]], "Item": ["container", [{"name": "network_id", "type": "zigzag32"}, {"anon": true, "type": ["switch", {"compareTo": "network_id", "fields": {"0": "void"}, "default": ["container", [{"name": "count", "type": "lu16"}, {"name": "metadata", "type": "varint"}, {"name": "has_stack_id", "type": "u8"}, {"name": "stack_id", "type": ["switch", {"compareTo": "has_stack_id", "fields": {"0": "void"}, "default": "zigzag32"}]}, {"name": "block_runtime_id", "type": "zigzag32"}, {"name": "extra", "type": ["switch", {"compareTo": "network_id", "fields": {"/ShieldItemID": ["encapsulated", {"lengthType": "varint", "type": "ItemExtraDataWithBlockingTick"}]}, "default": ["encapsulated", {"lengthType": "varint", "type": "ItemExtraDataWithoutBlockingTick"}]}]}]]}]}]], "vec3i": ["container", [{"name": "x", "type": "zigzag32"}, {"name": "y", "type": "zigzag32"}, {"name": "z", "type": "zigzag32"}]], "vec3u": ["container", [{"name": "x", "type": "varint"}, {"name": "y", "type": "varint"}, {"name": "z", "type": "varint"}]], "vec3f": ["container", [{"name": "x", "type": "lf32"}, {"name": "y", "type": "lf32"}, {"name": "z", "type": "lf32"}]], "vec2f": ["container", [{"name": "x", "type": "lf32"}, {"name": "z", "type": "lf32"}]], "MetadataDictionary": ["array", {"countType": "varint", "type": ["container", [{"name": "key", "type": ["mapper", {"type": "varint", "mappings": {"0": "flags", "1": "health", "2": "variant", "3": "color", "4": "nametag", "5": "owner_eid", "6": "target_eid", "7": "air", "8": "potion_color", "9": "potion_ambient", "10": "jump_duration", "11": "hurt_time", "12": "hurt_direction", "13": "paddle_time_left", "14": "paddle_time_right", "15": "experience_value", "16": "minecart_display_block", "17": "minecart_display_offset", "18": "minecart_has_display", "20": "old_swell", "21": "swell_dir", "22": "charge_amount", "23": "enderman_held_runtime_id", "24": "entity_age", "26": "player_flags", "27": "player_index", "28": "player_bed_position", "29": "fireball_power_x", "30": "fireball_power_y", "31": "fireball_power_z", "32": "aux_power", "33": "fish_x", "34": "fish_z", "35": "fish_angle", "36": "potion_aux_value", "37": "lead_holder_eid", "38": "scale", "39": "interactive_tag", "40": "npc_skin_id", "41": "url_tag", "42": "max_airdata_max_air", "43": "mark_variant", "44": "container_type", "45": "container_base_size", "46": "container_extra_slots_per_strength", "47": "block_target", "48": "wither_invulnerable_ticks", "49": "wither_target_1", "50": "wither_target_2", "51": "wither_target_3", "52": "aerial_attack", "53": "boundingbox_width", "54": "boundingbox_height", "55": "fuse_length", "56": "rider_seat_position", "57": "rider_rotation_locked", "58": "rider_max_rotation", "59": "rider_min_rotation", "60": "rider_rotation_offset", "61": "area_effect_cloud_radius", "62": "area_effect_cloud_waiting", "63": "area_effect_cloud_particle_id", "64": "shulker_peek_id", "65": "shulker_attach_face", "66": "shulker_attached", "67": "shulker_attach_pos", "68": "trading_player_eid", "69": "trading_career", "70": "has_command_block", "71": "command_block_command", "72": "command_block_last_output", "73": "command_block_track_output", "74": "controlling_rider_seat_number", "75": "strength", "76": "max_strength", "77": "spell_casting_color", "78": "limited_life", "79": "armor_stand_pose_index", "80": "ender_crystal_time_offset", "81": "always_show_nametag", "82": "color_2", "83": "name_author", "84": "score_tag", "85": "balloon_attached_entity", "86": "pufferfish_size", "87": "bubble_time", "88": "agent", "89": "sitting_amount", "90": "sitting_amount_previous", "91": "eating_counter", "92": "flags_extended", "93": "laying_amount", "94": "laying_amount_previous", "95": "duration", "96": "spawn_time", "97": "change_rate", "98": "change_on_pickup", "99": "pickup_count", "100": "interact_text", "101": "trade_tier", "102": "max_trade_tier", "103": "trade_experience", "104": "skin_id", "105": "spawning_frames", "106": "command_block_tick_delay", "107": "command_block_execute_on_first_tick", "108": "ambient_sound_interval", "109": "ambient_sound_interval_range", "110": "ambient_sound_event_name", "111": "fall_damage_multiplier", "112": "name_raw_text", "113": "can_ride_target", "114": "low_tier_cured_discount", "115": "high_tier_cured_discount", "116": "nearby_cured_discount", "117": "nearby_cured_discount_timestamp", "118": "hitbox", "119": "is_buoyant", "120": "base_runtime_id", "121": "freezing_effect_strength", "122": "buoyancy_data", "123": "goat_horn_count", "124": "update_properties"}}]}, {"name": "type", "type": ["mapper", {"type": "varint", "mappings": {"0": "byte", "1": "short", "2": "int", "3": "float", "4": "string", "5": "compound", "6": "vec3i", "7": "long", "8": "vec3f"}}]}, {"name": "value", "type": ["switch", {"compareTo": "key", "fields": {"flags": "MetadataFlags1", "flags_extended": "MetadataFlags2"}, "default": ["switch", {"compareTo": "type", "fields": {"byte": "i8", "short": "li16", "int": "zigzag32", "float": "lf32", "string": "string", "compound": "nbt", "vec3i": "vec3i", "long": "zigzag64", "vec3f": "vec3f"}}]}]}]]}], "MetadataFlags1": ["bitflags", {"type": "zigzag64", "big": true, "flags": ["onfire", "sneaking", "riding", "sprinting", "action", "invisible", "tempted", "inlove", "saddled", "powered", "ignited", "baby", "converting", "critical", "can_show_nametag", "always_show_nametag", "no_ai", "silent", "wallclimbing", "can_climb", "swimmer", "can_fly", "walker", "resting", "sitting", "angry", "interested", "charged", "tamed", "orphaned", "leashed", "sheared", "gliding", "elder", "moving", "breathing", "chested", "stackable", "showbase", "rearing", "vibrating", "idling", "evoker_spell", "charge_attack", "wasd_controlled", "can_power_jump", "linger", "has_collision", "affected_by_gravity", "fire_immune", "dancing", "enchanted", "show_trident_rope", "container_private", "transforming", "spin_attack", "swimming", "bribed", "pregnant", "laying_egg", "rider_can_pick", "transition_sitting", "eating", "laying_down"]}], "MetadataFlags2": ["bitflags", {"type": "zigzag64", "big": true, "flags": ["sneezing", "trusting", "rolling", "scared", "in_scaffolding", "over_scaffolding", "fall_through_scaffolding", "blocking", "transition_blocking", "blocked_using_shield", "blocked_using_damaged_shield", "sleeping", "wants_to_wake", "trade_interest", "door_breaker", "breaking_obstruction", "door_opener", "illager_captain", "stunned", "roaring", "delayed_attacking", "avoiding_mobs", "avoiding_block", "facing_target_to_range_attack", "hidden_when_invisible", "is_in_ui", "stalking", "emoting", "celebrating", "admiring", "celebrating_special", "unknown95", "ram_attack", "playing_dead"]}], "Link": ["container", [{"name": "ridden_entity_id", "type": "zigzag64"}, {"name": "rider_entity_id", "type": "zigzag64"}, {"name": "type", "type": "u8"}, {"name": "immediate", "type": "bool"}, {"name": "rider_initiated", "type": "bool"}]], "Links": ["array", {"countType": "varint", "type": "Link"}], "EntityAttributes": ["array", {"countType": "varint", "type": ["container", [{"name": "name", "type": "string"}, {"name": "min", "type": "lf32"}, {"name": "value", "type": "lf32"}, {"name": "max", "type": "lf32"}]]}], "Rotation": ["container", [{"name": "yaw", "type": "byterot"}, {"name": "pitch", "type": "byterot"}, {"name": "head_yaw", "type": "byterot"}]], "BlockCoordinates": ["container", [{"name": "x", "type": "zigzag32"}, {"name": "y", "type": "varint"}, {"name": "z", "type": "zigzag32"}]], "PlayerAttributes": ["array", {"countType": "varint", "type": ["container", [{"name": "min", "type": "lf32"}, {"name": "max", "type": "lf32"}, {"name": "current", "type": "lf32"}, {"name": "default", "type": "lf32"}, {"name": "name", "type": "string"}]]}], "TransactionUseItem": ["container", [{"name": "action_type", "type": ["mapper", {"type": "varint", "mappings": {"0": "click_block", "1": "click_air", "2": "break_block"}}]}, {"name": "block_position", "type": "BlockCoordinates"}, {"name": "face", "type": "zigzag32"}, {"name": "hotbar_slot", "type": "zigzag32"}, {"name": "held_item", "type": "<PERSON><PERSON>"}, {"name": "player_pos", "type": "vec3f"}, {"name": "click_pos", "type": "vec3f"}, {"name": "block_runtime_id", "type": "varint"}]], "TransactionActions": ["array", {"countType": "varint", "type": ["container", [{"name": "source_type", "type": ["mapper", {"type": "varint", "mappings": {"0": "container", "1": "global", "2": "world_interaction", "3": "creative", "100": "craft_slot", "99999": "craft"}}]}, {"anon": true, "type": ["switch", {"compareTo": "source_type", "fields": {"container": ["container", [{"name": "inventory_id", "type": "WindowIDVarint"}]], "craft": ["container", [{"name": "action", "type": "varint"}]], "world_interaction": ["container", [{"name": "flags", "type": "varint"}]], "craft_slot": ["container", [{"name": "action", "type": "varint"}]]}, "default": "void"}]}, {"name": "slot", "type": "varint"}, {"name": "old_item", "type": "<PERSON><PERSON>"}, {"name": "new_item", "type": "<PERSON><PERSON>"}]]}], "TransactionLegacy": ["container", [{"name": "legacy_request_id", "type": "zigzag32"}, {"name": "legacy_transactions", "type": ["switch", {"compareTo": "legacy_request_id", "fields": {"0": "void"}, "default": ["array", {"countType": "varint", "type": ["container", [{"name": "container_id", "type": "u8"}, {"name": "changed_slots", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "slot_id", "type": "u8"}]]}]}]]}]}]}]], "Transaction": ["container", [{"name": "legacy", "type": "TransactionLegacy"}, {"name": "transaction_type", "type": ["mapper", {"type": "varint", "mappings": {"0": "normal", "1": "inventory_mismatch", "2": "item_use", "3": "item_use_on_entity", "4": "item_release"}}]}, {"name": "actions", "type": "TransactionActions"}, {"name": "transaction_data", "type": ["switch", {"compareTo": "transaction_type", "fields": {"normal": "void", "inventory_mismatch": "void", "item_use": "TransactionUseItem", "item_use_on_entity": ["container", [{"name": "entity_runtime_id", "type": "varint64"}, {"name": "action_type", "type": ["mapper", {"type": "varint", "mappings": {"0": "interact", "1": "attack"}}]}, {"name": "hotbar_slot", "type": "zigzag32"}, {"name": "held_item", "type": "<PERSON><PERSON>"}, {"name": "player_pos", "type": "vec3f"}, {"name": "click_pos", "type": "vec3f"}]], "item_release": ["container", [{"name": "action_type", "type": ["mapper", {"type": "varint", "mappings": {"0": "release", "1": "consume"}}]}, {"name": "hotbar_slot", "type": "zigzag32"}, {"name": "held_item", "type": "<PERSON><PERSON>"}, {"name": "head_pos", "type": "vec3f"}]]}}]}]], "ItemStacks": ["array", {"countType": "varint", "type": "<PERSON><PERSON>"}], "RecipeIngredient": ["container", [{"name": "network_id", "type": "zigzag32"}, {"anon": true, "type": ["switch", {"compareTo": "network_id", "fields": {"0": "void"}, "default": ["container", [{"name": "network_data", "type": "zigzag32"}, {"name": "count", "type": "zigzag32"}]]}]}]], "PotionTypeRecipes": ["array", {"countType": "varint", "type": ["container", [{"name": "input_item_id", "type": "zigzag32"}, {"name": "input_item_meta", "type": "zigzag32"}, {"name": "ingredient_id", "type": "zigzag32"}, {"name": "ingredient_meta", "type": "zigzag32"}, {"name": "output_item_id", "type": "zigzag32"}, {"name": "output_item_meta", "type": "zigzag32"}]]}], "PotionContainerChangeRecipes": ["array", {"countType": "varint", "type": ["container", [{"name": "input_item_id", "type": "zigzag32"}, {"name": "ingredient_id", "type": "zigzag32"}, {"name": "output_item_id", "type": "zigzag32"}]]}], "Recipes": ["array", {"countType": "varint", "type": ["container", [{"name": "type", "type": ["mapper", {"type": "zigzag32", "mappings": {"0": "shapeless", "1": "shaped", "2": "furnace", "3": "furnace_with_metadata", "4": "multi", "5": "shulker_box", "6": "shapeless_chemistry", "7": "shaped_chemistry"}}]}, {"name": "recipe", "type": ["switch", {"compareTo": "type", "fields": {"shapeless": ["container", [{"name": "recipe_id", "type": "LatinString"}, {"name": "input", "type": ["array", {"countType": "varint", "type": "RecipeIngredient"}]}, {"name": "output", "type": ["array", {"countType": "varint", "type": "ItemLegacy"}]}, {"name": "uuid", "type": "uuid"}, {"name": "block", "type": "string"}, {"name": "priority", "type": "zigzag32"}, {"name": "network_id", "type": "varint"}]], "shulker_box": ["container", [{"name": "recipe_id", "type": "LatinString"}, {"name": "input", "type": ["array", {"countType": "varint", "type": "RecipeIngredient"}]}, {"name": "output", "type": ["array", {"countType": "varint", "type": "ItemLegacy"}]}, {"name": "uuid", "type": "uuid"}, {"name": "block", "type": "string"}, {"name": "priority", "type": "zigzag32"}, {"name": "network_id", "type": "varint"}]], "shapeless_chemistry": ["container", [{"name": "recipe_id", "type": "LatinString"}, {"name": "input", "type": ["array", {"countType": "varint", "type": "RecipeIngredient"}]}, {"name": "output", "type": ["array", {"countType": "varint", "type": "ItemLegacy"}]}, {"name": "uuid", "type": "uuid"}, {"name": "block", "type": "string"}, {"name": "priority", "type": "zigzag32"}, {"name": "network_id", "type": "varint"}]], "shaped": ["container", [{"name": "recipe_id", "type": "LatinString"}, {"name": "width", "type": "zigzag32"}, {"name": "height", "type": "zigzag32"}, {"name": "input", "type": ["array", {"count": "width", "type": ["array", {"count": "height", "type": "RecipeIngredient"}]}]}, {"name": "output", "type": ["array", {"countType": "varint", "type": "ItemLegacy"}]}, {"name": "uuid", "type": "uuid"}, {"name": "block", "type": "string"}, {"name": "priority", "type": "zigzag32"}, {"name": "network_id", "type": "varint"}]], "shaped_chemistry": ["container", [{"name": "recipe_id", "type": "LatinString"}, {"name": "width", "type": "zigzag32"}, {"name": "height", "type": "zigzag32"}, {"name": "input", "type": ["array", {"count": "width", "type": ["array", {"count": "height", "type": "RecipeIngredient"}]}]}, {"name": "output", "type": ["array", {"countType": "varint", "type": "ItemLegacy"}]}, {"name": "uuid", "type": "uuid"}, {"name": "block", "type": "string"}, {"name": "priority", "type": "zigzag32"}, {"name": "network_id", "type": "varint"}]], "furnace": ["container", [{"name": "input_id", "type": "zigzag32"}, {"name": "output", "type": "ItemLegacy"}, {"name": "block", "type": "string"}]], "furnace_with_metadata": ["container", [{"name": "input_id", "type": "zigzag32"}, {"name": "input_meta", "type": "zigzag32"}, {"name": "output", "type": "ItemLegacy"}, {"name": "block", "type": "string"}]], "multi": ["container", [{"name": "uuid", "type": "uuid"}, {"name": "network_id", "type": "varint"}]]}}]}]]}], "SkinImage": ["container", [{"name": "width", "type": "li32"}, {"name": "height", "type": "li32"}, {"name": "data", "type": "ByteArray"}]], "Skin": ["container", [{"name": "skin_id", "type": "string"}, {"name": "play_fab_id", "type": "string"}, {"name": "skin_resource_pack", "type": "string"}, {"name": "skin_data", "type": "SkinImage"}, {"name": "animations", "type": ["array", {"countType": "li32", "type": ["container", [{"name": "skin_image", "type": "SkinImage"}, {"name": "animation_type", "type": "li32"}, {"name": "animation_frames", "type": "lf32"}, {"name": "expression_type", "type": "lf32"}]]}]}, {"name": "cape_data", "type": "SkinImage"}, {"name": "geometry_data", "type": "string"}, {"name": "geometry_data_version", "type": "string"}, {"name": "animation_data", "type": "string"}, {"name": "cape_id", "type": "string"}, {"name": "full_skin_id", "type": "string"}, {"name": "arm_size", "type": "string"}, {"name": "skin_color", "type": "string"}, {"name": "personal_pieces", "type": ["array", {"countType": "li32", "type": ["container", [{"name": "piece_id", "type": "string"}, {"name": "piece_type", "type": "string"}, {"name": "pack_id", "type": "string"}, {"name": "is_default_piece", "type": "bool"}, {"name": "product_id", "type": "string"}]]}]}, {"name": "piece_tint_colors", "type": ["array", {"countType": "li32", "type": ["container", [{"name": "piece_type", "type": "string"}, {"name": "colors", "type": ["array", {"countType": "li32", "type": "string"}]}]]}]}, {"name": "premium", "type": "bool"}, {"name": "persona", "type": "bool"}, {"name": "cape_on_classic", "type": "bool"}, {"name": "primary_user", "type": "bool"}]], "PlayerRecords": ["container", [{"name": "type", "type": ["mapper", {"type": "u8", "mappings": {"0": "add", "1": "remove"}}]}, {"name": "records_count", "type": "varint"}, {"name": "records", "type": ["array", {"count": "records_count", "type": ["switch", {"compareTo": "type", "fields": {"add": ["container", [{"name": "uuid", "type": "uuid"}, {"name": "entity_unique_id", "type": "zigzag64"}, {"name": "username", "type": "string"}, {"name": "xbox_user_id", "type": "string"}, {"name": "platform_chat_id", "type": "string"}, {"name": "build_platform", "type": "li32"}, {"name": "skin_data", "type": "Skin"}, {"name": "is_teacher", "type": "bool"}, {"name": "is_host", "type": "bool"}]], "remove": ["container", [{"name": "uuid", "type": "uuid"}]]}}]}]}, {"name": "verified", "type": ["switch", {"compareTo": "type", "fields": {"add": ["array", {"count": "records_count", "type": "bool"}]}}]}]], "Enchant": ["container", [{"name": "id", "type": "u8"}, {"name": "level", "type": "u8"}]], "EnchantOption": ["container", [{"name": "cost", "type": "varint"}, {"name": "slot_flags", "type": "li32"}, {"name": "equip_enchants", "type": ["array", {"countType": "varint", "type": "Enchant"}]}, {"name": "held_enchants", "type": ["array", {"countType": "varint", "type": "Enchant"}]}, {"name": "self_enchants", "type": ["array", {"countType": "varint", "type": "Enchant"}]}, {"name": "name", "type": "string"}, {"name": "option_id", "type": "zigzag32"}]], "Action": ["mapper", {"type": "zigzag32", "mappings": {"0": "start_break", "1": "abort_break", "2": "stop_break", "3": "get_updated_block", "4": "drop_item", "5": "start_sleeping", "6": "stop_sleeping", "7": "respawn", "8": "jump", "9": "start_sprint", "10": "stop_sprint", "11": "start_sneak", "12": "stop_sneak", "13": "creative_player_destroy_block", "14": "dimension_change_ack", "15": "start_glide", "16": "stop_glide", "17": "build_denied", "18": "crack_break", "19": "change_skin", "20": "set_enchatnment_seed", "21": "swimming", "22": "stop_swimming", "23": "start_spin_attack", "24": "stop_spin_attack", "25": "interact_block", "26": "predict_break", "27": "continue_break"}}], "StackRequestSlotInfo": ["container", [{"name": "slot_type", "type": "ContainerSlotType"}, {"name": "slot", "type": "u8"}, {"name": "stack_id", "type": "zigzag32"}]], "ItemStackRequest": ["container", [{"name": "request_id", "type": "zigzag32"}, {"name": "actions", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "type_id", "type": ["mapper", {"type": "u8", "mappings": {"0": "take", "1": "place", "2": "swap", "3": "drop", "4": "destroy", "5": "consume", "6": "create", "7": "lab_table_combine", "8": "beacon_payment", "9": "mine_block", "10": "craft_recipe", "11": "craft_recipe_auto", "12": "craft_creative", "13": "optional", "14": "craft_grindstone_request", "15": "craft_loom_request", "16": "non_implemented", "17": "results_deprecated"}}]}, {"anon": true, "type": ["switch", {"compareTo": "type_id", "fields": {"take": ["container", [{"name": "count", "type": "u8"}, {"name": "source", "type": "StackRequestSlotInfo"}, {"name": "destination", "type": "StackRequestSlotInfo"}]], "place": ["container", [{"name": "count", "type": "u8"}, {"name": "source", "type": "StackRequestSlotInfo"}, {"name": "destination", "type": "StackRequestSlotInfo"}]], "swap": ["container", [{"name": "source", "type": "StackRequestSlotInfo"}, {"name": "destination", "type": "StackRequestSlotInfo"}]], "drop": ["container", [{"name": "count", "type": "u8"}, {"name": "source", "type": "StackRequestSlotInfo"}, {"name": "randomly", "type": "bool"}]], "destroy": ["container", [{"name": "count", "type": "u8"}, {"name": "source", "type": "StackRequestSlotInfo"}]], "consume": ["container", [{"name": "count", "type": "u8"}, {"name": "source", "type": "StackRequestSlotInfo"}]], "create": ["container", [{"name": "result_slot_id", "type": "u8"}]], "beacon_payment": ["container", [{"name": "primary_effect", "type": "zigzag32"}, {"name": "secondary_effect", "type": "zigzag32"}]], "mine_block": ["container", [{"name": "hotbar_slot", "type": "zigzag32"}, {"name": "predicted_durability", "type": "zigzag32"}, {"name": "network_id", "type": "zigzag32"}]], "craft_recipe": ["container", [{"name": "recipe_network_id", "type": "varint"}]], "craft_recipe_auto": ["container", [{"name": "recipe_network_id", "type": "varint"}, {"name": "times_crafted", "type": "u8"}]], "craft_creative": ["container", [{"name": "item_id", "type": "varint"}]], "optional": ["container", [{"name": "recipe_network_id", "type": "varint"}, {"name": "filtered_string_index", "type": "li32"}]], "craft_grindstone_request": ["container", [{"name": "recipe_network_id", "type": "varint"}, {"name": "cost", "type": "varint"}]], "craft_loom_request": ["container", [{"name": "pattern", "type": "string"}]], "non_implemented": "void", "results_deprecated": ["container", [{"name": "result_items", "type": ["array", {"countType": "varint", "type": "ItemLegacy"}]}, {"name": "times_crafted", "type": "u8"}]]}}]}]]}]}, {"name": "custom_names", "type": ["array", {"countType": "varint", "type": "string"}]}]], "ItemStackResponses": ["array", {"countType": "varint", "type": ["container", [{"name": "status", "type": ["mapper", {"type": "u8", "mappings": {"0": "ok", "1": "error"}}]}, {"name": "request_id", "type": "zigzag32"}, {"anon": true, "type": ["switch", {"compareTo": "status", "fields": {"ok": ["container", [{"name": "containers", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "slot_type", "type": "ContainerSlotType"}, {"name": "slots", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "slot", "type": "u8"}, {"name": "hotbar_slot", "type": "u8"}, {"name": "count", "type": "u8"}, {"name": "item_stack_id", "type": "zigzag32"}, {"name": "custom_name", "type": "string"}, {"name": "durability_correction", "type": "zigzag32"}]]}]}]]}]}]]}}]}]]}], "ItemComponentList": ["array", {"countType": "varint", "type": ["container", [{"name": "name", "type": "string"}, {"name": "nbt", "type": "nbt"}]]}], "CommandOrigin": ["container", [{"name": "type", "type": ["mapper", {"type": "varint", "mappings": {"0": "player", "1": "block", "2": "minecart_block", "3": "dev_console", "4": "test", "5": "automation_player", "6": "client_automation", "7": "dedicated_server", "8": "entity", "9": "virtual", "10": "game_argument", "11": "entity_server", "12": "precompiled", "13": "game_director_entity_server", "14": "script"}}]}, {"name": "uuid", "type": "uuid"}, {"name": "request_id", "type": "string"}, {"name": "player_entity_id", "type": ["switch", {"compareTo": "type", "fields": {"dev_console": ["container", [{"name": "player_entity_id", "type": "zigzag64"}]], "test": ["container", [{"name": "player_entity_id", "type": "zigzag64"}]]}}]}]], "TrackedObject": ["container", [{"name": "type", "type": ["mapper", {"type": "li32", "mappings": {"0": "entity", "1": "block"}}]}, {"name": "entity_unique_id", "type": ["switch", {"compareTo": "type", "fields": {"entity": "zigzag64"}}]}, {"name": "block_position", "type": ["switch", {"compareTo": "type", "fields": {"block": "BlockCoordinates"}}]}]], "MapDecoration": ["container", [{"name": "type", "type": ["mapper", {"type": "u8", "mappings": {"0": "marker_white", "1": "marker_green", "2": "marker_red", "3": "marker_blue", "4": "cross_white", "5": "triangle_red", "6": "square_white", "7": "marker_sign", "8": "marker_pink", "9": "marker_orange", "10": "marker_yellow", "11": "marker_teal", "12": "triangle_green", "13": "small_square_white", "14": "mansion", "15": "monument", "16": "no_draw", "17": "village_desert", "18": "village_plains", "19": "village_savanna", "20": "village_snowy", "21": "village_taiga", "22": "jungle_temple", "23": "witch_hut"}}]}, {"name": "rotation", "type": "u8"}, {"name": "x", "type": "u8"}, {"name": "y", "type": "u8"}, {"name": "label", "type": "string"}, {"name": "color_abgr", "type": "varint"}]], "StructureBlockSettings": ["container", [{"name": "palette_name", "type": "string"}, {"name": "ignore_entities", "type": "bool"}, {"name": "ignore_blocks", "type": "bool"}, {"name": "size", "type": "BlockCoordinates"}, {"name": "structure_offset", "type": "BlockCoordinates"}, {"name": "last_editing_player_unique_id", "type": "zigzag64"}, {"name": "rotation", "type": ["mapper", {"type": "u8", "mappings": {"0": "none", "1": "90_deg", "2": "180_deg", "3": "270_deg"}}]}, {"name": "mirror", "type": ["mapper", {"type": "u8", "mappings": {"0": "none", "1": "x_axis", "2": "z_axis", "3": "both_axes"}}]}, {"name": "animation_mode", "type": ["mapper", {"type": "u8", "mappings": {"0": "none", "1": "layers", "2": "blocks"}}]}, {"name": "animation_duration", "type": "lf32"}, {"name": "integrity", "type": "lf32"}, {"name": "seed", "type": "lu32"}, {"name": "pivot", "type": "vec3f"}]], "EducationSharedResourceURI": ["container", [{"name": "button_name", "type": "string"}, {"name": "link_uri", "type": "string"}]], "EducationExternalLinkSettings": ["container", [{"name": "url", "type": "string"}, {"name": "display_name", "type": "string"}]], "BlockUpdate": ["container", [{"name": "position", "type": "BlockCoordinates"}, {"name": "runtime_id", "type": "varint"}, {"name": "flags", "type": "varint"}, {"name": "entity_unique_id", "type": "zigzag64"}, {"name": "transition_type", "type": ["mapper", {"type": "varint", "mappings": {"0": "entity", "1": "create", "2": "destroy"}}]}]], "MaterialReducer": ["container", [{"name": "mix", "type": "zigzag32"}, {"name": "items", "type": ["container", [{"name": "network_id", "type": "zigzag32"}, {"name": "count", "type": "zigzag32"}]]}]], "WindowID": ["mapper", {"type": "i8", "mappings": {"0": "inventory", "1": "first", "100": "last", "119": "offhand", "120": "armor", "121": "creative", "122": "hotbar", "123": "fixed_inventory", "124": "ui", "-100": "drop_contents", "-24": "beacon", "-23": "trading_output", "-22": "trading_use_inputs", "-21": "trading_input_2", "-20": "trading_input_1", "-17": "enchant_output", "-16": "enchant_material", "-15": "enchant_input", "-13": "anvil_output", "-12": "anvil_result", "-11": "anvil_material", "-10": "container_input", "-5": "crafting_use_ingredient", "-4": "crafting_result", "-3": "crafting_remove_ingredient", "-2": "crafting_add_ingredient", "-1": "none"}}], "WindowIDVarint": ["mapper", {"type": "varint", "mappings": {"0": "inventory", "1": "first", "100": "last", "119": "offhand", "120": "armor", "121": "creative", "122": "hotbar", "123": "fixed_inventory", "124": "ui", "-100": "drop_contents", "-24": "beacon", "-23": "trading_output", "-22": "trading_use_inputs", "-21": "trading_input_2", "-20": "trading_input_1", "-17": "enchant_output", "-16": "enchant_material", "-15": "enchant_input", "-13": "anvil_output", "-12": "anvil_result", "-11": "anvil_material", "-10": "container_input", "-5": "crafting_use_ingredient", "-4": "crafting_result", "-3": "crafting_remove_ingredient", "-2": "crafting_add_ingredient", "-1": "none"}}], "WindowType": ["mapper", {"type": "i8", "mappings": {"0": "container", "1": "workbench", "2": "furnace", "3": "enchantment", "4": "brewing_stand", "5": "anvil", "6": "dispenser", "7": "dropper", "8": "hopper", "9": "cauldron", "10": "minecart_chest", "11": "minecart_hopper", "12": "horse", "13": "beacon", "14": "structure_editor", "15": "trading", "16": "command_block", "17": "jukebox", "18": "armor", "19": "hand", "20": "compound_creator", "21": "element_constructor", "22": "material_reducer", "23": "lab_table", "24": "loom", "25": "lectern", "26": "grindstone", "27": "blast_furnace", "28": "smoker", "29": "stonecutter", "30": "cartography", "31": "hud", "32": "jigsaw_editor", "33": "smithing_table", "-9": "none", "-1": "inventory"}}], "ContainerSlotType": ["mapper", {"type": "u8", "mappings": {"0": "anvil_input", "1": "anvil_material", "2": "anvil_result", "3": "smithing_table_input", "4": "smithing_table_material", "5": "smithing_table_result", "6": "armor", "7": "container", "8": "beacon_payment", "9": "brewing_input", "10": "brewing_result", "11": "brewing_fuel", "12": "hotbar_and_inventory", "13": "crafting_input", "14": "crafting_output", "15": "recipe_construction", "16": "recipe_nature", "17": "recipe_items", "18": "recipe_search", "19": "recipe_search_bar", "20": "recipe_equipment", "21": "enchanting_input", "22": "enchanting_lapis", "23": "furnace_fuel", "24": "furnace_ingredient", "25": "furnace_output", "26": "horse_equip", "27": "hotbar", "28": "inventory", "29": "s<PERSON><PERSON>", "30": "trade_ingredient1", "31": "trade_ingredient2", "32": "trade_result", "33": "offhand", "34": "compcreate_input", "35": "compcreate_output", "36": "elemconstruct_output", "37": "matreduce_input", "38": "matreduce_output", "39": "labtable_input", "40": "loom_input", "41": "loom_dye", "42": "loom_material", "43": "loom_result", "44": "blast_furnace_ingredient", "45": "smoker_ingredient", "46": "trade2_ingredient1", "47": "trade2_ingredient2", "48": "trade2_result", "49": "grindstone_input", "50": "grindstone_additional", "51": "grindstone_result", "52": "stonecutter_input", "53": "stonecutter_result", "54": "cartography_input", "55": "cartography_additional", "56": "cartography_result", "57": "barrel", "58": "cursor", "59": "creative_output"}}], "SoundType": ["mapper", {"type": "varint", "mappings": {"0": "ItemUseOn", "1": "Hit", "2": "Step", "3": "Fly", "4": "Jump", "5": "Break", "6": "Place", "7": "HeavyStep", "8": "Gallop", "9": "Fall", "10": "Ambient", "11": "AmbientBaby", "12": "AmbientInWater", "13": "Breathe", "14": "Death", "15": "DeathInWater", "16": "DeathToZombie", "17": "Hurt", "18": "HurtInWater", "19": "Mad", "20": "Boost", "21": "Bow", "22": "SquishBig", "23": "SquishSmall", "24": "FallBig", "25": "FallSmall", "26": "Splash", "27": "Fizz", "28": "Flap", "29": "Swim", "30": "Drink", "31": "Eat", "32": "Takeoff", "33": "Shake", "34": "<PERSON><PERSON>", "35": "Land", "36": "Saddle", "37": "Armor", "38": "MobArmorStandPlace", "39": "AddChest", "40": "<PERSON>hrow", "41": "Attack", "42": "AttackNoDamage", "43": "AttackStrong", "44": "<PERSON><PERSON>", "45": "Shear", "46": "Milk", "47": "Thunder", "48": "Explode", "49": "Fire", "50": "Ignite", "51": "<PERSON><PERSON>", "52": "Stare", "53": "Spawn", "54": "Shoot", "55": "BreakBlock", "56": "Launch", "57": "Blast", "58": "LargeBlast", "59": "Twinkle", "60": "<PERSON><PERSON><PERSON>", "61": "Infect", "62": "LevelUp", "63": "BowHit", "64": "BulletHit", "65": "ExtinguishFire", "66": "ItemFizz", "67": "ChestOpen", "68": "ChestClosed", "69": "ShulkerBoxOpen", "70": "ShulkerBoxClosed", "71": "EnderChestOpen", "72": "EnderChestClosed", "73": "PowerOn", "74": "PowerOff", "75": "Attach", "76": "<PERSON><PERSON>", "77": "<PERSON><PERSON>", "78": "<PERSON><PERSON>", "79": "Pop", "80": "DropSlot", "81": "Note", "82": "Thorns", "83": "PistonIn", "84": "PistonOut", "85": "Portal", "86": "Water", "87": "LavaPop", "88": "<PERSON><PERSON>", "89": "<PERSON><PERSON><PERSON>", "90": "BucketFillWater", "91": "BucketFillLava", "92": "BucketEmptyWater", "93": "BucketEmptyLava", "94": "ArmorEquipChain", "95": "ArmorEquipDiamond", "96": "ArmorEquipGeneric", "97": "ArmorEquipGold", "98": "ArmorEquipIron", "99": "ArmorEquipLeather", "100": "ArmorEquipElytra", "101": "Record13", "102": "RecordCat", "103": "RecordBlocks", "104": "RecordChirp", "105": "RecordFar", "106": "RecordMall", "107": "RecordMellohi", "108": "RecordStal", "109": "RecordStrad", "110": "RecordWard", "111": "Record11", "112": "RecordWait", "113": "StopRecord", "114": "<PERSON>lop", "115": "GuardianCurse", "116": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "117": "MobWarningBaby", "118": "Teleport", "119": "ShulkerOpen", "120": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "121": "Haggle", "122": "Haggle<PERSON>es", "123": "HaggleNo", "124": "HaggleIdle", "125": "Chorus<PERSON>row", "126": "<PERSON><PERSON><PERSON><PERSON>", "127": "Glass", "128": "PotionBrewed", "129": "CastSpell", "130": "PrepareAttackSpell", "131": "Prepare<PERSON><PERSON><PERSON>", "132": "Prepare<PERSON><PERSON><PERSON>", "133": "<PERSON>", "134": "Charge", "135": "CameraTakePicture", "136": "LeashKnotPlace", "137": "LeashKnotBreak", "138": "AmbientGrowl", "139": "AmbientWhine", "140": "AmbientPant", "141": "AmbientPurr", "142": "AmbientPurreow", "143": "DeathMinVolume", "144": "DeathMidVolume", "145": "ImitateBlaze", "146": "ImitateCave<PERSON><PERSON><PERSON>", "147": "ImitateCreeper", "148": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "149": "ImitateEnderDragon", "150": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "151": "ImitateEndermite", "152": "ImitateEvocationIllager", "153": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "154": "ImitateHusk", "155": "ImitateIllusionIllager", "156": "ImitateMagmaCube", "157": "ImitatePolarBear", "158": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "159": "ImitateSilverfish", "160": "ImitateSkeleton", "161": "ImitateSlime", "162": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "163": "ImitateStray", "164": "ImitateVex", "165": "ImitateVindicationIllager", "166": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "167": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "168": "ImitateWitherSkeleton", "169": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "170": "ImitateZ<PERSON><PERSON>", "171": "ImitateZombiePigman", "172": "ImitateZombieVillager", "173": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "174": "EndPortalCreated", "175": "AnvilUse", "176": "BottleDragonBreath", "177": "PortalTravel", "178": "TridentHit", "179": "TridentReturn", "180": "TridentRiptide1", "181": "TridentRiptide2", "182": "TridentRiptide3", "183": "TridentThrow", "184": "TridentThunder", "185": "TridentHitGround", "186": "<PERSON><PERSON><PERSON>", "187": "FletchingTableUse", "188": "ElemConstructOpen", "189": "IceBombHit", "190": "BalloonPop", "191": "LtReactionIceBomb", "192": "LtReactionBleach", "193": "LtReactionElephantToothpaste", "194": "LtReactionElephantToothpaste2", "195": "LtReactionGlowStick", "196": "LtReactionGlowStick2", "197": "LtReactionLuminol", "198": "LtReactionSalt", "199": "LtReactionFertilizer", "200": "LtReactionFireball", "201": "LtReactionMagnesiumSalt", "202": "LtReactionMiscFire", "203": "LtReactionFire", "204": "LtReactionMiscExplosion", "205": "LtReactionMiscMystical", "206": "LtReactionMiscMystical2", "207": "LtReactionProduct", "208": "SparklerUse", "209": "GlowStickUse", "210": "SparklerActive", "211": "ConvertToDrowned", "212": "BucketFillFish", "213": "BucketEmptyFish", "214": "BubbleColumnUpwards", "215": "BubbleColumnDownwards", "216": "BubblePop", "217": "BubbleUpInside", "218": "BubbleDownInside", "219": "<PERSON><PERSON><PERSON>", "220": "DeathB<PERSON>", "221": "<PERSON><PERSON><PERSON>", "222": "SpawnBaby", "223": "Born", "224": "TurtleEggBreak", "225": "TurtleEggCrack", "226": "TurtleEggHatched", "227": "<PERSON><PERSON><PERSON>", "228": "TurtleEggAttacked", "229": "BeaconActivate", "230": "BeaconAmbient", "231": "BeaconDeactivate", "232": "BeaconPower", "233": "ConduitActivate", "234": "ConduitAmbient", "235": "ConduitAttack", "236": "ConduitDeactivate", "237": "ConduitShort", "238": "Swoop", "239": "BambooSaplingPlace", "240": "<PERSON><PERSON><PERSON><PERSON>", "241": "Sneeze", "242": "AmbientTame", "243": "Scared", "244": "ScaffoldingClimb", "245": "CrossbowLoadingStart", "246": "CrossbowLoadingMiddle", "247": "CrossbowLoadingEnd", "248": "CrossbowShoot", "249": "CrossbowQuickChargeStart", "250": "CrossbowQuickChargeMiddle", "251": "CrossbowQuickChargeEnd", "252": "AmbientAggressive", "253": "AmbientWorried", "254": "CantBreed", "255": "ShieldBlock", "256": "LecternBookPlace", "257": "GrindstoneUse", "258": "Bell", "259": "CampfireCrackle", "260": "Roar", "261": "<PERSON><PERSON>", "262": "SweetBerryBushHurt", "263": "<PERSON><PERSON><PERSON><PERSON>ush<PERSON>ick", "264": "CartographyTableUse", "265": "StonecutterUse", "266": "ComposterEmpty", "267": "ComposterFill", "268": "ComposterFillLayer", "269": "ComposterReady", "270": "BarrelOpen", "271": "BarrelClose", "272": "RaidHorn", "273": "LoomUse", "274": "AmbientInRaid", "275": "UICartographyTableUse", "276": "UIStonecutterUse", "277": "UILoomUse", "278": "SmokerUse", "279": "BlastFurnaceUse", "280": "SmithingTableUse", "281": "Screech", "282": "Sleep", "283": "FurnaceUse", "284": "MooshroomConvert", "285": "MilkSuspiciously", "286": "Celebrate", "287": "JumpPrevent", "288": "AmbientPollinate", "289": "BeehiveDrip", "290": "BeehiveEnter", "291": "BeehiveExit", "292": "BeehiveWork", "293": "BeehiveShear", "294": "HoneybottleDrink", "295": "AmbientCave", "296": "Retreat", "297": "ConvertToZombified", "298": "<PERSON><PERSON><PERSON>", "299": "StepLava", "300": "Tempt", "301": "Panic", "302": "Angry", "303": "AmbientMoodWarpedForest", "304": "AmbientMoodSoulsandValley", "305": "AmbientMoodNetherWastes", "306": "AmbientMoodBasaltDeltas", "307": "AmbientMoodCrimsonForest", "308": "RespawnAnchorCharge", "309": "RespawnAnchorDeplete", "310": "RespawnAnchorSetSpawn", "311": "RespawnAnchorAmbient", "312": "SoulEscapeQuiet", "313": "SoulEscapeLoud", "314": "RecordPigstep", "315": "LinkCompassToLodestone", "316": "UseSmithingTable", "317": "EquipNetherite", "318": "AmbientLoopWarpedForest", "319": "AmbientLoopSoulsandValley", "320": "AmbientLoopNetherWastes", "321": "AmbientLoopBasaltDeltas", "322": "AmbientLoopCrimsonForest", "323": "AmbientAdditionWarpedForest", "324": "AmbientAdditionSoulsandValley", "325": "AmbientAdditionNetherWastes", "326": "AmbientAdditionBasaltDeltas", "327": "AmbientAdditionCrimsonForest", "328": "SculkSensorPowerOn", "329": "SculkSensorPowerOff", "330": "BucketFillPowderSnow", "331": "BucketEmptyPowderSnow", "332": "PointedDripstoneCauldronDripWater", "333": "PointedDripstoneCauldronDripLava", "334": "PointedDripstoneDripWater", "335": "PointedDripstoneDripLava", "336": "CaveVinesPickBerries", "337": "BigDripleafTiltDown", "338": "BigDripleafTiltUp", "339": "CopperWaxOn", "340": "CopperWaxOff", "341": "Scrape", "342": "Player<PERSON>urtDrown", "343": "PlayerHurtOnFire", "344": "PlayerHurtFreeze", "345": "UseSpyglass", "346": "StopUsingSpyglass", "347": "AmethystBlockChime", "348": "AmbientScreamer", "349": "HurtScreamer", "350": "DeathScreamer", "351": "MilkScreamer", "352": "Jump<PERSON>oBlock", "353": "PreRam", "354": "PreRamScreamer", "355": "RamImpact", "356": "RamImpactScreamer", "357": "SquidInkSquirt", "358": "GlowSquidInkSquirt", "359": "ConvertToStray", "360": "CakeAddCandle", "361": "ExtinguishCandle", "362": "AmbientCandle", "363": "BlockClick", "364": "BlockClickFail", "365": "SculkCatalystBloom", "366": "SculkShriekerShriek", "367": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "368": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "369": "WardenNearbyClosest", "370": "Warden<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "371": "RecordOtherside", "372": "Undefined"}}], "LegacyEntityType": ["mapper", {"type": "li32", "mappings": {"10": "chicken", "11": "cow", "12": "pig", "13": "sheep", "14": "wolf", "15": "villager", "16": "mooshroom", "17": "squid", "18": "rabbit", "19": "bat", "20": "iron_golem", "21": "snow_golem", "22": "ocelot", "23": "horse", "24": "donkey", "25": "mule", "26": "skeleton_horse", "27": "zombie_horse", "28": "polar_bear", "29": "llama", "30": "parrot", "31": "dolphin", "32": "zombie", "33": "creeper", "34": "skeleton", "35": "spider", "36": "zombie_pigman", "37": "slime", "38": "enderman", "39": "silverfish", "40": "cave_spider", "41": "ghast", "42": "magma_cube", "43": "blaze", "44": "zombie_villager", "45": "witch", "46": "stray", "47": "husk", "48": "wither_skeleton", "49": "guardian", "50": "elder_guardian", "51": "npc", "52": "wither", "53": "ender_dragon", "54": "s<PERSON><PERSON>", "55": "endermite", "56": "agent", "57": "vindicator", "58": "phantom", "61": "armor_stand", "62": "tripod_camera", "63": "player", "64": "item", "65": "tnt", "66": "falling_block", "67": "moving_block", "68": "xp_bottle", "69": "xp_orb", "70": "eye_of_ender_signal", "71": "ender_crystal", "72": "fireworks_rocket", "73": "thrown_trident", "74": "turtle", "75": "cat", "76": "shulker_bullet", "77": "fishing_hook", "78": "chalkboard", "79": "dragon_fireball", "80": "arrow", "81": "snowball", "82": "egg", "83": "painting", "84": "minecart", "85": "fireball", "86": "splash_potion", "87": "ender_pearl", "88": "leash_knot", "89": "wither_skull", "90": "boat", "91": "wither_skull_dangerous", "93": "lightning_bolt", "94": "small_fireball", "95": "area_effect_cloud", "96": "hopper_minecart", "97": "tnt_minecart", "98": "chest_minecart", "100": "command_block_minecart", "101": "lingering_potion", "102": "llama_spit", "103": "evocation_fang", "104": "evocation_illager", "105": "vex", "106": "ice_bomb", "107": "balloon", "108": "pufferfish", "109": "salmon", "110": "drowned", "111": "tropicalfish", "112": "cod", "113": "panda"}}], "mcpe_packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"1": "login", "2": "play_status", "3": "server_to_client_handshake", "4": "client_to_server_handshake", "5": "disconnect", "6": "resource_packs_info", "7": "resource_pack_stack", "8": "resource_pack_client_response", "9": "text", "10": "set_time", "11": "start_game", "12": "add_player", "13": "add_entity", "14": "remove_entity", "15": "add_item_entity", "17": "take_item_entity", "18": "move_entity", "19": "move_player", "20": "rider_jump", "21": "update_block", "22": "add_painting", "23": "tick_sync", "24": "level_sound_event_old", "25": "level_event", "26": "block_event", "27": "entity_event", "28": "mob_effect", "29": "update_attributes", "30": "inventory_transaction", "31": "mob_equipment", "32": "mob_armor_equipment", "33": "interact", "34": "block_pick_request", "35": "entity_pick_request", "36": "player_action", "38": "hurt_armor", "39": "set_entity_data", "40": "set_entity_motion", "41": "set_entity_link", "42": "set_health", "43": "set_spawn_position", "44": "animate", "45": "respawn", "46": "container_open", "47": "container_close", "48": "player_hotbar", "49": "inventory_content", "50": "inventory_slot", "51": "container_set_data", "52": "crafting_data", "53": "crafting_event", "54": "gui_data_pick_item", "55": "adventure_settings", "56": "block_entity_data", "57": "player_input", "58": "level_chunk", "59": "set_commands_enabled", "60": "set_difficulty", "61": "change_dimension", "62": "set_player_game_type", "63": "player_list", "64": "simple_event", "65": "event", "66": "spawn_experience_orb", "67": "clientbound_map_item_data", "68": "map_info_request", "69": "request_chunk_radius", "70": "chunk_radius_update", "71": "item_frame_drop_item", "72": "game_rules_changed", "73": "camera", "74": "boss_event", "75": "show_credits", "76": "available_commands", "77": "command_request", "78": "command_block_update", "79": "command_output", "80": "update_trade", "81": "update_equipment", "82": "resource_pack_data_info", "83": "resource_pack_chunk_data", "84": "resource_pack_chunk_request", "85": "transfer", "86": "play_sound", "87": "stop_sound", "88": "set_title", "89": "add_behavior_tree", "90": "structure_block_update", "91": "show_store_offer", "92": "purchase_receipt", "93": "player_skin", "94": "sub_client_login", "95": "initiate_web_socket_connection", "96": "set_last_hurt_by", "97": "book_edit", "98": "npc_request", "99": "photo_transfer", "100": "modal_form_request", "101": "modal_form_response", "102": "server_settings_request", "103": "server_settings_response", "104": "show_profile", "105": "set_default_game_type", "106": "remove_objective", "107": "set_display_objective", "108": "set_score", "109": "lab_table", "110": "update_block_synced", "111": "move_entity_delta", "112": "set_scoreboard_identity", "113": "set_local_player_as_initialized", "114": "update_soft_enum", "115": "network_stack_latency", "117": "script_custom_event", "118": "spawn_particle_effect", "119": "available_entity_identifiers", "120": "level_sound_event_v2", "121": "network_chunk_publisher_update", "122": "biome_definition_list", "123": "level_sound_event", "124": "level_event_generic", "125": "lectern_update", "126": "video_stream_connect", "127": "add_ecs_entity", "128": "remove_ecs_entity", "129": "client_cache_status", "130": "on_screen_texture_animation", "131": "map_create_locked_copy", "132": "structure_template_data_export_request", "133": "structure_template_data_export_response", "134": "update_block_properties", "135": "client_cache_blob_status", "136": "client_cache_miss_response", "137": "education_settings", "138": "emote", "139": "multiplayer_settings", "140": "settings_command", "141": "anvil_damage", "142": "completed_using_item", "143": "network_settings", "144": "player_auth_input", "145": "creative_content", "146": "player_enchant_options", "147": "item_stack_request", "148": "item_stack_response", "149": "player_armor_damage", "151": "update_player_game_type", "152": "emote_list", "153": "position_tracking_db_broadcast", "154": "position_tracking_db_request", "156": "packet_violation_warning", "157": "motion_prediction_hints", "158": "animate_entity", "159": "camera_shake", "160": "player_fog", "161": "correct_player_move_prediction", "162": "item_component", "163": "filter_text_packet", "164": "debug_renderer", "165": "sync_entity_property", "166": "add_volume_entity", "167": "remove_volume_entity", "168": "simulation_type", "169": "npc_dialogue", "170": "edu_uri_resource_packet", "171": "create_photo", "172": "update_subchunk_blocks", "173": "photo_info_request", "174": "subchunk", "175": "subchunk_request"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"login": "packet_login", "play_status": "packet_play_status", "server_to_client_handshake": "packet_server_to_client_handshake", "client_to_server_handshake": "packet_client_to_server_handshake", "disconnect": "packet_disconnect", "resource_packs_info": "packet_resource_packs_info", "resource_pack_stack": "packet_resource_pack_stack", "resource_pack_client_response": "packet_resource_pack_client_response", "text": "packet_text", "set_time": "packet_set_time", "start_game": "packet_start_game", "add_player": "packet_add_player", "add_entity": "packet_add_entity", "remove_entity": "packet_remove_entity", "add_item_entity": "packet_add_item_entity", "take_item_entity": "packet_take_item_entity", "move_entity": "packet_move_entity", "move_player": "packet_move_player", "rider_jump": "packet_rider_jump", "update_block": "packet_update_block", "add_painting": "packet_add_painting", "tick_sync": "packet_tick_sync", "level_sound_event_old": "packet_level_sound_event_old", "level_event": "packet_level_event", "block_event": "packet_block_event", "entity_event": "packet_entity_event", "mob_effect": "packet_mob_effect", "update_attributes": "packet_update_attributes", "inventory_transaction": "packet_inventory_transaction", "mob_equipment": "packet_mob_equipment", "mob_armor_equipment": "packet_mob_armor_equipment", "interact": "packet_interact", "block_pick_request": "packet_block_pick_request", "entity_pick_request": "packet_entity_pick_request", "player_action": "packet_player_action", "hurt_armor": "packet_hurt_armor", "set_entity_data": "packet_set_entity_data", "set_entity_motion": "packet_set_entity_motion", "set_entity_link": "packet_set_entity_link", "set_health": "packet_set_health", "set_spawn_position": "packet_set_spawn_position", "animate": "packet_animate", "respawn": "packet_respawn", "container_open": "packet_container_open", "container_close": "packet_container_close", "player_hotbar": "packet_player_hotbar", "inventory_content": "packet_inventory_content", "inventory_slot": "packet_inventory_slot", "container_set_data": "packet_container_set_data", "crafting_data": "packet_crafting_data", "crafting_event": "packet_crafting_event", "gui_data_pick_item": "packet_gui_data_pick_item", "adventure_settings": "packet_adventure_settings", "block_entity_data": "packet_block_entity_data", "player_input": "packet_player_input", "level_chunk": "packet_level_chunk", "set_commands_enabled": "packet_set_commands_enabled", "set_difficulty": "packet_set_difficulty", "change_dimension": "packet_change_dimension", "set_player_game_type": "packet_set_player_game_type", "player_list": "packet_player_list", "simple_event": "packet_simple_event", "event": "packet_event", "spawn_experience_orb": "packet_spawn_experience_orb", "clientbound_map_item_data": "packet_clientbound_map_item_data", "map_info_request": "packet_map_info_request", "request_chunk_radius": "packet_request_chunk_radius", "chunk_radius_update": "packet_chunk_radius_update", "item_frame_drop_item": "packet_item_frame_drop_item", "game_rules_changed": "packet_game_rules_changed", "camera": "packet_camera", "boss_event": "packet_boss_event", "show_credits": "packet_show_credits", "available_commands": "packet_available_commands", "command_request": "packet_command_request", "command_block_update": "packet_command_block_update", "command_output": "packet_command_output", "update_trade": "packet_update_trade", "update_equipment": "packet_update_equipment", "resource_pack_data_info": "packet_resource_pack_data_info", "resource_pack_chunk_data": "packet_resource_pack_chunk_data", "resource_pack_chunk_request": "packet_resource_pack_chunk_request", "transfer": "packet_transfer", "play_sound": "packet_play_sound", "stop_sound": "packet_stop_sound", "set_title": "packet_set_title", "add_behavior_tree": "packet_add_behavior_tree", "structure_block_update": "packet_structure_block_update", "show_store_offer": "packet_show_store_offer", "purchase_receipt": "packet_purchase_receipt", "player_skin": "packet_player_skin", "sub_client_login": "packet_sub_client_login", "initiate_web_socket_connection": "packet_initiate_web_socket_connection", "set_last_hurt_by": "packet_set_last_hurt_by", "book_edit": "packet_book_edit", "npc_request": "packet_npc_request", "photo_transfer": "packet_photo_transfer", "modal_form_request": "packet_modal_form_request", "modal_form_response": "packet_modal_form_response", "server_settings_request": "packet_server_settings_request", "server_settings_response": "packet_server_settings_response", "show_profile": "packet_show_profile", "set_default_game_type": "packet_set_default_game_type", "remove_objective": "packet_remove_objective", "set_display_objective": "packet_set_display_objective", "set_score": "packet_set_score", "lab_table": "packet_lab_table", "update_block_synced": "packet_update_block_synced", "move_entity_delta": "packet_move_entity_delta", "set_scoreboard_identity": "packet_set_scoreboard_identity", "set_local_player_as_initialized": "packet_set_local_player_as_initialized", "update_soft_enum": "packet_update_soft_enum", "network_stack_latency": "packet_network_stack_latency", "script_custom_event": "packet_script_custom_event", "spawn_particle_effect": "packet_spawn_particle_effect", "available_entity_identifiers": "packet_available_entity_identifiers", "level_sound_event_v2": "packet_level_sound_event_v2", "network_chunk_publisher_update": "packet_network_chunk_publisher_update", "biome_definition_list": "packet_biome_definition_list", "level_sound_event": "packet_level_sound_event", "level_event_generic": "packet_level_event_generic", "lectern_update": "packet_lectern_update", "video_stream_connect": "packet_video_stream_connect", "add_ecs_entity": "packet_add_ecs_entity", "remove_ecs_entity": "packet_remove_ecs_entity", "client_cache_status": "packet_client_cache_status", "on_screen_texture_animation": "packet_on_screen_texture_animation", "map_create_locked_copy": "packet_map_create_locked_copy", "structure_template_data_export_request": "packet_structure_template_data_export_request", "structure_template_data_export_response": "packet_structure_template_data_export_response", "update_block_properties": "packet_update_block_properties", "client_cache_blob_status": "packet_client_cache_blob_status", "client_cache_miss_response": "packet_client_cache_miss_response", "education_settings": "packet_education_settings", "emote": "packet_emote", "multiplayer_settings": "packet_multiplayer_settings", "settings_command": "packet_settings_command", "anvil_damage": "packet_anvil_damage", "completed_using_item": "packet_completed_using_item", "network_settings": "packet_network_settings", "player_auth_input": "packet_player_auth_input", "creative_content": "packet_creative_content", "player_enchant_options": "packet_player_enchant_options", "item_stack_request": "packet_item_stack_request", "item_stack_response": "packet_item_stack_response", "player_armor_damage": "packet_player_armor_damage", "update_player_game_type": "packet_update_player_game_type", "emote_list": "packet_emote_list", "position_tracking_db_request": "packet_position_tracking_db_request", "position_tracking_db_broadcast": "packet_position_tracking_db_broadcast", "packet_violation_warning": "packet_packet_violation_warning", "motion_prediction_hints": "packet_motion_prediction_hints", "animate_entity": "packet_animate_entity", "camera_shake": "packet_camera_shake", "player_fog": "packet_player_fog", "correct_player_move_prediction": "packet_correct_player_move_prediction", "item_component": "packet_item_component", "filter_text_packet": "packet_filter_text_packet", "debug_renderer": "packet_debug_renderer", "sync_entity_property": "packet_sync_entity_property", "add_volume_entity": "packet_add_volume_entity", "remove_volume_entity": "packet_remove_volume_entity", "simulation_type": "packet_simulation_type", "npc_dialogue": "packet_npc_dialogue", "edu_uri_resource_packet": "packet_edu_uri_resource_packet", "create_photo": "packet_create_photo", "update_subchunk_blocks": "packet_update_subchunk_blocks", "photo_info_request": "packet_photo_info_request", "subchunk": "packet_subchunk", "subchunk_request": "packet_subchunk_request"}}]}]], "packet_login": ["container", [{"name": "protocol_version", "type": "i32"}, {"name": "tokens", "type": ["encapsulated", {"lengthType": "varint", "type": "LoginTokens"}]}]], "LoginTokens": ["container", [{"name": "identity", "type": "LittleString"}, {"name": "client", "type": "LittleString"}]], "packet_play_status": ["container", [{"name": "status", "type": ["mapper", {"type": "i32", "mappings": {"0": "login_success", "1": "failed_client", "2": "failed_spawn", "3": "player_spawn", "4": "failed_invalid_tenant", "5": "failed_vanilla_edu", "6": "failed_edu_vanilla", "7": "failed_server_full"}}]}]], "packet_server_to_client_handshake": ["container", [{"name": "token", "type": "string"}]], "packet_client_to_server_handshake": ["container", []], "packet_disconnect": ["container", [{"name": "hide_disconnect_reason", "type": "bool"}, {"name": "message", "type": "string"}]], "packet_resource_packs_info": ["container", [{"name": "must_accept", "type": "bool"}, {"name": "has_scripts", "type": "bool"}, {"name": "force_server_packs", "type": "bool"}, {"name": "behaviour_packs", "type": "BehaviourPackInfos"}, {"name": "texture_packs", "type": "TexturePackInfos"}]], "packet_resource_pack_stack": ["container", [{"name": "must_accept", "type": "bool"}, {"name": "behavior_packs", "type": "ResourcePackIdVersions"}, {"name": "resource_packs", "type": "ResourcePackIdVersions"}, {"name": "game_version", "type": "string"}, {"name": "experiments", "type": "Experiments"}, {"name": "experiments_previously_used", "type": "bool"}]], "packet_resource_pack_client_response": ["container", [{"name": "response_status", "type": ["mapper", {"type": "u8", "mappings": {"0": "none", "1": "refused", "2": "send_packs", "3": "have_all_packs", "4": "completed"}}]}, {"name": "resourcepackids", "type": "ResourcePackIds"}]], "packet_text": ["container", [{"name": "type", "type": ["mapper", {"type": "u8", "mappings": {"0": "raw", "1": "chat", "2": "translation", "3": "popup", "4": "jukebox_popup", "5": "tip", "6": "system", "7": "whisper", "8": "announcement", "9": "json_whisper", "10": "json"}}]}, {"name": "needs_translation", "type": "bool"}, {"anon": true, "type": ["switch", {"compareTo": "type", "fields": {"chat": ["container", [{"name": "source_name", "type": "string"}, {"name": "message", "type": "string"}]], "whisper": ["container", [{"name": "source_name", "type": "string"}, {"name": "message", "type": "string"}]], "announcement": ["container", [{"name": "source_name", "type": "string"}, {"name": "message", "type": "string"}]], "raw": ["container", [{"name": "message", "type": "string"}]], "tip": ["container", [{"name": "message", "type": "string"}]], "system": ["container", [{"name": "message", "type": "string"}]], "json_whisper": ["container", [{"name": "message", "type": "string"}]], "json": ["container", [{"name": "message", "type": "string"}]], "translation": ["container", [{"name": "message", "type": "string"}, {"name": "parameters", "type": ["array", {"countType": "varint", "type": "string"}]}]], "popup": ["container", [{"name": "message", "type": "string"}, {"name": "parameters", "type": ["array", {"countType": "varint", "type": "string"}]}]], "jukebox_popup": ["container", [{"name": "message", "type": "string"}, {"name": "parameters", "type": ["array", {"countType": "varint", "type": "string"}]}]]}}]}, {"name": "xuid", "type": "string"}, {"name": "platform_chat_id", "type": "string"}]], "packet_set_time": ["container", [{"name": "time", "type": "zigzag32"}]], "packet_start_game": ["container", [{"name": "entity_id", "type": "zigzag64"}, {"name": "runtime_entity_id", "type": "varint64"}, {"name": "player_gamemode", "type": "GameMode"}, {"name": "player_position", "type": "vec3f"}, {"name": "rotation", "type": "vec2f"}, {"name": "seed", "type": "zigzag32"}, {"name": "biome_type", "type": "li16"}, {"name": "biome_name", "type": "string"}, {"name": "dimension", "type": "zigzag32"}, {"name": "generator", "type": "zigzag32"}, {"name": "world_gamemode", "type": "GameMode"}, {"name": "difficulty", "type": "zigzag32"}, {"name": "spawn_position", "type": "BlockCoordinates"}, {"name": "achievements_disabled", "type": "bool"}, {"name": "day_cycle_stop_time", "type": "zigzag32"}, {"name": "edu_offer", "type": "zigzag32"}, {"name": "edu_features_enabled", "type": "bool"}, {"name": "edu_product_uuid", "type": "string"}, {"name": "rain_level", "type": "lf32"}, {"name": "lightning_level", "type": "lf32"}, {"name": "has_confirmed_platform_locked_content", "type": "bool"}, {"name": "is_multiplayer", "type": "bool"}, {"name": "broadcast_to_lan", "type": "bool"}, {"name": "xbox_live_broadcast_mode", "type": "varint"}, {"name": "platform_broadcast_mode", "type": "varint"}, {"name": "enable_commands", "type": "bool"}, {"name": "is_texturepacks_required", "type": "bool"}, {"name": "<PERSON><PERSON><PERSON>", "type": "GameRules"}, {"name": "experiments", "type": "Experiments"}, {"name": "experiments_previously_used", "type": "bool"}, {"name": "bonus_chest", "type": "bool"}, {"name": "map_enabled", "type": "bool"}, {"name": "permission_level", "type": "zigzag32"}, {"name": "server_chunk_tick_range", "type": "li32"}, {"name": "has_locked_behavior_pack", "type": "bool"}, {"name": "has_locked_resource_pack", "type": "bool"}, {"name": "is_from_locked_world_template", "type": "bool"}, {"name": "msa_gamertags_only", "type": "bool"}, {"name": "is_from_world_template", "type": "bool"}, {"name": "is_world_template_option_locked", "type": "bool"}, {"name": "only_spawn_v1_villagers", "type": "bool"}, {"name": "game_version", "type": "string"}, {"name": "limited_world_width", "type": "li32"}, {"name": "limited_world_length", "type": "li32"}, {"name": "is_new_nether", "type": "bool"}, {"name": "edu_resource_uri", "type": "EducationSharedResourceURI"}, {"name": "experimental_gameplay_override", "type": "bool"}, {"name": "level_id", "type": "string"}, {"name": "world_name", "type": "string"}, {"name": "premium_world_template_id", "type": "string"}, {"name": "is_trial", "type": "bool"}, {"name": "movement_authority", "type": ["mapper", {"type": "zigzag32", "mappings": {"0": "client", "1": "server", "2": "server_with_rewind"}}]}, {"name": "rewind_history_size", "type": "zigzag32"}, {"name": "server_authoritative_block_breaking", "type": "bool"}, {"name": "current_tick", "type": "li64"}, {"name": "enchantment_seed", "type": "zigzag32"}, {"name": "block_properties", "type": "BlockProperties"}, {"name": "itemstates", "type": "Itemstates"}, {"name": "multiplayer_correlation_id", "type": "string"}, {"name": "server_authoritative_inventory", "type": "bool"}, {"name": "engine", "type": "string"}, {"name": "block_pallete_checksum", "type": "lu64"}]], "packet_add_player": ["container", [{"name": "uuid", "type": "uuid"}, {"name": "username", "type": "string"}, {"name": "entity_id_self", "type": "zigzag64"}, {"name": "runtime_entity_id", "type": "varint64"}, {"name": "platform_chat_id", "type": "string"}, {"name": "position", "type": "vec3f"}, {"name": "velocity", "type": "vec3f"}, {"name": "pitch", "type": "lf32"}, {"name": "yaw", "type": "lf32"}, {"name": "head_yaw", "type": "lf32"}, {"name": "held_item", "type": "<PERSON><PERSON>"}, {"name": "metadata", "type": "MetadataDictionary"}, {"name": "flags", "type": "varint"}, {"name": "command_permission", "type": "varint"}, {"name": "action_permissions", "type": "varint"}, {"name": "permission_level", "type": "varint"}, {"name": "custom_stored_permissions", "type": "varint"}, {"name": "user_id", "type": "li64"}, {"name": "links", "type": "Links"}, {"name": "device_id", "type": "string"}, {"name": "device_os", "type": "li32"}]], "packet_add_entity": ["container", [{"name": "entity_id_self", "type": "zigzag64"}, {"name": "runtime_entity_id", "type": "varint64"}, {"name": "entity_type", "type": "string"}, {"name": "position", "type": "vec3f"}, {"name": "velocity", "type": "vec3f"}, {"name": "pitch", "type": "lf32"}, {"name": "yaw", "type": "lf32"}, {"name": "head_yaw", "type": "lf32"}, {"name": "attributes", "type": "EntityAttributes"}, {"name": "metadata", "type": "MetadataDictionary"}, {"name": "links", "type": "Links"}]], "packet_remove_entity": ["container", [{"name": "entity_id_self", "type": "zigzag64"}]], "packet_add_item_entity": ["container", [{"name": "entity_id_self", "type": "zigzag64"}, {"name": "runtime_entity_id", "type": "varint64"}, {"name": "item", "type": "<PERSON><PERSON>"}, {"name": "position", "type": "vec3f"}, {"name": "velocity", "type": "vec3f"}, {"name": "metadata", "type": "MetadataDictionary"}, {"name": "is_from_fishing", "type": "bool"}]], "packet_take_item_entity": ["container", [{"name": "runtime_entity_id", "type": "varint64"}, {"name": "target", "type": "varint"}]], "packet_move_entity": ["container", [{"name": "runtime_entity_id", "type": "varint64"}, {"name": "flags", "type": "u8"}, {"name": "position", "type": "vec3f"}, {"name": "rotation", "type": "Rotation"}]], "packet_move_player": ["container", [{"name": "runtime_id", "type": "varint"}, {"name": "position", "type": "vec3f"}, {"name": "pitch", "type": "lf32"}, {"name": "yaw", "type": "lf32"}, {"name": "head_yaw", "type": "lf32"}, {"name": "mode", "type": ["mapper", {"type": "u8", "mappings": {"0": "normal", "1": "reset", "2": "teleport", "3": "rotation"}}]}, {"name": "on_ground", "type": "bool"}, {"name": "ridden_runtime_id", "type": "varint"}, {"name": "teleport", "type": ["switch", {"compareTo": "mode", "fields": {"teleport": ["container", [{"name": "cause", "type": ["mapper", {"type": "li32", "mappings": {"0": "unknown", "1": "projectile", "2": "chorus_fruit", "3": "command", "4": "behavior"}}]}, {"name": "source_entity_type", "type": "LegacyEntityType"}]]}}]}, {"name": "tick", "type": "varint64"}]], "packet_rider_jump": ["container", [{"name": "jump_strength", "type": "zigzag32"}]], "packet_update_block": ["container", [{"name": "position", "type": "BlockCoordinates"}, {"name": "block_runtime_id", "type": "varint"}, {"name": "flags", "type": "UpdateBlockFlags"}, {"name": "layer", "type": "varint"}]], "UpdateBlockFlags": ["bitflags", {"type": "varint", "flags": {"neighbors": 1, "network": 2, "no_graphic": 4, "unused": 8, "priority": 16}}], "packet_add_painting": ["container", [{"name": "entity_id_self", "type": "zigzag64"}, {"name": "runtime_entity_id", "type": "varint64"}, {"name": "coordinates", "type": "vec3f"}, {"name": "direction", "type": "zigzag32"}, {"name": "title", "type": "string"}]], "packet_tick_sync": ["container", [{"name": "request_time", "type": "li64"}, {"name": "response_time", "type": "li64"}]], "packet_level_sound_event_old": ["container", [{"name": "sound_id", "type": "u8"}, {"name": "position", "type": "vec3f"}, {"name": "block_id", "type": "zigzag32"}, {"name": "entity_type", "type": "zigzag32"}, {"name": "is_baby_mob", "type": "bool"}, {"name": "is_global", "type": "bool"}]], "packet_level_event": ["container", [{"name": "event", "type": ["mapper", {"type": "zigzag32", "mappings": {"1000": "sound_click", "1001": "sound_click_fail", "1002": "sound_shoot", "1003": "sound_door", "1004": "sound_fizz", "1005": "sound_ignite", "1007": "sound_ghast", "1008": "sound_ghast_shoot", "1009": "sound_blaze_shoot", "1010": "sound_door_bump", "1012": "sound_door_crash", "1018": "sound_enderman_teleport", "1020": "sound_anvil_break", "1021": "sound_anvil_use", "1022": "sound_anvil_fall", "1030": "sound_pop", "1032": "sound_portal", "1040": "sound_itemframe_add_item", "1041": "sound_itemframe_remove", "1042": "sound_itemframe_place", "1043": "sound_itemframe_remove_item", "1044": "sound_itemframe_rotate_item", "1050": "sound_camera", "1051": "sound_orb", "1052": "sound_totem", "1060": "sound_armor_stand_break", "1061": "sound_armor_stand_hit", "1062": "sound_armor_stand_fall", "1063": "sound_armor_stand_place", "1064": "pointed_dripstone_land", "1065": "dye_used", "1066": "ink_sack_used", "2000": "particle_shoot", "2001": "particle_destroy", "2002": "particle_splash", "2003": "particle_eye_despawn", "2004": "particle_spawn", "2005": "particle_crop_growth", "2006": "particle_guardian_curse", "2007": "particle_death_smoke", "2008": "particle_block_force_field", "2009": "particle_projectile_hit", "2010": "particle_dragon_egg_teleport", "2011": "particle_crop_eaten", "2012": "particle_critical", "2013": "particle_enderman_teleport", "2014": "particle_punch_block", "2015": "particle_bubble", "2016": "particle_evaporate", "2017": "particle_destroy_armor_stand", "2018": "particle_breaking_egg", "2019": "particle_destroy_egg", "2020": "particle_evaporate_water", "2021": "particle_destroy_block_no_sound", "2022": "particle_knockback_roar", "2023": "particle_teleport_trail", "2024": "particle_point_cloud", "2025": "particle_explosion", "2026": "particle_block_explosion", "2027": "particle_vibration_signal", "2028": "particle_dripstone_drip", "2029": "particle_fizz_effect", "2030": "particle_wax_on", "2031": "particle_wax_off", "2032": "particle_scrape", "2033": "particle_electric_spark", "2034": "particle_turtle_egg", "2035": "particle_sculk_shriek", "2036": "sculk_catalyst_bloom", "3001": "start_rain", "3002": "start_thunder", "3003": "stop_rain", "3004": "stop_thunder", "3005": "pause_game", "3006": "pause_game_no_screen", "3007": "set_game_speed", "3500": "redstone_trigger", "3501": "cauldron_explode", "3502": "cauldron_dye_armor", "3503": "cauldron_clean_armor", "3504": "cauldron_fill_potion", "3505": "cauldron_take_potion", "3506": "cauldron_fill_water", "3507": "cauldron_take_water", "3508": "cauldron_add_dye", "3509": "cauldron_clean_banner", "3600": "block_start_break", "3601": "block_stop_break", "4000": "set_data", "9800": "players_sleeping", "9801": "sleeping_players", "16384": "add_particle_mask", "16385": "add_particle_bubble", "16386": "add_particle_bubble_manual", "16387": "add_particle_critical", "16388": "add_particle_block_force_field", "16389": "add_particle_smoke", "16390": "add_particle_explode", "16391": "add_particle_evaporation", "16392": "add_particle_flame", "16393": "add_particle_candle_flame", "16394": "add_particle_lava", "16395": "add_particle_large_smoke", "16396": "add_particle_redstone", "16397": "add_particle_rising_red_dust", "16398": "add_particle_item_break", "16399": "add_particle_snowball_poof", "16400": "add_particle_huge_explode", "16401": "add_particle_huge_explode_seed", "16402": "add_particle_mob_flame", "16403": "add_particle_heart", "16404": "add_particle_terrain", "16405": "add_particle_town_aura", "16406": "add_particle_portal", "16408": "add_particle_water_splash", "16409": "add_particle_water_splash_manual", "16410": "add_particle_water_wake", "16411": "add_particle_drip_water", "16412": "add_particle_drip_lava", "16413": "add_particle_drip_honey", "16414": "add_particle_stalactite_drip_water", "16415": "add_particle_stalactite_drip_lava", "16416": "add_particle_falling_dust", "16417": "add_particle_mob_spell", "16418": "add_particle_mob_spell_ambient", "16419": "add_particle_mob_spell_instantaneous", "16420": "add_particle_ink", "16421": "add_particle_slime", "16422": "add_particle_rain_splash", "16423": "add_particle_villager_angry", "16424": "add_particle_villager_happy", "16425": "add_particle_enchantment_table", "16426": "add_particle_tracking_emitter", "16427": "add_particle_note", "16428": "add_particle_witch_spell", "16429": "add_particle_carrot", "16430": "add_particle_mob_appearance", "16431": "add_particle_end_rod", "16432": "add_particle_dragons_breath", "16433": "add_particle_spit", "16434": "add_particle_totem", "16435": "add_particle_food", "16436": "add_particle_fireworks_starter", "16437": "add_particle_fireworks_spark", "16438": "add_particle_fireworks_overlay", "16439": "add_particle_balloon_gas", "16440": "add_particle_colored_flame", "16441": "add_particle_sparkler", "16442": "add_particle_conduit", "16443": "add_particle_bubble_column_up", "16444": "add_particle_bubble_column_down", "16445": "add_particle_sneeze", "16446": "add_particle_shulker_bullet", "16447": "add_particle_bleach", "16448": "add_particle_dragon_destroy_block", "16449": "add_particle_mycelium_dust", "16450": "add_particle_falling_red_dust", "16451": "add_particle_campfire_smoke", "16452": "add_particle_tall_campfire_smoke", "16453": "add_particle_dragon_breath_fire", "16454": "add_particle_dragon_breath_trail", "16455": "add_particle_blue_flame", "16456": "add_particle_soul", "16457": "add_particle_obsidian_tear", "16458": "add_particle_portal_reverse", "16459": "add_particle_snowflake", "16460": "add_particle_vibration_signal", "16461": "add_particle_sculk_sensor_redstone", "16462": "add_particle_spore_blossom_shower", "16463": "add_particle_spore_blossom_ambient", "16464": "add_particle_wax", "16465": "add_particle_electric_spark"}}]}, {"name": "position", "type": "vec3f"}, {"name": "data", "type": "zigzag32"}]], "packet_block_event": ["container", [{"name": "position", "type": "BlockCoordinates"}, {"name": "type", "type": ["mapper", {"type": "zigzag32", "mappings": {"0": "sound", "1": "change_state"}}]}, {"name": "data", "type": "zigzag32"}]], "packet_entity_event": ["container", [{"name": "runtime_entity_id", "type": "varint64"}, {"name": "event_id", "type": ["mapper", {"type": "u8", "mappings": {"1": "jump", "2": "hurt_animation", "3": "death_animation", "4": "arm_swing", "5": "stop_attack", "6": "tame_fail", "7": "tame_success", "8": "shake_wet", "9": "use_item", "10": "eat_grass_animation", "11": "fish_hook_bubble", "12": "fish_hook_position", "13": "fish_hook_hook", "14": "fish_hook_tease", "15": "squid_ink_cloud", "16": "zombie_villager_cure", "18": "respawn", "19": "iron_golem_offer_flower", "20": "iron_golem_withdraw_flower", "21": "love_particles", "22": "villager_angry", "23": "villager_happy", "24": "witch_spell_particles", "25": "firework_particles", "26": "in_love_particles", "27": "silverfish_spawn_animation", "28": "guardian_attack", "29": "witch_drink_potion", "30": "witch_throw_potion", "31": "minecart_tnt_prime_fuse", "32": "creeper_prime_fuse", "33": "air_supply_expired", "34": "player_add_xp_levels", "35": "elder_guardian_curse", "36": "agent_arm_swing", "37": "ender_dragon_death", "38": "dust_particles", "39": "arrow_shake", "57": "eating_item", "60": "baby_animal_feed", "61": "death_smoke_cloud", "62": "complete_trade", "63": "remove_leash", "65": "consume_totem", "66": "player_check_treasure_hunter_achievement", "67": "entity_spawn", "68": "dragon_puke", "69": "item_entity_merge", "70": "start_swim", "71": "balloon_pop", "72": "treasure_hunt", "73": "agent_summon", "74": "charged_item", "75": "fall"}}]}, {"name": "data", "type": "zigzag32"}]], "packet_mob_effect": ["container", [{"name": "runtime_entity_id", "type": "varint64"}, {"name": "event_id", "type": ["mapper", {"type": "u8", "mappings": {"1": "add", "2": "update", "3": "remove"}}]}, {"name": "effect_id", "type": "zigzag32"}, {"name": "amplifier", "type": "zigzag32"}, {"name": "particles", "type": "bool"}, {"name": "duration", "type": "zigzag32"}]], "packet_update_attributes": ["container", [{"name": "runtime_entity_id", "type": "varint64"}, {"name": "attributes", "type": "PlayerAttributes"}, {"name": "tick", "type": "varint64"}]], "packet_inventory_transaction": ["container", [{"name": "transaction", "type": "Transaction"}]], "packet_mob_equipment": ["container", [{"name": "runtime_entity_id", "type": "varint64"}, {"name": "item", "type": "<PERSON><PERSON>"}, {"name": "slot", "type": "u8"}, {"name": "selected_slot", "type": "u8"}, {"name": "window_id", "type": "WindowID"}]], "packet_mob_armor_equipment": ["container", [{"name": "runtime_entity_id", "type": "varint64"}, {"name": "helmet", "type": "<PERSON><PERSON>"}, {"name": "chestplate", "type": "<PERSON><PERSON>"}, {"name": "leggings", "type": "<PERSON><PERSON>"}, {"name": "boots", "type": "<PERSON><PERSON>"}]], "packet_interact": ["container", [{"name": "action_id", "type": ["mapper", {"type": "u8", "mappings": {"3": "leave_vehicle", "4": "mouse_over_entity", "5": "npc_open", "6": "open_inventory"}}]}, {"name": "target_entity_id", "type": "varint64"}, {"name": "position", "type": ["switch", {"compareTo": "action_id", "fields": {"mouse_over_entity": "vec3f", "leave_vehicle": "vec3f"}}]}]], "packet_block_pick_request": ["container", [{"name": "x", "type": "zigzag32"}, {"name": "y", "type": "zigzag32"}, {"name": "z", "type": "zigzag32"}, {"name": "add_user_data", "type": "bool"}, {"name": "selected_slot", "type": "u8"}]], "packet_entity_pick_request": ["container", [{"name": "runtime_entity_id", "type": "lu64"}, {"name": "selected_slot", "type": "u8"}, {"name": "with_data", "type": "bool"}]], "packet_player_action": ["container", [{"name": "runtime_entity_id", "type": "varint64"}, {"name": "action", "type": "Action"}, {"name": "position", "type": "BlockCoordinates"}, {"name": "face", "type": "zigzag32"}]], "packet_hurt_armor": ["container", [{"name": "cause", "type": "zigzag32"}, {"name": "damage", "type": "zigzag32"}, {"name": "armor_slots", "type": "zigzag64"}]], "packet_set_entity_data": ["container", [{"name": "runtime_entity_id", "type": "varint64"}, {"name": "metadata", "type": "MetadataDictionary"}, {"name": "tick", "type": "varint64"}]], "packet_set_entity_motion": ["container", [{"name": "runtime_entity_id", "type": "varint64"}, {"name": "velocity", "type": "vec3f"}]], "packet_set_entity_link": ["container", [{"name": "link", "type": "Link"}]], "packet_set_health": ["container", [{"name": "health", "type": "zigzag32"}]], "packet_set_spawn_position": ["container", [{"name": "spawn_type", "type": ["mapper", {"type": "zigzag32", "mappings": {"0": "player", "1": "world"}}]}, {"name": "player_position", "type": "BlockCoordinates"}, {"name": "dimension", "type": "zigzag32"}, {"name": "world_position", "type": "BlockCoordinates"}]], "packet_animate": ["container", [{"name": "action_id", "type": ["mapper", {"type": "zigzag32", "mappings": {"0": "none", "1": "swing_arm", "2": "unknown", "3": "wake_up", "4": "critical_hit", "5": "magic_critical_hit", "128": "row_right", "129": "row_left"}}]}, {"name": "runtime_entity_id", "type": "varint64"}, {"anon": true, "type": ["switch", {"compareTo": "action_id", "fields": {"row_right": ["container", [{"name": "boat_rowing_time", "type": "lf32"}]], "row_left": ["container", [{"name": "boat_rowing_time", "type": "lf32"}]]}}]}]], "packet_respawn": ["container", [{"name": "position", "type": "vec3f"}, {"name": "state", "type": "u8"}, {"name": "runtime_entity_id", "type": "varint64"}]], "packet_container_open": ["container", [{"name": "window_id", "type": "WindowID"}, {"name": "window_type", "type": "WindowType"}, {"name": "coordinates", "type": "BlockCoordinates"}, {"name": "runtime_entity_id", "type": "zigzag64"}]], "packet_container_close": ["container", [{"name": "window_id", "type": "WindowID"}, {"name": "server", "type": "bool"}]], "packet_player_hotbar": ["container", [{"name": "selected_slot", "type": "varint"}, {"name": "window_id", "type": "WindowID"}, {"name": "select_slot", "type": "bool"}]], "packet_inventory_content": ["container", [{"name": "window_id", "type": "WindowIDVarint"}, {"name": "input", "type": "ItemStacks"}]], "packet_inventory_slot": ["container", [{"name": "window_id", "type": "WindowIDVarint"}, {"name": "slot", "type": "varint"}, {"name": "item", "type": "<PERSON><PERSON>"}]], "packet_container_set_data": ["container", [{"name": "window_id", "type": "WindowID"}, {"name": "property", "type": "zigzag32"}, {"name": "value", "type": "zigzag32"}]], "packet_crafting_data": ["container", [{"name": "recipes", "type": "Recipes"}, {"name": "potion_type_recipes", "type": "PotionTypeRecipes"}, {"name": "potion_container_recipes", "type": "PotionContainerChangeRecipes"}, {"name": "material_reducers", "type": ["array", {"countType": "varint", "type": "MaterialReducer"}]}, {"name": "clear_recipes", "type": "bool"}]], "packet_crafting_event": ["container", [{"name": "window_id", "type": "WindowID"}, {"name": "recipe_type", "type": ["mapper", {"type": "zigzag32", "mappings": {"0": "inventory", "1": "crafting", "2": "workbench"}}]}, {"name": "recipe_id", "type": "uuid"}, {"name": "input", "type": ["array", {"countType": "varint", "type": "<PERSON><PERSON>"}]}, {"name": "result", "type": ["array", {"countType": "varint", "type": "<PERSON><PERSON>"}]}]], "packet_gui_data_pick_item": ["container", [{"name": "item_name", "type": "string"}, {"name": "item_effects", "type": "string"}, {"name": "hotbar_slot", "type": "li32"}]], "packet_adventure_settings": ["container", [{"name": "flags", "type": "AdventureFlags"}, {"name": "command_permission", "type": ["mapper", {"type": "varint", "mappings": {"0": "normal", "1": "operator", "2": "host", "3": "automation", "4": "admin"}}]}, {"name": "action_permissions", "type": "ActionPermissions"}, {"name": "permission_level", "type": ["mapper", {"type": "varint", "mappings": {"0": "visitor", "1": "member", "2": "operator", "3": "custom"}}]}, {"name": "custom_stored_permissions", "type": "varint"}, {"name": "user_id", "type": "li64"}]], "AdventureFlags": ["bitflags", {"type": "varint", "flags": {"world_immutable": 1, "no_pvp": 2, "auto_jump": 32, "allow_flight": 64, "no_clip": 128, "world_builder": 256, "flying": 512, "muted": 1024}}], "ActionPermissions": ["bitflags", {"type": "varint", "flags": {"mine": 65537, "doors_and_switches": 65538, "open_containers": 65540, "attack_players": 65544, "attack_mobs": 65552, "operator": 65568, "teleport": 65664, "build": 65792, "default": 66048}}], "packet_block_entity_data": ["container", [{"name": "position", "type": "BlockCoordinates"}, {"name": "nbt", "type": "nbt"}]], "packet_player_input": ["container", [{"name": "motion_x", "type": "lf32"}, {"name": "motion_z", "type": "lf32"}, {"name": "jumping", "type": "bool"}, {"name": "sneaking", "type": "bool"}]], "packet_level_chunk": ["container", [{"name": "x", "type": "zigzag32"}, {"name": "z", "type": "zigzag32"}, {"name": "sub_chunk_count", "type": "varint"}, {"name": "cache_enabled", "type": "bool"}, {"name": "blobs", "type": ["switch", {"compareTo": "cache_enabled", "fields": {"true": ["container", [{"name": "hashes", "type": ["array", {"countType": "varint", "type": "lu64"}]}]]}}]}, {"name": "payload", "type": "ByteArray"}]], "packet_set_commands_enabled": ["container", [{"name": "enabled", "type": "bool"}]], "packet_set_difficulty": ["container", [{"name": "difficulty", "type": "varint"}]], "packet_change_dimension": ["container", [{"name": "dimension", "type": "zigzag32"}, {"name": "position", "type": "vec3f"}, {"name": "respawn", "type": "bool"}]], "packet_set_player_game_type": ["container", [{"name": "gamemode", "type": "GameMode"}]], "packet_player_list": ["container", [{"name": "records", "type": "PlayerRecords"}]], "packet_simple_event": ["container", [{"name": "event_type", "type": ["mapper", {"type": "lu16", "mappings": {"0": "uninitialized_subtype", "1": "enable_commands", "2": "disable_commands", "3": "unlock_world_template_settings"}}]}]], "packet_event": ["container", [{"name": "runtime_id", "type": "varint64"}, {"name": "event_type", "type": ["mapper", {"type": "zigzag32", "mappings": {"0": "achievement_awarded", "1": "entity_interact", "2": "portal_built", "3": "portal_used", "4": "mob_killed", "5": "cauldron_used", "6": "player_death", "7": "boss_killed", "8": "agent_command", "9": "agent_created", "10": "banner_pattern_removed", "11": "command_executed", "12": "fish_bucketed", "13": "mob_born", "14": "pet_died", "15": "cauldron_block_used", "16": "composter_block_used", "17": "bell_block_used", "18": "actor_definition", "19": "raid_update", "20": "player_movement_anomaly", "21": "player_movement_corrected", "22": "honey_harvested", "23": "target_block_hit", "24": "piglin_barter", "25": "waxed_or_unwaxed_copper"}}]}, {"name": "use_player_id", "type": "u8"}, {"name": "event_data", "type": "restBuffer"}]], "packet_spawn_experience_orb": ["container", [{"name": "position", "type": "vec3f"}, {"name": "count", "type": "zigzag32"}]], "UpdateMapFlags": ["bitflags", {"type": "varint", "flags": ["void", "texture", "decoration", "initialisation"]}], "packet_clientbound_map_item_data": ["container", [{"name": "map_id", "type": "zigzag64"}, {"name": "update_flags", "type": "UpdateMapFlags"}, {"name": "dimension", "type": "u8"}, {"name": "locked", "type": "bool"}, {"name": "included_in", "type": ["switch", {"compareTo": "update_flags.initialisation", "fields": {"true": ["array", {"countType": "varint", "type": "zigzag64"}]}}]}, {"name": "scale", "type": ["switch", {"compareTo": "update_flags.initialisation || update_flags.decoration || update_flags.texture", "fields": {"true": "u8"}}]}, {"name": "tracked", "type": ["switch", {"compareTo": "update_flags.decoration", "fields": {"true": ["container", [{"name": "objects", "type": ["array", {"countType": "varint", "type": "TrackedObject"}]}, {"name": "decorations", "type": ["array", {"countType": "varint", "type": "MapDecoration"}]}]]}}]}, {"name": "texture", "type": ["switch", {"compareTo": "update_flags.texture", "fields": {"true": ["container", [{"name": "width", "type": "zigzag32"}, {"name": "height", "type": "zigzag32"}, {"name": "x_offset", "type": "zigzag32"}, {"name": "y_offset", "type": "zigzag32"}, {"name": "pixels", "type": ["array", {"countType": "varint", "type": "varint"}]}]]}}]}]], "packet_map_info_request": ["container", [{"name": "map_id", "type": "zigzag64"}]], "packet_request_chunk_radius": ["container", [{"name": "chunk_radius", "type": "zigzag32"}]], "packet_chunk_radius_update": ["container", [{"name": "chunk_radius", "type": "zigzag32"}]], "packet_item_frame_drop_item": ["container", [{"name": "coordinates", "type": "BlockCoordinates"}]], "packet_game_rules_changed": ["container", [{"name": "rules", "type": "GameRules"}]], "packet_camera": ["container", [{"name": "camera_entity_unique_id", "type": "zigzag64"}, {"name": "target_player_unique_id", "type": "zigzag64"}]], "packet_boss_event": ["container", [{"name": "boss_entity_id", "type": "zigzag64"}, {"name": "type", "type": ["mapper", {"type": "varint", "mappings": {"0": "show_bar", "1": "register_player", "2": "hide_bar", "3": "unregister_player", "4": "set_bar_progress", "5": "set_bar_title", "6": "update_properties", "7": "texture"}}]}, {"anon": true, "type": ["switch", {"compareTo": "type", "fields": {"show_bar": ["container", [{"name": "title", "type": "string"}, {"name": "progress", "type": "lf32"}, {"name": "screen_darkening", "type": "li16"}, {"name": "color", "type": "varint"}, {"name": "overlay", "type": "varint"}]], "register_player": ["container", [{"name": "player_id", "type": "zigzag64"}]], "unregister_player": ["container", [{"name": "player_id", "type": "zigzag64"}]], "set_bar_progress": ["container", [{"name": "progress", "type": "lf32"}]], "set_bar_title": ["container", [{"name": "title", "type": "string"}]], "update_properties": ["container", [{"name": "screen_darkening", "type": "li16"}, {"name": "color", "type": "varint"}, {"name": "overlay", "type": "varint"}]], "texture": ["container", [{"name": "color", "type": "varint"}, {"name": "overlay", "type": "varint"}]]}}]}]], "packet_show_credits": ["container", [{"name": "runtime_entity_id", "type": "varint64"}, {"name": "status", "type": "zigzag32"}]], "packet_available_commands": ["container", [{"name": "values_len", "type": "varint"}, {"name": "_enum_type", "type": ["enum_size_based_on_values_len"]}, {"name": "enum_values", "type": ["array", {"count": "values_len", "type": "string"}]}, {"name": "suffixes", "type": ["array", {"countType": "varint", "type": "string"}]}, {"name": "enums", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "name", "type": "string"}, {"name": "values", "type": ["array", {"countType": "varint", "type": ["switch", {"compareTo": "../_enum_type", "fields": {"byte": "u8", "short": "lu16", "int": "lu32"}}]}]}]]}]}, {"name": "command_data", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "name", "type": "string"}, {"name": "description", "type": "string"}, {"name": "flags", "type": "lu16"}, {"name": "permission_level", "type": "u8"}, {"name": "alias", "type": "li32"}, {"name": "overloads", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "parameters", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "parameter_name", "type": "string"}, {"name": "value_type", "type": ["mapper", {"type": "lu16", "mappings": {"1": "int", "2": "float", "3": "value", "4": "wildcard_int", "5": "operator", "6": "target", "16": "file_path", "32": "string", "40": "position", "44": "message", "46": "raw_text", "50": "json", "63": "command"}}]}, {"name": "enum_type", "type": ["mapper", {"type": "lu16", "mappings": {"16": "valid", "48": "enum", "256": "suffixed", "1040": "soft_enum"}}]}, {"name": "optional", "type": "bool"}, {"name": "options", "type": "CommandFlags"}]]}]}]]}]}]]}]}, {"name": "dynamic_enums", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "name", "type": "string"}, {"name": "values", "type": ["array", {"countType": "varint", "type": "string"}]}]]}]}, {"name": "enum_constraints", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "value_index", "type": "li32"}, {"name": "enum_index", "type": "li32"}, {"name": "constraints", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "constraint", "type": ["mapper", {"type": "u8", "mappings": {"0": "cheats_enabled", "1": "operator_permissions", "2": "host_permissions"}}]}]]}]}]]}]}]], "CommandFlags": ["bitfield", [{"name": "unused", "size": 1, "signed": false}, {"name": "collapse_enum", "size": 1, "signed": false}, {"name": "has_semantic_constraint", "size": 1, "signed": false}, {"name": "as_chained_command", "size": 1, "signed": false}, {"name": "unknown2", "size": 4, "signed": false}]], "packet_command_request": ["container", [{"name": "command", "type": "string"}, {"name": "origin", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "internal", "type": "bool"}]], "packet_command_block_update": ["container", [{"name": "is_block", "type": "bool"}, {"anon": true, "type": ["switch", {"compareTo": "is_block", "fields": {"true": ["container", [{"name": "position", "type": "BlockCoordinates"}, {"name": "mode", "type": ["mapper", {"type": "varint", "mappings": {"0": "impulse", "1": "repeat", "2": "chain"}}]}, {"name": "needs_redstone", "type": "bool"}, {"name": "conditional", "type": "bool"}]], "false": ["container", [{"name": "minecart_entity_runtime_id", "type": "varint64"}]]}}]}, {"name": "command", "type": "string"}, {"name": "last_output", "type": "string"}, {"name": "name", "type": "string"}, {"name": "should_track_output", "type": "bool"}, {"name": "tick_delay", "type": "li32"}, {"name": "execute_on_first_tick", "type": "bool"}]], "packet_command_output": ["container", [{"name": "origin", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "output_type", "type": ["mapper", {"type": "i8", "mappings": {"1": "last", "2": "silent", "3": "all", "4": "data_set"}}]}, {"name": "success_count", "type": "varint"}, {"name": "output", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "success", "type": "bool"}, {"name": "message_id", "type": "string"}, {"name": "parameters", "type": ["array", {"countType": "varint", "type": "string"}]}]]}]}, {"name": "data_set", "type": ["switch", {"compareTo": "output_type", "fields": {"data_set": "string"}, "default": "void"}]}]], "packet_update_trade": ["container", [{"name": "window_id", "type": "WindowID"}, {"name": "window_type", "type": "WindowType"}, {"name": "size", "type": "varint"}, {"name": "trade_tier", "type": "varint"}, {"name": "villager_unique_id", "type": "varint64"}, {"name": "entity_unique_id", "type": "varint64"}, {"name": "display_name", "type": "string"}, {"name": "new_trading_ui", "type": "bool"}, {"name": "economic_trades", "type": "bool"}, {"name": "offers", "type": "nbt"}]], "packet_update_equipment": ["container", [{"name": "window_id", "type": "WindowID"}, {"name": "window_type", "type": "WindowType"}, {"name": "size", "type": "u8"}, {"name": "entity_id", "type": "zigzag64"}, {"name": "inventory", "type": "nbt"}]], "packet_resource_pack_data_info": ["container", [{"name": "pack_id", "type": "string"}, {"name": "max_chunk_size", "type": "lu32"}, {"name": "chunk_count", "type": "lu32"}, {"name": "size", "type": "lu64"}, {"name": "hash", "type": "ByteArray"}, {"name": "is_premium", "type": "bool"}, {"name": "pack_type", "type": ["mapper", {"type": "u8", "mappings": {"1": "addon", "2": "cached", "3": "copy_protected", "4": "behavior", "5": "persona_piece", "6": "resources", "7": "skins", "8": "world_template"}}]}]], "packet_resource_pack_chunk_data": ["container", [{"name": "pack_id", "type": "string"}, {"name": "chunk_index", "type": "lu32"}, {"name": "progress", "type": "lu64"}, {"name": "payload", "type": "ByteArray"}]], "packet_resource_pack_chunk_request": ["container", [{"name": "pack_id", "type": "string"}, {"name": "chunk_index", "type": "lu32"}]], "packet_transfer": ["container", [{"name": "server_address", "type": "string"}, {"name": "port", "type": "lu16"}]], "packet_play_sound": ["container", [{"name": "name", "type": "string"}, {"name": "coordinates", "type": "BlockCoordinates"}, {"name": "volume", "type": "lf32"}, {"name": "pitch", "type": "lf32"}]], "packet_stop_sound": ["container", [{"name": "name", "type": "string"}, {"name": "stop_all", "type": "bool"}]], "packet_set_title": ["container", [{"name": "type", "type": ["mapper", {"type": "zigzag32", "mappings": {"0": "clear", "1": "reset", "2": "set_title", "3": "set_subtitle", "4": "action_bar_message", "5": "set_durations", "6": "set_title_json", "7": "set_subtitle_json", "8": "action_bar_message_json"}}]}, {"name": "text", "type": "string"}, {"name": "fade_in_time", "type": "zigzag32"}, {"name": "stay_time", "type": "zigzag32"}, {"name": "fade_out_time", "type": "zigzag32"}, {"name": "xuid", "type": "string"}, {"name": "platform_online_id", "type": "string"}]], "packet_add_behavior_tree": ["container", [{"name": "behaviortree", "type": "string"}]], "packet_structure_block_update": ["container", [{"name": "position", "type": "BlockCoordinates"}, {"name": "structure_name", "type": "string"}, {"name": "data_field", "type": "string"}, {"name": "include_players", "type": "bool"}, {"name": "show_bounding_box", "type": "bool"}, {"name": "structure_block_type", "type": "zigzag32"}, {"name": "settings", "type": "StructureBlockSettings"}, {"name": "redstone_save_mode", "type": "zigzag32"}, {"name": "should_trigger", "type": "bool"}]], "packet_show_store_offer": ["container", [{"name": "offer_id", "type": "string"}, {"name": "show_all", "type": "bool"}]], "packet_purchase_receipt": ["container", [{"name": "receipts", "type": ["array", {"countType": "varint", "type": "string"}]}]], "packet_player_skin": ["container", [{"name": "uuid", "type": "uuid"}, {"name": "skin", "type": "Skin"}, {"name": "skin_name", "type": "string"}, {"name": "old_skin_name", "type": "string"}, {"name": "is_verified", "type": "bool"}]], "packet_sub_client_login": ["container", [{"name": "tokens", "type": ["encapsulated", {"lengthType": "varint", "type": "LoginTokens"}]}]], "packet_initiate_web_socket_connection": ["container", [{"name": "server", "type": "string"}]], "packet_set_last_hurt_by": ["container", [{"name": "entity_type", "type": "varint"}]], "packet_book_edit": ["container", [{"name": "type", "type": ["mapper", {"type": "u8", "mappings": {"0": "replace_page", "1": "add_page", "2": "delete_page", "3": "swap_pages", "4": "sign"}}]}, {"name": "slot", "type": "u8"}, {"anon": true, "type": ["switch", {"compareTo": "type", "fields": {"replace_page": ["container", [{"name": "page_number", "type": "u8"}, {"name": "text", "type": "string"}, {"name": "photo_name", "type": "string"}]], "add_page": ["container", [{"name": "page_number", "type": "u8"}, {"name": "text", "type": "string"}, {"name": "photo_name", "type": "string"}]], "delete_page": ["container", [{"name": "page_number", "type": "u8"}]], "swap_pages": ["container", [{"name": "page1", "type": "u8"}, {"name": "page2", "type": "u8"}]], "sign": ["container", [{"name": "title", "type": "string"}, {"name": "author", "type": "string"}, {"name": "xuid", "type": "string"}]]}}]}]], "packet_npc_request": ["container", [{"name": "runtime_entity_id", "type": "varint64"}, {"name": "request_type", "type": ["mapper", {"type": "u8", "mappings": {"0": "set_actions", "1": "execute_action", "2": "execute_closing_commands", "3": "set_name", "4": "set_skin", "5": "set_interaction_text", "6": "execute_opening_commands"}}]}, {"name": "command", "type": "string"}, {"name": "action_type", "type": ["mapper", {"type": "u8", "mappings": {"0": "set_actions", "1": "execute_action", "2": "execute_closing_commands", "3": "set_name", "4": "set_skin", "5": "set_interact_text", "6": "execute_opening_commands"}}]}, {"name": "scene_name", "type": "string"}]], "packet_photo_transfer": ["container", [{"name": "image_name", "type": "string"}, {"name": "image_data", "type": "string"}, {"name": "book_id", "type": "string"}, {"name": "photo_type", "type": "u8"}, {"name": "source_type", "type": "u8"}, {"name": "owner_entity_unique_id", "type": "li64"}, {"name": "new_photo_name", "type": "string"}]], "packet_modal_form_request": ["container", [{"name": "form_id", "type": "varint"}, {"name": "data", "type": "string"}]], "packet_modal_form_response": ["container", [{"name": "form_id", "type": "varint"}, {"name": "data", "type": "string"}]], "packet_server_settings_request": ["container", []], "packet_server_settings_response": ["container", [{"name": "form_id", "type": "varint"}, {"name": "data", "type": "string"}]], "packet_show_profile": ["container", [{"name": "xuid", "type": "string"}]], "packet_set_default_game_type": ["container", [{"name": "gamemode", "type": "GameMode"}]], "packet_remove_objective": ["container", [{"name": "objective_name", "type": "string"}]], "packet_set_display_objective": ["container", [{"name": "display_slot", "type": "string"}, {"name": "objective_name", "type": "string"}, {"name": "display_name", "type": "string"}, {"name": "criteria_name", "type": "string"}, {"name": "sort_order", "type": "zigzag32"}]], "packet_set_score": ["container", [{"name": "action", "type": ["mapper", {"type": "u8", "mappings": {"0": "change", "1": "remove"}}]}, {"name": "entries", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "scoreboard_id", "type": "zigzag64"}, {"name": "objective_name", "type": "string"}, {"name": "score", "type": "li32"}, {"anon": true, "type": ["switch", {"compareTo": "../action", "fields": {"change": ["container", [{"name": "entry_type", "type": ["mapper", {"type": "i8", "mappings": {"1": "player", "2": "entity", "3": "fake_player"}}]}, {"name": "entity_unique_id", "type": ["switch", {"compareTo": "entry_type", "fields": {"player": "zigzag64", "entity": "zigzag64"}}]}, {"name": "custom_name", "type": ["switch", {"compareTo": "entry_type", "fields": {"fake_player": "string"}}]}]]}}]}]]}]}]], "packet_lab_table": ["container", [{"name": "action_type", "type": ["mapper", {"type": "u8", "mappings": {"0": "combine", "1": "react", "2": "reset"}}]}, {"name": "position", "type": "vec3i"}, {"name": "reaction_type", "type": "u8"}]], "packet_update_block_synced": ["container", [{"name": "position", "type": "BlockCoordinates"}, {"name": "block_runtime_id", "type": "varint"}, {"name": "flags", "type": "UpdateBlockFlags"}, {"name": "layer", "type": "varint"}, {"name": "entity_unique_id", "type": "zigzag64"}, {"name": "transition_type", "type": ["mapper", {"type": "varint", "mappings": {"0": "entity", "1": "create", "2": "destroy"}}]}]], "packet_move_entity_delta": ["container", [{"name": "runtime_entity_id", "type": "varint64"}, {"name": "flags", "type": "DeltaMoveFlags"}, {"name": "x", "type": ["switch", {"compareTo": "flags.has_x", "fields": {"true": "lf32"}}]}, {"name": "y", "type": ["switch", {"compareTo": "flags.has_y", "fields": {"true": "lf32"}}]}, {"name": "z", "type": ["switch", {"compareTo": "flags.has_z", "fields": {"true": "lf32"}}]}, {"name": "rot_x", "type": ["switch", {"compareTo": "flags.has_rot_x", "fields": {"true": "u8"}}]}, {"name": "rot_y", "type": ["switch", {"compareTo": "flags.has_rot_y", "fields": {"true": "u8"}}]}, {"name": "rot_z", "type": ["switch", {"compareTo": "flags.has_rot_z", "fields": {"true": "u8"}}]}]], "DeltaMoveFlags": ["bitflags", {"type": "lu16", "flags": {"has_x": 1, "has_y": 2, "has_z": 4, "has_rot_x": 8, "has_rot_y": 16, "has_rot_z": 32, "on_ground": 64, "teleport": 128, "force_move": 256}}], "packet_set_scoreboard_identity": ["container", [{"name": "action", "type": ["mapper", {"type": "i8", "mappings": {"0": "register_identity", "1": "clear_identity"}}]}, {"name": "entries", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "scoreboard_id", "type": "zigzag64"}, {"name": "entity_unique_id", "type": ["switch", {"compareTo": "../action", "fields": {"register_identity": "zigzag64"}, "default": "void"}]}]]}]}]], "packet_set_local_player_as_initialized": ["container", [{"name": "runtime_entity_id", "type": "varint64"}]], "packet_update_soft_enum": ["container", [{"name": "enum_type", "type": "string"}, {"name": "options", "type": ["array", {"countType": "varint", "type": "string"}]}, {"name": "action_type", "type": ["mapper", {"type": "u8", "mappings": {"0": "add", "1": "remove", "2": "update"}}]}]], "packet_network_stack_latency": ["container", [{"name": "timestamp", "type": "lu64"}, {"name": "needs_response", "type": "u8"}]], "packet_script_custom_event": ["container", [{"name": "event_name", "type": "string"}, {"name": "event_data", "type": "string"}]], "packet_spawn_particle_effect": ["container", [{"name": "dimension", "type": "u8"}, {"name": "entity_id", "type": "zigzag64"}, {"name": "position", "type": "vec3f"}, {"name": "particle_name", "type": "string"}]], "packet_available_entity_identifiers": ["container", [{"name": "nbt", "type": "nbt"}]], "packet_level_sound_event_v2": ["container", [{"name": "sound_id", "type": "u8"}, {"name": "position", "type": "vec3f"}, {"name": "block_id", "type": "zigzag32"}, {"name": "entity_type", "type": "string"}, {"name": "is_baby_mob", "type": "bool"}, {"name": "is_global", "type": "bool"}]], "packet_network_chunk_publisher_update": ["container", [{"name": "coordinates", "type": "BlockCoordinates"}, {"name": "radius", "type": "varint"}]], "packet_biome_definition_list": ["container", [{"name": "nbt", "type": "nbt"}]], "packet_level_sound_event": ["container", [{"name": "sound_id", "type": "SoundType"}, {"name": "position", "type": "vec3f"}, {"name": "extra_data", "type": "zigzag32"}, {"name": "entity_type", "type": "string"}, {"name": "is_baby_mob", "type": "bool"}, {"name": "is_global", "type": "bool"}]], "packet_level_event_generic": ["container", [{"name": "event_id", "type": "varint"}, {"name": "nbt", "type": "nbtLoop"}]], "packet_lectern_update": ["container", [{"name": "page", "type": "u8"}, {"name": "page_count", "type": "u8"}, {"name": "position", "type": "vec3i"}, {"name": "drop_book", "type": "bool"}]], "packet_video_stream_connect": ["container", [{"name": "server_uri", "type": "string"}, {"name": "frame_send_frequency", "type": "lf32"}, {"name": "action", "type": ["mapper", {"type": "u8", "mappings": {"1": "none", "2": "close"}}]}, {"name": "resolution_x", "type": "li32"}, {"name": "resolution_y", "type": "li32"}]], "packet_add_ecs_entity": ["container", [{"name": "network_id", "type": "varint64"}]], "packet_remove_ecs_entity": ["container", [{"name": "network_id", "type": "varint64"}]], "packet_client_cache_status": ["container", [{"name": "enabled", "type": "bool"}]], "packet_on_screen_texture_animation": ["container", [{"name": "animation_type", "type": "lu32"}]], "packet_map_create_locked_copy": ["container", [{"name": "original_map_id", "type": "zigzag64"}, {"name": "new_map_id", "type": "zigzag64"}]], "packet_structure_template_data_export_request": ["container", [{"name": "name", "type": "string"}, {"name": "position", "type": "BlockCoordinates"}, {"name": "settings", "type": "StructureBlockSettings"}, {"name": "request_type", "type": ["mapper", {"type": "u8", "mappings": {"1": "export_from_save", "2": "export_from_load", "3": "query_saved_structure"}}]}]], "packet_structure_template_data_export_response": ["container", [{"name": "name", "type": "string"}, {"name": "success", "type": "bool"}, {"name": "nbt", "type": ["switch", {"compareTo": "success", "fields": {"true": "nbt"}}]}, {"name": "response_type", "type": ["mapper", {"type": "u8", "mappings": {"1": "export", "2": "query"}}]}]], "packet_update_block_properties": ["container", [{"name": "nbt", "type": "nbt"}]], "packet_client_cache_blob_status": ["container", [{"name": "misses", "type": "varint"}, {"name": "haves", "type": "varint"}, {"name": "missing", "type": ["array", {"count": "misses", "type": "lu64"}]}, {"name": "have", "type": ["array", {"count": "haves", "type": "lu64"}]}]], "packet_client_cache_miss_response": ["container", [{"name": "blobs", "type": ["array", {"countType": "varint", "type": "Blob"}]}]], "packet_education_settings": ["container", [{"name": "CodeBuilderDefaultURI", "type": "string"}, {"name": "CodeBuilderTitle", "type": "string"}, {"name": "CanResizeCodeBuilder", "type": "bool"}, {"name": "disable_legacy_title_bar", "type": "bool"}, {"name": "post_process_filter", "type": "string"}, {"name": "screenshot_border_path", "type": "string"}, {"name": "has_agent_capabilities", "type": "bool"}, {"name": "agent_capabilities", "type": ["switch", {"compareTo": "has_agent_capabilities", "fields": {"true": ["container", [{"name": "has", "type": "bool"}, {"name": "can_modify_blocks", "type": "bool"}]]}}]}, {"name": "HasOverrideURI", "type": "bool"}, {"name": "OverrideURI", "type": ["switch", {"compareTo": "HasOverrideURI", "fields": {"true": "string"}}]}, {"name": "HasQuiz", "type": "bool"}, {"name": "has_external_link_settings", "type": "bool"}, {"name": "external_link_settings", "type": ["switch", {"compareTo": "has_external_link_settings", "fields": {"true": ["container", [{"name": "has", "type": "bool"}, {"name": "url", "type": "string"}, {"name": "display_name", "type": "string"}]]}}]}]], "packet_emote": ["container", [{"name": "entity_id", "type": "varint64"}, {"name": "emote_id", "type": "string"}, {"name": "flags", "type": "u8"}]], "packet_multiplayer_settings": ["container", [{"name": "action_type", "type": ["mapper", {"type": "zigzag32", "mappings": {"0": "enable_multiplayer", "1": "disable_multiplayer", "2": "refresh_join_code"}}]}]], "packet_settings_command": ["container", [{"name": "command_line", "type": "string"}, {"name": "suppress_output", "type": "bool"}]], "packet_anvil_damage": ["container", [{"name": "damage", "type": "u8"}, {"name": "position", "type": "BlockCoordinates"}]], "packet_completed_using_item": ["container", [{"name": "used_item_id", "type": "li16"}, {"name": "use_method", "type": ["mapper", {"type": "li32", "mappings": {"0": "equip_armor", "1": "eat", "2": "attack", "3": "consume", "4": "throw", "5": "shoot", "6": "place", "7": "fill_bottle", "8": "fill_bucket", "9": "pour_bucket", "10": "use_tool", "11": "interact", "12": "retrieved", "13": "dyed", "14": "traded"}}]}]], "packet_network_settings": ["container", [{"name": "compression_threshold", "type": "u16"}]], "packet_player_auth_input": ["container", [{"name": "pitch", "type": "lf32"}, {"name": "yaw", "type": "lf32"}, {"name": "position", "type": "vec3f"}, {"name": "move_vector", "type": "vec2f"}, {"name": "head_yaw", "type": "lf32"}, {"name": "input_data", "type": "InputFlag"}, {"name": "input_mode", "type": ["mapper", {"type": "varint", "mappings": {"0": "unknown", "1": "mouse", "2": "touch", "3": "game_pad", "4": "motion_controller"}}]}, {"name": "play_mode", "type": ["mapper", {"type": "varint", "mappings": {"0": "normal", "1": "teaser", "2": "screen", "3": "viewer", "4": "reality", "5": "placement", "6": "living_room", "7": "exit_level", "8": "exit_level_living_room", "9": "num_modes"}}]}, {"name": "gaze_direction", "type": ["switch", {"compareTo": "play_mode", "fields": {"reality": "vec3f"}}]}, {"name": "tick", "type": "varint64"}, {"name": "delta", "type": "vec3f"}, {"name": "transaction", "type": ["switch", {"compareTo": "input_data.item_interact", "fields": {"true": ["container", [{"name": "legacy", "type": "TransactionLegacy"}, {"name": "actions", "type": "TransactionActions"}, {"name": "data", "type": "TransactionUseItem"}]]}}]}, {"name": "item_stack_request", "type": ["switch", {"compareTo": "input_data.item_stack_request", "fields": {"true": "ItemStackRequest"}}]}, {"name": "block_action", "type": ["switch", {"compareTo": "input_data.block_action", "fields": {"true": ["array", {"countType": "zigzag32", "type": ["container", [{"name": "action", "type": "Action"}, {"anon": true, "type": ["switch", {"compareTo": "action", "fields": {"start_break": ["container", [{"name": "position", "type": "vec3i"}, {"name": "face", "type": "zigzag32"}]], "abort_break": ["container", [{"name": "position", "type": "vec3i"}, {"name": "face", "type": "zigzag32"}]], "crack_break": ["container", [{"name": "position", "type": "vec3i"}, {"name": "face", "type": "zigzag32"}]], "predict_break": ["container", [{"name": "position", "type": "vec3i"}, {"name": "face", "type": "zigzag32"}]], "continue_break": ["container", [{"name": "position", "type": "vec3i"}, {"name": "face", "type": "zigzag32"}]]}}]}]]}]}}]}]], "InputFlag": ["bitflags", {"type": "varint64", "big": true, "flags": ["ascend", "descend", "north_jump", "jump_down", "sprint_down", "change_height", "jumping", "auto_jumping_in_water", "sneaking", "sneak_down", "up", "down", "left", "right", "up_left", "up_right", "want_up", "want_down", "want_down_slow", "want_up_slow", "sprinting", "ascend_block", "descend_block", "sneak_toggle_down", "persist_sneak", "start_sprinting", "stop_sprinting", "start_sneaking", "stop_sneaking", "start_swimming", "stop_swimming", "start_jumping", "start_gliding", "stop_gliding", "item_interact", "block_action", "item_stack_request"]}], "packet_creative_content": ["container", [{"name": "items", "type": ["array", {"countType": "varint", "type": ["container", [{"name": "entry_id", "type": "varint"}, {"name": "item", "type": "ItemLegacy"}]]}]}]], "packet_player_enchant_options": ["container", [{"name": "options", "type": ["array", {"countType": "varint", "type": "EnchantOption"}]}]], "packet_item_stack_request": ["container", [{"name": "requests", "type": ["array", {"countType": "varint", "type": "ItemStackRequest"}]}]], "packet_item_stack_response": ["container", [{"name": "responses", "type": "ItemStackResponses"}]], "packet_player_armor_damage": ["container", [{"name": "type", "type": "ArmorDamageType"}, {"name": "helmet_damage", "type": ["switch", {"compareTo": "type.head", "fields": {"true": "zigzag32"}}]}, {"name": "chestplate_damage", "type": ["switch", {"compareTo": "type.chest", "fields": {"true": "zigzag32"}}]}, {"name": "leggings_damage", "type": ["switch", {"compareTo": "type.legs", "fields": {"true": "zigzag32"}}]}, {"name": "boots_damage", "type": ["switch", {"compareTo": "type.feet", "fields": {"true": "zigzag32"}}]}]], "ArmorDamageType": ["bitflags", {"type": "u8", "flags": {"head": 1, "chest": 2, "legs": 4, "feet": 8}}], "packet_update_player_game_type": ["container", [{"name": "gamemode", "type": "GameMode"}, {"name": "player_unique_id", "type": "zigzag64"}]], "packet_emote_list": ["container", [{"name": "player_id", "type": "varint64"}, {"name": "emote_pieces", "type": ["array", {"countType": "varint", "type": "uuid"}]}]], "packet_position_tracking_db_request": ["container", [{"name": "action", "type": ["mapper", {"type": "u8", "mappings": {"0": "query"}}]}, {"name": "tracking_id", "type": "zigzag32"}]], "packet_position_tracking_db_broadcast": ["container", [{"name": "broadcast_action", "type": ["mapper", {"type": "u8", "mappings": {"0": "update", "1": "destory", "2": "not_found"}}]}, {"name": "tracking_id", "type": "zigzag32"}, {"name": "nbt", "type": "nbt"}]], "packet_packet_violation_warning": ["container", [{"name": "violation_type", "type": ["mapper", {"type": "zigzag32", "mappings": {"0": "malformed"}}]}, {"name": "severity", "type": ["mapper", {"type": "zigzag32", "mappings": {"0": "warning", "1": "final_warning", "2": "terminating"}}]}, {"name": "packet_id", "type": "zigzag32"}, {"name": "reason", "type": "string"}]], "packet_motion_prediction_hints": ["container", [{"name": "entity_runtime_id", "type": "varint64"}, {"name": "velocity", "type": "vec3f"}, {"name": "on_ground", "type": "bool"}]], "packet_animate_entity": ["container", [{"name": "animation", "type": "string"}, {"name": "next_state", "type": "string"}, {"name": "stop_condition", "type": "string"}, {"name": "stop_condition_version", "type": "li32"}, {"name": "controller", "type": "string"}, {"name": "blend_out_time", "type": "lf32"}, {"name": "runtime_entity_ids", "type": ["array", {"countType": "varint", "type": "varint64"}]}]], "packet_camera_shake": ["container", [{"name": "intensity", "type": "lf32"}, {"name": "duration", "type": "lf32"}, {"name": "type", "type": "u8"}, {"name": "action", "type": ["mapper", {"type": "u8", "mappings": {"0": "add", "1": "stop"}}]}]], "packet_player_fog": ["container", [{"name": "stack", "type": ["array", {"countType": "varint", "type": "string"}]}]], "packet_correct_player_move_prediction": ["container", [{"name": "position", "type": "vec3f"}, {"name": "delta", "type": "vec3f"}, {"name": "on_ground", "type": "bool"}, {"name": "tick", "type": "varint64"}]], "packet_item_component": ["container", [{"name": "entries", "type": "ItemComponentList"}]], "packet_filter_text_packet": ["container", [{"name": "text", "type": "string"}, {"name": "from_server", "type": "bool"}]], "packet_debug_renderer": ["container", [{"name": "type", "type": ["mapper", {"type": "li32", "mappings": {"1": "clear", "2": "add_cube"}}]}, {"anon": true, "type": ["switch", {"compareTo": "type", "fields": {"clear": "void", "add_cube": ["container", [{"name": "text", "type": "string"}, {"name": "position", "type": "vec3f"}, {"name": "red", "type": "lf32"}, {"name": "green", "type": "lf32"}, {"name": "blue", "type": "lf32"}, {"name": "alpha", "type": "lf32"}, {"name": "duration", "type": "li64"}]]}}]}]], "packet_sync_entity_property": ["container", [{"name": "nbt", "type": "nbt"}]], "packet_add_volume_entity": ["container", [{"name": "entity_id", "type": "varint64"}, {"name": "nbt", "type": "nbt"}, {"name": "engine_version", "type": "string"}]], "packet_remove_volume_entity": ["container", [{"name": "entity_id", "type": "varint64"}]], "packet_simulation_type": ["container", [{"name": "type", "type": ["mapper", {"type": "u8", "mappings": {"0": "game", "1": "editor", "2": "test", "3": "invalid"}}]}]], "packet_npc_dialogue": ["container", [{"name": "entity_id", "type": "lu64"}, {"name": "action_type", "type": ["mapper", {"type": "varint", "mappings": {"0": "open", "1": "close"}}]}, {"name": "dialogue", "type": "string"}, {"name": "screen_name", "type": "string"}, {"name": "npc_name", "type": "string"}, {"name": "action_json", "type": "string"}]], "packet_edu_uri_resource_packet": ["container", [{"name": "resource", "type": "EducationSharedResourceURI"}]], "packet_create_photo": ["container", [{"name": "entity_unique_id", "type": "li64"}, {"name": "photo_name", "type": "string"}, {"name": "item_name", "type": "string"}]], "packet_update_subchunk_blocks": ["container", [{"name": "x", "type": "zigzag32"}, {"name": "y", "type": "zigzag32"}, {"name": "z", "type": "zigzag32"}, {"name": "blocks", "type": ["array", {"countType": "varint", "type": "BlockUpdate"}]}, {"name": "extra", "type": ["array", {"countType": "varint", "type": "BlockUpdate"}]}]], "packet_photo_info_request": ["container", [{"name": "photo_id", "type": "zigzag64"}]], "packet_subchunk": ["container", [{"name": "dimension", "type": "zigzag32"}, {"name": "x", "type": "zigzag32"}, {"name": "y", "type": "zigzag32"}, {"name": "z", "type": "zigzag32"}, {"name": "data", "type": "ByteArray"}, {"name": "request_result", "type": ["mapper", {"type": "zigzag32", "mappings": {"0": "undefined", "1": "success", "2": "chunk_not_found", "3": "invalid_dimension", "4": "player_not_found", "5": "y_index_out_of_bounds"}}]}, {"name": "heightmap_type", "type": ["mapper", {"type": "u8", "mappings": {"0": "no_data", "1": "has_data", "2": "too_high", "3": "too_low"}}]}, {"name": "heightmap", "type": ["switch", {"compareTo": "heightmap_type", "fields": {"has_data": ["buffer", {"count": 256}]}}]}, {"name": "cache_enabled", "type": "bool"}, {"name": "blob_id", "type": ["switch", {"compareTo": "cache_enabled", "fields": {"true": "lu64"}}]}]], "packet_subchunk_request": ["container", [{"name": "dimension", "type": "zigzag32"}, {"name": "x", "type": "zigzag32"}, {"name": "y", "type": "zigzag32"}, {"name": "z", "type": "zigzag32"}]]}}