declare namespace MinecraftData {

  type Attributes = Attribute[];
  
  interface Attribute {
    /**
     * The Mojang name of an attribute (usually is generic.[name] or minecraft:generic.[name]
     */
    resource: string;
    /**
     * The name of an attribute
     */
    name: string;
    /**
     * The minimum value of an attribute
     */
    min: number;
    /**
     * The maximum value of an attribute
     */
    max: number;
    /**
     * The default value of an attribute
     */
    default: number;
  }
  
  
  type Biomes = Biome[];
  
  interface Biome {
    /**
     * The unique identifier for a biome
     */
    id: number;
    /**
     * The name of a biome
     */
    name: string;
    /**
     * The category of a biome
     */
    category: string;
    /**
     * An indicator for the temperature in a biome
     */
    temperature: number;
    /**
     * The type of precipitation: none, rain or snow [before 1.19.4]
     */
    precipitation?: string;
    /**
     * True if a biome has any precipitation (rain or snow) [1.19.4+]
     */
    has_precipitation?: boolean;
    /**
     * The dimension of a biome: overworld, nether or end
     */
    dimension: string;
    /**
     * The display name of a biome
     */
    displayName: string;
    /**
     * The color in a biome
     */
    color: number;
    /**
     * How much rain there is in a biome [before 1.19.4]
     */
    rainfall?: number;
    [k: string]: unknown;
  }
  
  
  interface BlockCollisionShapes {
    /**
     * Each block's collision shape id(s).
     */
    blocks: {
      [k: string]: number | [number, ...number[]];
    };
    /**
     * Collision shapes by id, each shape being composed of a list of collision boxes.
     */
    shapes: {
      /**
       * The boxes of this shape.
       */
      [k: string]: [number, number, number, number, number, number][];
    };
  }
  
  
  type BlockLoot = BlockLootEntry[];
  
  interface BlockLootEntry {
    /**
     * The name of the block
     */
    block: string;
    /**
     * The states of the block (Bedrock Edition)
     */
    states?: {
      [k: string]: unknown;
    };
    /**
     * The list of item drops
     */
    drops: BlockItemDrop[];
  }
  interface BlockItemDrop {
    /**
     * The name of the item being dropped
     */
    item: string;
    /**
     * The metadata of the item being dropped (Bedrock Edition)
     */
    metadata?: number;
    /**
     * The percent chance of the item drop to occur
     */
    dropChance: number;
    /**
     * The min/max of number of items in this item drop stack
     */
    stackSizeRange: (number | null)[];
    /**
     * The required age of the block for the item drop to occur
     */
    blockAge?: number;
    /**
     * If silk touch is required
     */
    silkTouch?: boolean;
    /**
     * If not having silk touch is required
     */
    noSilkTouch?: boolean;
  }
  
  
  type BlockMappingsSchema =
    | []
    | [
        {
          pc: {
            name: string;
            states: {
              [k: string]: unknown;
            };
            [k: string]: unknown;
          };
          pe: {
            name: string;
            states: {
              [k: string]: unknown;
            };
            [k: string]: unknown;
          };
          [k: string]: unknown;
        }
      ];
  
  
  type Blocks = Block[];
  
  interface Block {
    /**
     * The unique identifier for a block
     */
    id: number;
    /**
     * The display name of a block
     */
    displayName: string;
    /**
     * The name of a block
     */
    name: string;
    /**
     * Hardness of a block
     */
    hardness: number | null;
    /**
     * Stack size for a block
     */
    stackSize: number;
    /**
     * true if a block is diggable
     */
    diggable: boolean;
    /**
     * BoundingBox of a block
     */
    boundingBox: "block" | "empty";
    /**
     * Material of a block
     */
    material?: string;
    /**
     * Using one of these tools is required to harvest a block, without that you get a 3.33x time penalty.
     */
    harvestTools?: {
      /**
       * This interface was referenced by `undefined`'s JSON-Schema definition
       * via the `patternProperty` "^[0-9]+$".
       */
      [k: string]: boolean;
    };
    variations?: {
      metadata: number;
      displayName: string;
      description?: string;
    }[];
    states?: {
      /**
       * The name of the property
       */
      name: string;
      /**
       * The type of the property
       */
      type: "enum" | "bool" | "int" | "direction";
      /**
       * The possible values of the property
       */
      values?: unknown[];
      /**
       * The number of possible values
       */
      num_values: number;
    }[];
    drops: (
      | number
      | {
          /**
           * minimum number or chance, default : 1
           */
          minCount?: number;
          /**
           * maximum number or chance, default : minCount
           */
          maxCount?: number;
          drop:
            | number
            | {
                id: number;
                metadata: number;
              };
        }
    )[];
    /**
     * true if a block is transparent
     */
    transparent: boolean;
    /**
     * Light emitted by that block
     */
    emitLight: number;
    /**
     * Light filtered by that block
     */
    filterLight: number;
    /**
     * Minimum state id
     */
    minStateId?: number;
    /**
     * Maximum state id
     */
    maxStateId?: number;
    /**
     * Default state id
     */
    defaultState?: number;
    /**
     * Blast resistance
     */
    resistance?: number | null;
  }
  
  
  interface HttpsExampleComPersonSchemaJson {
    graph?: RootNode;
    parsers: ParserInfo[];
    [k: string]: unknown;
  }
  interface RootNode {
    type: "root";
    name: string;
    executable: boolean;
    redirects: string[];
    children: (LiteralNode | ArgumentNode)[];
    [k: string]: unknown;
  }
  interface LiteralNode {
    type: "literal";
    name: string;
    executable: boolean;
    redirects: string[];
    children: (LiteralNode | ArgumentNode)[];
    [k: string]: unknown;
  }
  interface ArgumentNode {
    type: "argument";
    name: string;
    executable: boolean;
    redirects: string[];
    children: (LiteralNode | ArgumentNode)[];
    parser?: {
      parser?: string;
      modifier?: {
        [k: string]: unknown;
      } | null;
      [k: string]: unknown;
    };
    [k: string]: unknown;
  }
  interface ParserInfo {
    parser: string;
    modifier: {
      [k: string]: unknown;
    } | null;
    examples: string[];
    [k: string]: unknown;
  }
  
  
  type Effects = Effect[];
  
  interface Effect {
    /**
     * The unique identifier for an effect
     */
    id: number;
    /**
     * The display name of an effect
     */
    displayName: string;
    /**
     * The name of an effect
     */
    name: string;
    /**
     * Whether an effect is positive or negative
     */
    type: "good" | "bad";
  }
  
  
  type Enchantments = Enchantment[];
  
  interface Enchantment {
    /**
     * The unique identifier for an enchantment
     */
    id: number;
    /**
     * The name of an enchantment
     */
    name: string;
    /**
     * The display name of an enchantment
     */
    displayName: string;
    /**
     * The maximum level of an enchantment
     */
    maxLevel: number;
    /**
     * Min cost equation's coefficients a * level + b
     */
    minCost: {
      a?: number;
      b?: number;
      [k: string]: unknown;
    };
    /**
     * Max cost equation's coefficients a * level + b
     */
    maxCost: {
      a?: number;
      b?: number;
      [k: string]: unknown;
    };
    /**
     * Can only be found in a treasure, not created
     */
    treasureOnly: boolean;
    /**
     * Is a curse, not an enchantment
     */
    curse: boolean;
    /**
     * List of enchantment not compatibles
     */
    exclude: string[];
    /**
     * The category of enchantable items
     */
    category: string;
    /**
     * Weight of the rarity of the enchantment
     */
    weight: number;
    /**
     * Can this enchantment be traded
     */
    tradeable: boolean;
    /**
     * Can this enchantment be discovered
     */
    discoverable: boolean;
    [k: string]: unknown;
  }
  
  
  type Entities = Entity[];
  
  interface Entity {
    /**
     * The unique identifier for an entity
     */
    id: number;
    /**
     * The internal id of an entity : used in eggs metadata for example
     */
    internalId?: number;
    /**
     * The display name of an entity
     */
    displayName: string;
    /**
     * The name of an entity
     */
    name: string;
    /**
     * The type of an entity
     */
    type: string;
    /**
     * The width of the entity
     */
    width: number | null;
    /**
     * The height of the entity
     */
    height: number | null;
    /**
     * The length of the entity
     */
    length?: number | null;
    /**
     * The offset of the entity
     */
    offset?: number | null;
    /**
     * The category of an entity : a semantic category
     */
    category?: string;
    /**
     * The pc metadata tags of an entity. (Naming is via mc code, with data_ and id_ prefixes stripped)
     */
    metadataKeys?: string[];
  }
  
  
  type EntityLoot = EntityLootEntry[];
  
  interface EntityLootEntry {
    /**
     * The name of the entity
     */
    entity: string;
    /**
     * The list of item drops
     */
    drops: EntityItemDrop[];
  }
  interface EntityItemDrop {
    /**
     * The name of the item being dropped
     */
    item: string;
    /**
     * The metadata of the item being dropped (Bedrock Edition)
     */
    metadata?: number;
    /**
     * The percent chance of the item drop to occur
     */
    dropChance: number;
    /**
     * The min/max of number of items in this item drop stack
     */
    stackSizeRange: (number | null)[];
    /**
     * If a player killer is required
     */
    playerKill?: boolean;
  }
  
  
  type VersionForFeatureIdentification = string;
  type Features = FeatureEntry[];
  
  interface FeatureEntry {
    /**
     * The name of the feature
     */
    name?: string;
    /**
     * The description of the feature
     */
    description?: string;
    /**
     * A tuple that describes the range of versions in the range
     */
    versions?: VersionForFeatureIdentification[];
    [k: string]: unknown;
  }
  
  
  type Foods = Food[];
  
  interface Food {
    /**
     * The associated item ID for this food item
     */
    id: number;
    /**
     * The display name of an item
     */
    displayName: string;
    /**
     * Stack size for an item
     */
    stackSize: number;
    /**
     * The name of an item
     */
    name: string;
    /**
     * The amount of food points the food item replenishes
     */
    foodPoints: number;
    /**
     * The amount of saturation points the food restores (foodPoints * saturationRatio)
     */
    saturation: number;
    /**
     * foodPoints + saturation
     */
    effectiveQuality: number;
    /**
     * The 'saturation modifier' in Minecraft code, used to determine how much saturation an item has
     */
    saturationRatio: number;
    variations?: {
      metadata: number;
      displayName: string;
    }[];
  }
  
  
  type Instruments = Instrument[];
  
  interface Instrument {
    /**
     * The unique identifier for an instrument
     */
    id: number;
    /**
     * The name of an instrument
     */
    name: string;
    /**
     * The sound ID played by this instrument
     */
    sound?: string;
  }
  
  
  type Items = Item[];
  
  interface Item {
    /**
     * The unique identifier for an item
     */
    id: number;
    /**
     * The display name of an item
     */
    displayName: string;
    /**
     * Stack size for an item
     */
    stackSize: number;
    /**
     * describes categories of enchants this item can use
     */
    enchantCategories?: string[];
    /**
     * describes what items this item can be fixed with in an anvil
     */
    repairWith?: string[];
    /**
     * the amount of durability an item has before being damaged/used
     */
    maxDurability?: number;
    /**
     * The name of an item
     */
    name: string;
    variations?: {
      metadata: number;
      displayName: string;
      [k: string]: unknown;
    }[];
    [k: string]: unknown;
  }
  
  
  interface EnUs {
    /**
     * This interface was referenced by `EnUs`'s JSON-Schema definition
     * via the `patternProperty` "^[a-zA-Z.:0-9_-]+$".
     */
    [k: string]: string;
  }
  
  
  type MapIcons = MapIcon[];
  
  interface MapIcon {
    /**
     * The unique identifier for a map icon
     */
    id: number;
    /**
     * The name of a map icon
     */
    name: string;
    /**
     * Description of the map icon's appearance
     */
    appearance?: string;
    /**
     * Visibility in item frames
     */
    visibleInItemFrame: boolean;
  }
  
  
  interface Materials {
    [k: string]: Material;
  }
  /**
   * This interface was referenced by `Materials`'s JSON-Schema definition
   * via the `patternProperty` "^[a-z;/_]+$".
   */
  interface Material {
    /**
     * This interface was referenced by `Material`'s JSON-Schema definition
     * via the `patternProperty` "^[0-9]+$".
     */
    [k: string]: number;
  }
  
  
  type Particles = Particle[];
  
  interface Particle {
    /**
     * The unique identifier for a particle
     */
    id: number;
    /**
     * The name of a particle
     */
    name: string;
  }
  
  
  type ProtocolVersions = {
    /**
     * The protocol version
     */
    version: number;
    dataVersion?: number;
    minecraftVersion: string;
    majorVersion: string;
    usesNetty?: boolean;
    releaseType?: string;
  }[];
  
  
  
  
  
  
  type Recipe = ShapedRecipe | ShapelessRecipe;
  /**
   * An item can be represented different ways.
   */
  type RecipeItem = Id | IdMetadata | Id1Metadata1Count1;
  /**
   * A single numerical ID or null.
   */
  type Id = number | null;
  /**
   * A list of id and metadata. This is preferred if there are many items at once, e.g. in a shape.
   */
  type IdMetadata = [] | [Id] | [Id, Metadata];
  type Metadata = number;
  type Count = number;
  /**
   * A shape is a list of rows, which are lists of items. There must be at least one row with at least one item in it. All rows must have the same length. Empty rows at the beginning or end of a shape may be omitted. Empty colums at the end may also be omitted. When an item can be crafted in a 2x2 grid, the shape may not be larger than 2x2.
   *
   * @minItems 1
   * @maxItems 3
   */
  type Shape = [ShapeRow] | [ShapeRow, ShapeRow] | [ShapeRow, ShapeRow, ShapeRow];
  /**
   * @minItems 1
   * @maxItems 3
   */
  type ShapeRow = [RecipeItem] | [RecipeItem, RecipeItem] | [RecipeItem, RecipeItem, RecipeItem];
  /**
   * @minItems 1
   */
  type Ingredients = [RecipeItem, ...RecipeItem[]];
  
  /**
   * A dictionary of quoted numerical item IDs. Each ID maps to a list of recipes. There may be multiple different recipes per item (same ID and metadata). The recipes may not be sorted.
   */
  interface Recipes {
    /**
     * This interface was referenced by `Recipes`'s JSON-Schema definition
     * via the `patternProperty` "^[0-9]+$".
     */
    [k: string]: Recipe[] | NewRecipe;
  }
  /**
   * A shaped recipe is a dictionary of result, inShape and optionally outShape
   */
  interface ShapedRecipe {
    result: RecipeItem;
    inShape: Shape;
    outShape?: Shape;
  }
  /**
   * A dictionary of at least id, optionally metadata and count. This is preferred if there are not many items at once, e.g. result in a recipe.
   */
  interface Id1Metadata1Count1 {
    id: Id;
    metadata?: Metadata;
    count?: Count;
  }
  /**
   * A shapeless recipe is a dictionary of result and ingredients
   */
  interface ShapelessRecipe {
    result: RecipeItem;
    ingredients: Ingredients;
  }
  /**
   * Bedrock recipe schema
   */
  interface NewRecipe {
    /**
     * A unique identifier for this item
     */
    name: string | null;
    /**
     * What type of recipe and block this recipe relates to
     */
    type:
      | "multi"
      | "cartography_table"
      | "stonecutter"
      | "crafting_table"
      | "crafting_table_shapeless"
      | "shulker_box"
      | "furnace"
      | "blast_furnace"
      | "smoker"
      | "soul_campfire"
      | "campfire"
      | "smithing_table";
    /**
     * @minItems 1
     */
    ingredients: [unknown, ...unknown[]];
    input?: unknown[];
    output: unknown[];
    /**
     * Specific to bedrock edition
     */
    priority?: number;
  }
  
  
  type Sounds = Sound[];
  
  interface Sound {
    /**
     * The unique identifier for a sound
     */
    id: number;
    /**
     * The name of a sound
     */
    name: string;
  }
  
  
  interface Tints {
    grass?: {
      data?: {
        keys?: string[];
        color?: number;
      }[];
      default?: number;
    };
    foliage?: {
      data?: {
        keys?: string[];
        color?: number;
      }[];
      default?: number;
    };
    water?: {
      data?: {
        keys?: string[];
        color?: number;
      }[];
      default?: number;
    };
    redstone?: {
      data?: {
        keys?: number[];
        color?: number;
      }[];
      default?: number;
    };
    constant?: {
      data?: {
        keys?: string[];
        color?: number;
      }[];
      default?: number;
    };
  }
  
  
  interface Version {
    /**
     * The protocol version
     */
    version?: number;
    minecraftVersion?: string;
    majorVersion?: string;
    releaseType?: string;
  }
  
  
  /**
   * @minItems 1
   */
  type Windows = [Window, ...Window[]];
  
  interface Window {
    /**
     * The unique identifier for the window
     */
    id: string;
    /**
     * The default displayed name of the window
     */
    name: string;
    /**
     * The slots displayed in the window
     *
     * @minItems 1
     */
    slots?: [
      {
        /**
         * The name of the slot or slot range
         */
        name: string;
        /**
         * The position of the slot or begin of the slot range
         */
        index: number;
        /**
         * The size of the slot range
         */
        size?: number;
      },
      ...{
        /**
         * The name of the slot or slot range
         */
        name: string;
        /**
         * The position of the slot or begin of the slot range
         */
        index: number;
        /**
         * The size of the slot range
         */
        size?: number;
      }[]
    ];
    /**
     * Names of the properties of the window
     *
     * @minItems 1
     */
    properties?: [string, ...string[]];
    openedWith?: {
      type: "item" | "entity" | "block";
      id: number;
    }[];
  }
  

  export interface SupportsFeature {
    /** `1.9 - latest`
     * the chunk format uses local palettes */
    "usesPalettedChunks": boolean;
    /** `1.8 - 1.12.2`
     * spawner is called mob_spawner */
    "mobSpawner": boolean;
    /** `1.13 - latest`
     * spawner is called spawner */
    "spawner": boolean;
    /** `1.9 - latest`
     * the elytra exists and players can fly with it */
    "hasElytraFlying": boolean;
    /** `1.11 - 1.13.2`
     * firework attached entity metadata is at index 7 and is varint */
    "fireworkMetadataVarInt7": boolean;
    /** `1.14 - 1.16.5`
     * firework attached entity metadata is at index 8 and is optvarint */
    "fireworkMetadataOptVarInt8": boolean;
    /** `1.17 - latest`
     * firework attached entity metadata is at index 9 and is optvarint */
    "fireworkMetadataOptVarInt9": boolean;
    /** `1.11 - 1.12.2`
     * the firework entity used for elytra boosting is named fireworks_rocket */
    "fireworkNamePlural": boolean;
    /** `1.13 - latest`
     * the firework entity used for elytra boosting is named firework_rocket */
    "fireworkNameSingular": boolean;
    /** `1.8 - 1.12.2`
     * block metadata is encoded in a separate metadata */
    "blockMetadata": boolean;
    /** `1.13 - latest`
     * block metadata is encoded as state id */
    "blockStateId": boolean;
    /** `1.13 - latest`
     * can sleep near mobs in creative */
    "creativeSleepNearMobs": boolean;
    /** `1.8 - 1.8.9`
     * Entity positions are represented with fixed point numbers */
    "fixedPointPosition": boolean;
    /** `1.9 - latest`
     * Entity positions are represented with double */
    "doublePosition": boolean;
    /** `1.8 - 1.8.9`
     * Delta of position are represented with fixed point numbers */
    "fixedPointDelta": boolean;
    /** `1.9 - latest`
     * Delta of position are represented with fixed point numbers times 128 */
    "fixedPointDelta128": boolean;
    /** `1.8 - 1.12.2`
     * custom channel are prefixed by MC| */
    "customChannelMCPrefixed": boolean;
    /** `1.13 - latest`
     * custom channel is an identifier starting in minecraft namespace */
    "customChannelIdentifier": boolean;
    /** `1.17 - latest`
     * dimensionData is available, providing an additional information about the current dimension */
    "dimensionDataIsAvailable": boolean;
    /** `1.19 - latest`
     * dimensionData like world height is exclusively in the codec in login packet */
    "dimensionDataInCodec": boolean;
    /** `1.8 - 1.8.9`
     * use item is done with block place packet */
    "useItemWithBlockPlace": boolean;
    /** `1.9 - latest`
     * use item is done with its own packet */
    "useItemWithOwnPacket": boolean;
    /** `1.8 - 1.8.9`
     * block_place packet has heldItem */
    "blockPlaceHasHeldItem": boolean;
    /** `1.9 - 1.10.2`
     * block_place packet has hand and int cursor */
    "blockPlaceHasHandAndIntCursor": boolean;
    /** `1.11 - 1.13.2`
     * block_place packet has hand and float cursor */
    "blockPlaceHasHandAndFloatCursor": boolean;
    /** `1.14 - latest`
     * block_place packet has inside block */
    "blockPlaceHasInsideBlock": boolean;
    /** `1.8 - 1.8.9`
     * teleport is done using position packet */
    "teleportUsesPositionPacket": boolean;
    /** `1.8 - 1.11.2`
     * the position is sent every tick */
    "positionUpdateSentEveryTick": boolean;
    /** `1.9 - latest`
     * teleport is done using its own packet */
    "teleportUsesOwnPacket": boolean;
    /** `1.8 - 1.12.2`
     * one block of several variations */
    "oneBlockForSeveralVariations": boolean;
    /** `1.13 - latest`
     * all variations of a packet have their own id */
    "blockSchemeIsFlat": boolean;
    /** `1.8 - 1.12.2`
     * tab complete doesn't have a tool tip */
    "tabCompleteHasNoToolTip": boolean;
    /** `1.13 - latest`
     * tab complete has a tool tip */
    "tabCompleteHasAToolTip": boolean;
    /** `1.8 - 1.12.2`
     * effect are prefixed by minecraft: */
    "effectAreMinecraftPrefixed": boolean;
    /** `1.13 - latest`
     * effect are not prefixed */
    "effectAreNotPrefixed": boolean;
    /** `1.8 - 1.12.2`
     * items are also blocks */
    "itemsAreAlsoBlocks": boolean;
    /** `1.13 - latest`
     * items are not block */
    "itemsAreNotBlocks": boolean;
    /** `1.14 - latest`
     * the fishing hook entity is named fishing_bobber */
    "fishingBobberCorrectlyNamed": boolean;
    /** `1.8 - 1.12.2`
     * book editing is handled through plugin channels */
    "editBookIsPluginChannel": boolean;
    /** `1.13 - latest`
     * book editing is handled through a packet with the updated book item */
    "hasEditBookPacket": boolean;
    /** `1.13 - 1.17.1`
     * edit_book packet sends a new book item (with its NBT containing page data) to server */
    "editBookPacketUsesNbt": boolean;
    /** `1.8 - 1.8.9`
     * when sending MC|BSign, item type should be written_book */
    "clientUpdateBookIdWhenSign": boolean;
    /** `1.8 - 1.12.2`
     * select trade through plugin channel MC|TrSel */
    "useMCTrSel": boolean;
    /** `1.8 - 1.12.2`
     * receive trade list through plugin channel MC|TrList */
    "useMCTrList": boolean;
    /** `1.13 - 1.13.2`
     * receive trade list through plugin channel usetrader_list */
    "usetraderlist": boolean;
    /** `1.8 - 1.8.9`
     * doesn't have an off-hand slot */
    "doesntHaveOffHandSlot": boolean;
    /** `1.16 - latest`
     * multi block changes has trust edges field */
    "multiBlockChangeHasTrustEdges": boolean;
    /** `1.8 - 1.15.2`
     * dimension is an int (the index of an enum) */
    "dimensionIsAnInt": boolean;
    /** `1.16 - 1.16.1`
     * dimension is a string (the dimension is the same as the world name) */
    "dimensionIsAString": boolean;
    /** `1.16.2 - latest`
     * dimension is an nbt compound */
    "dimensionIsAWorld": boolean;
    /** `1.8 - 1.12.2`
     * chests don't have a type property */
    "doesntHaveChestType": boolean;
    /** `1.8 - 1.8.9`
     * packet MC|AdvCmd was incorrectly spelled in 1.8 as MC|AdvCdm */
    "usesAdvCdm": boolean;
    /** `1.9 - 1.12.2`
     * uses MC|AdvCmd to set command block information */
    "usesAdvCmd": boolean;
    /** `1.8 - 1.11.2`
     * gives a index for each trade in a villagers metadata */
    "indexesVillagerRecipes": boolean;
    /** `1.9 - latest`
     * if there is a cooldown after attacks to deal full damage */
    "hasAttackCooldown": boolean;
    /** `1.16 - latest`
     * uses the login packet as defined in mcData */
    "usesLoginPacket": boolean;
    /** `1.16.2 - latest`
     * in the multi_block_change packet is stored as a single number */
    "usesMultiblockSingleLong": boolean;
    /** `1.16.2 - latest`
     * in the multi_block_change packet, all 3 axis coords are defined */
    "usesMultiblock3DChunkCoords": boolean;
    /** `1.8 - 1.12.2`
     * the parameter metadata of the setblock command is a number */
    "setBlockUsesMetadataNumber": boolean;
    /** `1.8 - 1.12.2`
     * send item name for anvil using plugin channel MC|TrList */
    "useMCItemName": boolean;
    /** `1.14 - latest`
     * selecting a trade automatically puts the required items into trading slots */
    "selectingTradeMovesItems": boolean;
    /** `1.8 - 1.9.4`
     * resource pack uses hash validation */
    "resourcePackUsesHash": boolean;
    /** `1.20.3 - latest`
     * resource pack uses UUID identification */
    "resourcePackUsesUUID": boolean;
    /** `1.8 - 1.10.2`
     * max chars in chat */
    "lessCharsInChat": boolean;
    /** `1.13 - latest`
     * teams use chatcomponents for formatting */
    "teamUsesChatComponents": boolean;
    /** `1.8 - 1.8.9`
     * teams use scoreboard_team packet */
    "teamUsesScoreboard": boolean;
    /** `1.11 - 1.12.2`
     * this is when the end_crystal's entity name is ender_crystal */
    "enderCrystalNameEndsInErNoCaps": boolean;
    /** `1.14 - latest`
     * this is when the end_crystal's entity name is end_crystal */
    "enderCrystalNameNoCapsWithUnderscore": boolean;
    /** `1.8 - 1.10.2`
     * this is when some entities names would be captialized and appended without underscores like 'Boat' or 'ArmorStand' */
    "entityNameUpperCaseNoUnderscore": boolean;
    /** `1.13 - 1.13.2`
     * this is when some entity names are lowercase and appended without underscores like 'armorstand' or 'endercrystal' */
    "entityNameLowerCaseNoUnderscore": boolean;
    /** `1.8 - 1.16.5`
     * this is when the description packet existed */
    "transactionPacketExists": boolean;
    /** `1.17.1 - latest`
     * starting in 1.17.1, actionId has been replaced with stateId */
    "stateIdUsed": boolean;
    /** `1.8 - 1.16.5`
     * pre 1.17, actionId is used */
    "actionIdUsed": boolean;
    /** `1.17 - latest`
     * use setslot as transaction instead of just hoping it'll work */
    "setSlotAsTransaction": boolean;
    /** `1.8 - 1.8.9`
     * arm animation packet sent before use entity packet */
    "armAnimationBeforeUse": boolean;
    /** `1.18 - latest`
     * world y defaults to starts at -64 and ends at 384 */
    "tallWorld": boolean;
    /** `1.8 - 1.8.9`
     * sign text send when updating signs is send as stringified strings */
    "sendStringifiedSignText": boolean;
    /** `1.13 - latest`
     * undefined */
    "usesBlockStates": boolean;
    /** `1.17 - latest`
     * undefined */
    "effectNamesMatchRegistryName": boolean;
    /** `1.9 - latest`
     * undefined */
    "shieldSlot": boolean;
    /** `1.14 - latest`
     * undefined */
    "village&pillageInventoryWindows": boolean;
    /** `1.16 - latest`
     * undefined */
    "netherUpdateInventoryWindows": boolean;
    /** `1.8 - 1.8.9`
     * Chunk unloading is done by sending an empty chunk */
    "unloadChunkByEmptyChunk": boolean;
    /** `1.9 - latest`
     * Chunk unloading is done by sending directly an unload chunk packet */
    "unloadChunkDirect": boolean;
    /** `1.8 - 1.10.2`
     * entity names are in camel case */
    "entityCamelCase": boolean;
    /** `1.11 - latest`
     * entity name are in snake case */
    "entitySnakeCase": boolean;
    /** `1.8 - 1.8.9`
     * respawn field is payload */
    "respawnIsPayload": boolean;
    /** `1.9 - latest`
     * respawn field is action id */
    "respawnIsActionId": boolean;
    /** `1.8 - 1.8.9`
     * attach is used to stack entities */
    "attachStackEntity": boolean;
    /** `1.9 - latest`
     * set passengers is used to stack entities */
    "setPassengerStackEntity": boolean;
    /** `1.13 - latest`
     * many items got merged, separated or renamed */
    "theFlattening": boolean;
    /** `1.8 - 1.10.2`
     * block_place packet has int cursor */
    "blockPlaceHasIntCursor": boolean;
    /** `1.14 - latest`
     * the client's chunk position must be updated to render chunks correctly */
    "updateViewPosition": boolean;
    /** `1.14 - latest`
     * chunk light data is sent in a separate packet */
    "lightSentSeparately": boolean;
    /** `1.14 - latest`
     * game difficulty is sent separately from the login packet */
    "difficultySentSeparately": boolean;
    /** `1.15 - 1.17_major`
     * biomes sent in own packet */
    "biomesSentSeparately": boolean;
    /** `1.14 - latest`
     * player digging packets should be responded to */
    "acknowledgePlayerDigging": boolean;
    /** `1.14 - latest`
     * there are 6 types of signs based on the different trees */
    "multiTypeSigns": boolean;
    /** `1.15 - latest`
     * entity metadata is sent separately from the spawn packets */
    "entityMetadataSentSeparately": boolean;
    /** `1.16 - latest`
     * entity attributes are in snake case */
    "attributeSnakeCase": boolean;
    /** `1.16 - latest`
     * entity equipment packet contains all equipment slots instead of just one */
    "allEntityEquipmentInOne": boolean;
    /** `1.12 - latest`
     * entity prefixed with minecraft: on this versions */
    "entityMCPrefixed": boolean;
    /** `1.8 - 1.8.9`
     * in never versions its nbt but in 1.8 its on metadata */
    "nbtOnMetadata": boolean;
    /** `1.13 - latest`
     * added shulker boxes to the game */
    "theShulkerBoxes": boolean;
    /**
     * item.metadata[this_ix] will be the item that was dropped on the ground */
    "metadataIxOfItem": 8 | 7 | 6 | 5 | 8;
    /** `1.14_major - latest`
     * item serialization in [even] newer versions uses present field [exclusively] to show nullability rather than sending blockId as -1 */
    "itemSerializationWillOnlyUsePresent": boolean;
    /** `1.13_major - latest`
     * item serialization in newer versions uses present field to show nullability rather or sending blockId as -1 can be used */
    "itemSerializationAllowsPresent": boolean;
    /** `1.8_major - 1.12_major`
     * item serialization in older versions uses blockId field to show nullability by setting blockId to -1 */
    "itemSerializationUsesBlockId": boolean;
    /** `1.13_major - latest`
     * in newer versions, an nbt key called 'Damage' is used to store durability */
    "saveDurabilityAsDamage": boolean;
    /**
     * what the nbt key for enchants is */
    "nbtNameForEnchant": "Enchantments" | "ench";
    /** `1.8_major - latest`
     * Enchanted books store enchantment data in a separate NBT tag called StoredEnchantments */
    "booksUseStoredEnchantments": boolean;
    /**
     * type of value that stores enchant lvl in the nbt */
    "typeOfValueForEnchantLevel": "string" | "short";
    /**
     * where the durability is saved in nbt */
    "whereDurabilityIsSerialized": "Damage" | "metadata";
    /** `1.14_major - latest`
     * An item's custom Lore is stored as a string in NBT, in older versions it's a list of strings */
    "itemLoreIsAString": boolean;
    /** `1.8_major - 1.8_major`
     * in older versions, spawn eggs have a field in their nbt called 'internalId' which tells what entity they will spawn */
    "spawnEggsUseInternalIdInNbt": boolean;
    /** `1.9_major - 1.12_major`
     * in older versions, spawn eggs have a key in their nbt called EntityTag which is an object with a field called id, which is an identifier like 'minecraft:cow' that tells the client what mob this egg will spawn */
    "spawnEggsUseEntityTagInNbt": boolean;
    /** `1.13_major - latest`
     * in newer versions, spawn eggs have the entity they spawn in their name, ex: 'squid_spawn_egg' */
    "spawnEggsHaveSpawnedEntityInName": boolean;
    /** `1.19_major - latest`
     * starting in 1.19 chat messages generally carry a cryptographic signature, `packet_chat` has been replaced clientbound by `packet_player_chat` and `packet_system_chat` and serverbound by `packet_chat_message` */
    "signedChat": boolean;
    /** `1.19 - 1.19.2`
     * allows for usage of `signature` instead of verifyToken in serverbound packet_encryption_begin */
    "signatureEncryption": boolean;
    /** `1.19.2 - latest`
     * uses `signatureV2` public key signature */
    "profileKeySignatureV2": boolean;
    /** `1.16_major - latest`
     * clientbound chat packet contains message sender's UUID */
    "clientboundChatHasSender": boolean;
    /** `1.19 - latest`
     * One packet to add living and non-living entities */
    "consolidatedEntitySpawnPacket": boolean;
    /** `1.19.2`
     * Signed messages are ordered and depend on previous messages, and message payloads are hashed before generating a signature */
    "chainedChatWithHashing": boolean;
    /** `1.19.3 - latest`
     * Users generate a session with a unique ID upon login. Chat messages are signed using this ID and an index that is increased with each message */
    "useChatSessions": boolean;
    /** `1.19.3 - latest`
     * The player_info packet may contain multiple actions in one packet. The actions field is a bitfield signifying what actions are included */
    "playerInfoActionIsBitfield": boolean;
    /** `1.19_major - latest`
     * Chat messages are formatted on the client side */
    "clientsideChatFormatting": boolean;
    /** `1.19.3 - latest`
     * There exists a serializer of type long for entity metadata */
    "entityMetadataHasLong": boolean;
    /** `1.19.4 - latest`
     * Has a Bundle Packet to group packets for processing at once */
    "hasBundlePacket": boolean;
    /** `1.19.4 - latest`
     * Entity Metadata is defined in mcdata */
    "mcDataHasEntityMetadata": boolean;
    /** `1.20 - latest`
     * Signs can have text on the front and back */
    "multiSidedSigns": boolean;
    /** `1.15_major - latest`
     * Items with maximum durability have explicit NBT data Damage:0 */
    "explicitMaxDurability": boolean;
    /** `1.17 - latest`
     * in 1.17, light encoding changed to handle new world height */
    "newLightingDataFormat": boolean;
    /** `1.20.2 - latest`
     * in 1.20.2, a new configuration state was added to allow configuration after login */
    "hasConfigurationState": boolean;
    /** `1.20.2 - latest`
     * Players and entities spawn with the same spawn_entity packet */
    "unifiedPlayerAndEntitySpawnPacket": boolean;
    /** `1.20.3 - latest`
     * Chat message packets use NBT to serialize chat components instead of JSON */
    "chatPacketsUseNbtComponents": boolean;
    /** `1.20.5 - latest`
     * Signed chat commands use new `packet_chat_command_signed` packet */
    "seperateSignedChatCommandPacket": boolean;
    /** `1.20.5 - latest`
     * Spawn and respawn packet now use shared worldState type for their data, and dimensions are now integers */
    "spawnRespawnWorldDataField": boolean;
    /** `1.20.5 - latest`
     * Particle packet structure contains uses strings instead of integers in protocol */
    "updatedParticlesPacket": boolean;
    /** `1.20.5 - latest`
     * Codec data is now split into multiple NBTs by ID instead of one */
    "segmentedRegistryCodecData": boolean;
    /** `1.20.5 - latest`
     * New Item schema */
    "itemsWithComponents": boolean;
    /** `1.14 - latest`
     * Support the execute command */
    "hasExecuteCommand": boolean;
    /** `1.21 - latest`
     * The chat type field in the `player_chat` packet now starts at 1 instead of 0 */
    "incrementedChatType": boolean;
    /** `1.21 - latest`
     * The server needs to send the registry data to the client before sending the finish_configuration packet */
    "registryDataIsMandatory": boolean;
  }

  type Object = { [key: string]: any }
  
  export interface Version {
    // Returns true if the current version is greater than or equal to the `other` version's dataVersion
    ['>='](other: string): boolean
    // Returns true if the current version is greater than the `other` version's dataVersion
    ['>'](other: string): boolean
    // Returns true if the current version is less than the `other` version's dataVersion
    ['<'](other: string): boolean
    // Returns true if the current version is less than than or equal to the `other` version's dataVersion
    ['<='](other: string): boolean
    // Returns true if the current version is equal to the `other` version's dataVersion
    ['=='](other: string): boolean
    type: 'pc' | 'bedrock'
    version?: number
    dataVersion?: number
    majorVersion?: string
    minecraftVersion?: string
  }
  
  export interface VersionSet {
    pc: { [version: string]: Version }
    bedrock: { [version: string]: Version }
  }
  
  export interface SupportedVersions {
    pc: string[]
    bedrock: string[]
  }
  
  export interface Schemas {
    biomes: Object
    blocks: Object
    effects: Object
    entities: Object
    instruments: Object
    items: Object
    materials: Object
    protocol: Object
    protocolVersions: Object
    recipes: Object
    version: Object
    windows: Object
    foods: Object
    blockLoot: Object
    entityLoot: Object
  }
  
  export interface LoginPacket {
    entityId: number
  
    /**
     * introduced in Minecraft 1.16.2
     */
    isHardcore?: boolean
  
    gameMode: number
  
    /**
     * Introduced in Minecraft 1.17
     */
    previousGameMode?: number
    /**
     * Introduced in Minecraft 1.17
     */
    worldNames?: string[]
    /**
     * Introduced in Minecraft 1.17
     */
    dimensionCodec?: Object
  
    dimension: Object
  
    /**
     * Introduced in Minecraft 1.17
     */
    worldName?: string
  
    hashedSeed: number
    maxPlayers: number
    viewDistance: number
  
    /**
     * Introduced in Minecraft 1.18
     */
    simulationDistance?: number
  
    reducedDebugInfo: boolean
    enableRespawnScreen: boolean
  
    /**
     * Introduced in Minecraft 1.17
     */
    isDebug?: boolean
    /**
     * Introduced in Minecraft 1.17
     */
    isFlat?: boolean
  }
  
  type MakeRequired<T, K extends keyof T> = T & Required<Pick<T, K>>
  
  type IndexedBlock = MakeRequired<Block, 'minStateId' | 'maxStateId' | 'defaultState'>
  
  export interface IndexedData {
    isOlderThan(version: string): boolean
    isNewerOrEqualTo(version: string): boolean
  
    blocks: { [id: number]: IndexedBlock }
    blocksByName: { [name: string]: IndexedBlock }
    blocksByStateId: { [id: number]: IndexedBlock }
    blocksArray: IndexedBlock[]
    blockMappings: {
      pc: { name: string, states: Record<string, string | number> },
      pe: { name: string, states: Record<string, string | number> }
    }[]
    /**
     * Bedrock edition only
     */
    blockStates?: { name: string; states: object; version: number }[]
    /**
     * id is the shape id of the block converted to a string
     */
    blockCollisionShapes: { blocks: { [name: string]: number | number[] }; shapes: { [id: string]: [number[]] } }
  
    loginPacket: LoginPacket
  
    items: { [id: number]: Item }
    itemsByName: { [name: string]: Item }
    itemsArray: Item[]
  
    foods: { [id: number]: Food }
    foodsByName: { [name: string]: Food }
    foodsArray: Food[]
  
    biomes: { [id: number]: Biome }
    biomesArray: Biome[]
    biomesByName: { [name: string]: Biome }
  
    recipes: { [id: number]: Recipe[] }
  
    instruments: { [id: number]: Instrument }
    instrumentsArray: Instrument[]
  
    materials: { [name: string]: Material }
  
    mobs: { [id: number]: Entity }
    objects: { [id: number]: Entity }
    entities: { [id: number]: Entity }
    entitiesByName: { [name: string]: Entity }
    entitiesArray: Entity[]
  
    enchantments: { [id: number]: Enchantment }
    enchantmentsByName: { [name: string]: Enchantment }
    enchantmentsArray: Enchantment[]
  
    /**
     * Bedrock edition only
     */
    defaultSkin?: Object
  
    protocol: Object
    protocolComments: Object
    /**
     * Bedrock edition only
     */
    protocolYaml?: string[]
  
    windows: { [id: string]: Window }
    windowsByName: { [name: string]: Window }
    windowsArray: Window[]
  
    effects: { [id: number]: Effect }
    effectsByName: { [name: string]: Effect }
    effectsArray: Effect[]
  
    particles: { [id: number]: Particle }
    particlesByName: { [name: string]: Particle }
    particlesArray: Particle[]
  
    attributes: { [resource: string]: string }
    attributesByName: { [name: string]: string }
    attributesArray: Attribute[]
  
    commands: {}
  
    version: Version
  
    type: 'pc' | 'bedrock'
  
    language: { [key: string]: string }
  
    blockLoot: { [name: string]: BlockLootEntry }
  
    entityLoot: { [name: string]: EntityLootEntry }
  
    mapIcons: { [id: number]: MapIcon }
    mapIconsByName: { [name: string]: MapIcon }
    mapIconsArray: MapIcon[]
  
    tints: Tints
  
    supportFeature: <T extends keyof SupportsFeature>(key: T) => SupportsFeature[T]
  }
  
  const versions: {
    [key in keyof SupportedVersions]: ProtocolVersions
  }
  const versionsByMinecraftVersion: VersionSet
  const preNettyVersionsByProtocolVersion: VersionSet
  const postNettyVersionsByProtocolVersion: VersionSet
  const supportedVersions: SupportedVersions
  const legacy: { pc: { blocks: { [id: string]: string } } }
  const schemas: Schemas
  
}

declare function MinecraftData(version: string | number): MinecraftData.IndexedData;
export = MinecraftData