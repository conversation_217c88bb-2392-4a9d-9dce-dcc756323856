name: npm-publish
on:
  push:
    branches:
      - master
  workflow_dispatch:
jobs:
  npm-publish:
    name: npm-publish
    runs-on: ubuntu-latest
    steps:
    - name: Checkout repository
      uses: actions/checkout@master
      with:
        submodules: true
    - name: Set up Node.js
      uses: actions/setup-node@master
      with:
        node-version: 18.0.0
    - run: npm install
    - id: publish
      uses: JS-DevTools/npm-publish@v1
      with:
        token: ${{ secrets.NPM_AUTH_TOKEN }}
    - name: Create Release
      if: steps.publish.outputs.type != 'none'
      id: create_release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ steps.publish.outputs.version }}
        release_name: Release ${{ steps.publish.outputs.version }}
        body: ${{ steps.publish.outputs.version }}
        draft: false
        prerelease: false

    - name: "Trigger new GH Page creation in mcData repo"
      uses: peter-evans/repository-dispatch@v1
      if: steps.publish.outputs.type != 'none' || github.event_name == 'workflow_dispatch'
      with:
        token: ${{ secrets.PAT_PASSWORD }}
        repository: PrismarineJS/minecraft-data
        event-type: node-mcData-release
