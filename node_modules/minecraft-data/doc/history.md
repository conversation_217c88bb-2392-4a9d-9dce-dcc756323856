# History

## 3.80.0

* update `minecraft-data`

## 3.79.0

* update `minecraft-data`

## 3.78.0

* update `minecraft-data`

## 3.77.0

* update `minecraft-data`

## 3.76.0

* update `minecraft-data`

## 3.75.0

* update `minecraft-data`

## 3.74.0

* update `minecraft-data`

## 3.72.0

* update `minecraft-data`

## 3.71.0

* update `minecraft-data`

## 3.70.0

* update `minecraft-data`

## 3.69.0

* update `minecraft-data`

## 3.68.0

* update `minecraft-data`

## 3.67.0

* update `minecraft-data`

## 3.66.0

* update `minecraft-data`

## 3.65.0

* update `minecraft-data`

## 3.64.1

* update `minecraft-data`

## 3.64.0

* update `minecraft-data`

## 3.63.0

* update `minecraft-data`

## 3.62.0

* update `minecraft-data`

## 3.61.2

* update `minecraft-data`

## 3.61.1

* update `minecraft-data`

## 3.61.0

* update `minecraft-data`

## 3.60.0

* update `minecraft-data`

## 3.59.3

* update `minecraft-data`

## 3.59.2

* update `minecraft-data`

## 3.59.1

* update `minecraft-data`

## 3.59.0

* update `minecraft-data`

## 3.58.0

* update `minecraft-data`

## 3.57.0

* update `minecraft-data`

## 3.56.0

* update `minecraft-data`

## 3.55.0

* update `minecraft-data`

## 3.54.0

* update `minecraft-data`

## 3.53.0

* update `minecraft-data`

## 3.52.0

* update `minecraft-data`

## 3.51.0

* update `minecraft-data`

## 3.50.0

* update `minecraft-data`

## 3.49.1

* update `minecraft-data`

## 3.49.0

* update `minecraft-data`

## 3.48.0

* update `minecraft-data`

## 3.47.0

* update `minecraft-data`

## 3.46.2

* update `minecraft-data`

## 3.46.1

* update `minecraft-data`

## 3.46.0

* update `minecraft-data`

## 3.45.0

* update `minecraft-data`

## 3.44.0

* update `minecraft-data`

## 3.43.1

* update `minecraft-data`

## 3.43.0

* update `minecraft-data`
* Add legacy bedrock block mappings (thanks @Flonja)

## 3.42.1

* update `minecraft-data`

## 3.41.0

* update `minecraft-data`

## 3.40.0

* update `minecraft-data`

## 3.39.0

* update `minecraft-data`

## 3.38.0

* update `minecraft-data`

## 3.37.0

* update `minecraft-data`

## 3.36.1

* update `minecraft-data`

## 3.36.0

* update `minecraft-data`

## 3.35.0

* update `minecraft-data`

## 3.34.0

* update `minecraft-data`

## 3.33.0

* update `minecraft-data`

## 3.32.0

* update `minecraft-data`

## 3.31.0

* update `minecraft-data`

## 3.30.0

* update `minecraft-data`

## 3.29.0

* update `minecraft-data`

## 3.28.0

* update `minecraft-data`

## 3.27.0

* update `minecraft-data`

## 3.26.0

* update `minecraft-data`

## 3.25.2

* update `minecraft-data`

## 3.25.1

* update `minecraft-data`

## 3.25.0

* update `minecraft-data`

## 3.24.0

* update `minecraft-data`

## 3.23.0

* update `minecraft-data`

## 3.22.0

* update `minecraft-data`

## 3.21.0

* update `minecraft-data`

## 3.20.0

* update `minecraft-data`

## 3.19.0

* update `minecraft-data`

## 3.18.0

* update `minecraft-data`

## 3.17.0

* update `minecraft-data`

## 3.16.0

* update `minecraft-data`

## 3.15.3

* update `minecraft-data`

## 3.15.2

* Fix entities by id

## 3.15.1

* update `minecraft-data`

## 3.15.0

* update `minecraft-data`

## 3.14.1

* Support supportFeature on bedrock

## 3.14.0

* update `minecraft-data`

## 3.13.0

* update `minecraft-data`

## 3.12.0

* update `minecraft-data`

## 3.11.0

* update `minecraft-data`

## 3.10.2

* update `minecraft-data`

## 3.10.1

* update `minecraft-data`

## 3.9.1

* update `minecraft-data`

## 3.9.0

* update `minecraft-data`

## 3.8.0

* update `minecraft-data`

## 3.7.3

* update `minecraft-data`

## 3.7.2

* update `minecraft-data`

## 3.7.1

* update `minecraft-data`

## 3.7.0

* update `minecraft-data`

## 3.6.0

* update `minecraft-data`

## 3.5.1

* update `minecraft-data`

## 3.5.0

* update `minecraft-data`

## 3.4.0

* update `minecraft-data`

## 3.3.0

* update `minecraft-data`

## 3.2.0

* update `minecraft-data`

## 3.1.1

* update `minecraft-data`

## 3.0.0

* update `minecraft-data`

## 2.221.0

* update `minecraft-data`

## 2.220.0

* update `minecraft-data`

## 2.119.1

* Alias versions that end in .0 to a version without the ending .0

## 2.119.0

* update `minecraft-data`

## 2.118.0

* update `minecraft-data`

## 2.117.1

* Add support for custom supportFeature return type

## 2.117.0

* update `minecraft-data`

## 2.116.0

* update `minecraft-data`

## 2.115.2

* Add supportFeature support

## 2.115.1

* update `minecraft-data`

## 2.114.1

* update `minecraft-data`

## 2.114.0

* update `minecraft-data`

## 2.113.3

* update `minecraft-data`

## 2.113.2

* update `minecraft-data`

## 2.113.1

* update `minecraft-data`
* Add block state IDs to all versions with block data

## 2.113.0

* update `minecraft-data`

## 2.112.0

* update `minecraft-data`

## 2.111.0

* update `minecraft-data`

## 2.110.0

* update `minecraft-data`

## 2.109.0

* update `minecraft-data`

## 2.108.0

* update `minecraft-data`

## 2.107.0

* update `minecraft-data`

## 2.106.0

* update `minecraft-data`

## 2.105.0

* update `minecraft-data`

## 2.104.0

* update `minecraft-data`

## 2.103.0

* update `minecraft-data`

## 2.102.0

* update `minecraft-data`

## 2.101.0

* update `minecraft-data`

## 2.100.1

* add attribute support (@U5B)

## 2.100.0

* update `minecraft-data`

## 2.99.3

* update `minecraft-data`

## 2.99.2

* update `minecraft-data`

## 2.99.1

* update `minecraft-data`

## 2.99.0

* update `minecraft-data`

## 2.98.1

* update `minecraft-data`

## 2.98.0

* update `minecraft-data`

## 2.97.0

* update `minecraft-data`

## 2.96.0

* update `minecraft-data`

## 2.95.0

* update `minecraft-data`

## 2.94.0

* update `minecraft-data`

## 2.93.1

* Update bedrock item and version handling (@extremeheat)

## 2.93.0

* update `minecraft-data`

## 2.92.1

* fix generate by not using pathToFileURL

## 2.92.0

* update `minecraft-data`
* try dynamic require one more time

## 2.91.1

* add back node 12 support (@u9g)

## 2.91.0

* update `minecraft-data`

## 2.90.0

* update `minecraft-data`

## 2.89.4

* Non-exclusively prefer release versions over snapshots

## 2.89.3

* Do not use snapshots when determining version

## 2.89.2

* update `minecraft-data` and bedrock support

## 2.88.0

* update `minecraft-data`

## 2.87.0

* update `minecraft-data`

## 2.86.0

* update `minecraft-data`

## 2.85.3

* update `minecraft-data`

## 2.85.2

* update `minecraft-data`

## 2.85.1

* update `minecraft-data`

## 2.85.0

* update `minecraft-data`

## 2.84.0

* update `minecraft-data`
* add legacy to API

## 2.83.1

* update `minecraft-data`

## 2.83.0

* update `minecraft-data`

## 2.82.2

* update `minecraft-data`

## 2.82.1

* update `minecraft-data`

## 2.81.0

* update `minecraft-data`

## 2.80.0

* update `minecraft-data`

## 2.79.0

* update `minecraft-data`

## 2.78.0

* update `minecraft-data`

## 2.77.0

* update `minecraft-data`

## 2.76.0

* update `minecraft-data`

## 2.75.0

* update `minecraft-data`

## 2.74.0

* update `minecraft-data`, add biomesByName

## 2.73.1

* update `minecraft-data`, add 1.16.5 to datapaths

## 2.73.0

* update `minecraft-data`, new enchantment data

## 2.72.0

* update `minecraft-data`, fixed recipes

## 2.71.0

* add map icons

## 2.70.2

* fix release

## 2.70.1

* update `minecraft-data`, fix states in block.json

## 2.70.0

* `minecraft-data` to 2.70.0
* 1.16.4 support

## 2.69.1

* revert load only necessary data (for now)

## 2.69.0

* expose login packet

## 2.68.1

* load only necessary data (thanks @TheDudeFromCI)
* update `minecraft-data` for 1.16.3
* add commands

## 2.67.0

* update `minecraft-data` to 2.67.0 : loottable + enchantments fix

## 2.65.0

* update `minecraft-data` to 2.65.0 : 1.16.2 protocol support
* add particle support

## 2.63.0

* update `minecraft-data` to 2.63.0

## 2.62.1

* food data (thanks @AppDevMichael)

## 2.61.0

* correct block states + default state

## 2.60.0

* full 1.16 support

## 2.59.0

* 1.16.1 protocol support

## 2.58.0

* 1.16 support

## 2.57.0

* fix abilities and recipes packets for 1.16-rc1

## 2.56.0

* 1.16-rc1 protocol support

## 2.55.0

* entity metadata type is a varint since 1.13

## 2.54.0

* update `minecraft-data` to 2.54.0 : better items

## 2.53.0

* update `minecraft-data` to 2.53.0 : better support for 1.14 and 1.15

## 2.52.0

* update `minecraft-data` to 2.52.0 : better block shapes
* expose block shapes

## 2.51.0

* update `minecraft-data` to 2.51.0 : more 1.15.2 data

## 2.50.0

* update `minecraft-data` to 2.50.0 : protocol fix + more 1.14.4 data

## 2.49.0

* update `minecraft-data` to 2.49.0 : 1.14.4 block fix + loading test in nmd

## 2.48.0

* update `minecraft-data` to 2.48.0 : fix for bounding boxes and entity categories

## 2.47.0

* `minecraft-data` 2.47.0 : add biomes, blocks, entities, items and recipes for 1.14.4

## 2.46.0

* `minecraft-data` 2.46.0 : fix entities for 1.13

## 2.45.0

* `minecraft-data` 2.45.0 : last 1.16 snapshot support + fix for glass for 1.13

## 2.44.0

* `minecraft-data` 2.44.0 : small fix to success packet for 20w13b

## 2.43.0

* `minecraft-data` 2.43.0 : 20w13b snapshot support (1.16 major)

## 2.42.0

* `minecraft-data` 2.42.0 : 1.15.2 protocol support

## 2.41.0

* `minecraft-data` 2.41.0 : 1.15 protocol support

## 2.40.0

* `minecraft-data` 2.40.0 : 1.15.1 protocol support

## 2.39.0

* `minecraft-data` 2.39.0 : 1.14.4 support

## 2.38.0

* `minecraft-data` 2.38.0 : 1.14.3 support

## 2.37.5

* `minecraft-data` 2.37.5 : fix intfield -> objectData in spawn_entity in all versions > 1.8

## 2.37.4

* `minecraft-data` 2.37.4 : 1.14 protocol

## 2.37.3

* `minecraft-data` 2.37.3 : fix stonecutting in declare_recipes 1.14.1 : only one ingredient

## 2.37.2

* `minecraft-data` 2.37.2 : u32 -> i32 in 1.14

## 2.37.1

* `minecraft-data` 2.37.1 : add missing version file for 1.14.1 and 1.14

## 2.37.0

* `minecraft-data` 2.37.0 : minecraft 1.14 support

## 2.36.0

* fix teams in 1.13

## 2.35.1

* make standard a devDependencies

## 2.35.0

* update `minecraft-data` to 2.35.0 and add blocksByStateId to API

## 2.34.0

* update `minecraft-data` to 2.34.0 : bring 1.13.2 support

## 2.33.0

* update `minecraft-data` to 2.33.0 : fix version definition for 1.13.2-pre1

## 2.32.0

* update `minecraft-data` to 2.32.0 : bring 1.13.2-pre1 support

## 2.31.0

* update `minecraft-data` to 2.31.0 : fix 1.13.1 datapath

## 2.30.0

* Update `minecraft-data` to 2.30.0 + bring typescript support

## 2.29.0

* Update `minecraft-data` to 2.29.0 : bring full 1.13 support

## 2.28.0

* update `minecraft-data` to 2.28.0 : support mc protocol 1.13.1

## 2.27.0

* update `minecraft-data` to 2.27.0 : bring minecraft 1.13 protocol support

## 2.26.0

* update `minecraft-data` to 2.25.0 : fix packet_title

## 2.25.0

* fix brigadier:string parser properties

## 2.24.0

* update `minecraft-data` to 2.23.0, some fixes for 17w50a protocol

## 2.23.0

* find the most recent minor version from major version when needed

## 2.22.0

* bump `minecraft-data` to 2.22.0, pocket edition 17w50a support

## 2.21.0

* bump `minecraft-data` to 2.21.0, pocket edition 1.12.2 support

## 2.20.0

* bump `minecraft-data` to 2.20.0, pocket edition 1.12.1 support

## 2.19.0

* add language to api, update `minecraft-data` to 2.19.0

## 2.18.0

* update `minecraft-data` to 2.18.0, better supports pocket edition 1.12

## 2.17.0

* update `minecraft-data` to 2.17.0, supports pocket edition 1.12

## 2.16.0

* update `minecraft-data` to 2.16.0 ,supports 1.12-pre4

## 2.15.0

* update `minecraft-data` to 2.15.0, supports pc 17w18b

## 2.14.0

* update `minecraft-data` to 2.14.0
* supports mc 17w15a

## 2.13.4

* update `minecraft-data`

## 2.13.3

* update `minecraft-data`

## 2.13.2

* add `.npmignore` (for data.js)

## 2.13.1

* update `minecraft-data`

## 2.13.0

* add protocol comments
  q

## 2.12.1

* remove protocol schema (moved to protodef)

## 2.12.0

* use minecraft data 2.10.0, complete 1.11 data

## 2.11.0

* update `minecraft-data` to 2.9.0, bring pocket edition 1.11.2 support

## 2.10.0

* update `minecraft-data` to 2.8.0, add pocket edition 1.0

## 2.9.0

* update `minecraft-data` to 2.7.0 : add 1.11 support (only the protocol)

## 2.8.0

* classic blocks
* update `minecraft-data` to 2.6.0

## 2.7.0

* update `minecraft-data` to 2.5.0, add enchantments to api, add 16w35a (first 1.11 snapshot)

## 2.6.0

* update `minecraft-data` to 2.4.0, add pocket edition 1.10.1 and 1.10.2 support, add pocket edition 0.15 support

## 2.5.1

* update `minecraft-data`, fix 1.10 version

## 2.5.0

* update `minecraft-data` to 2.3.0, add 1.10 support

## 2.4.0

* update `minecraft-data` to 2.2.0, add pe protocol

## 2.3.0

* update minecraft-data to 2.1.0, add pc 1.10-pre1 support

## 2.2.0

* add type (pe or pc) to API

## 2.1.0

* use blocks of pc 1.8 for pe 0.14

## 2.0.0

* update to minecraft-data to 2.0.0, adapt to handle both pc and pe
* the api of versionsByMinecraftVersion changed : now indexed by pc and pe : BREAKING CHANGE

## 1.6.0

* update minecraft-data to 1.1.0, add 16w20a support (1.1.0)

## 1.5.0

* update minecraft-data to 1.0.0, add 1.9.4 support

## 1.4.1

* actually add 1.9.2 support

## 1.4.0

* update `minecraft-data` : 1.9.2 support

## 1.3.1

* fix release (submodule)

## 1.3.0

* export schemas

## 1.2.6

* update `minecraft-data` : add 1.9.1-pre2

## 1.2.5

* update `minecraft-data` : fix in 1.9 protocol

## 1.2.4

* i8 not byte

## 1.2.3

* a few fixes in 1.9 protocol

## 1.2.2

* update `minecraft-data` : 1.9 release

## 1.2.1

* update minecraft-data : update blocks, items and recipes in 1.9, and fix a small error in 1.9 protocol

## 1.2.0

* update 1.9 protocol to 1.9-pre4

## 1.1.0

* update `minecraft-data` : protocol schema change, add classic

## 1.0.3

* update `minecraft-data` : 1.9 protocol fix

## 1.0.2

* forgot a short in 1.7 protocol

## 1.0.1

* update `minecraft-data` : fix short in 1.7 protocol

## 1.0.0

* update minecraft data, protocol.json has new numerical type names

## 0.20.4

* update minecraft-data : 1.7 plugin channel data buffers are length-prefixed

## 0.20.3

* update `minecraft-data` : fix in 1.7 protocol

## 0.20.2

* update minecraft-data : a fix in the key of 1.7 protocol

## 0.20.1

* fix release

## 0.20.0

* 1.9 updated to 16w05b
* 15w40b support kept
* 1.7 support added
* add transparency and light data in blocks

## 0.19.1

* fix release (missing submodule)

## 0.19.0

* move protocol data to top level
* expose preNettyVersionsByProtocolVersion and postNettyVersionsByProtocolVersion
* use it for a better resolution of the "major" version

## 0.18.0

* add protocol versions

## 0.17.0

* some windows fixing
* update entities : now mobs and objects

## 0.16.3

* use require-self to use require('minecraft-data') in example and make tonicdev actually work

## 0.16.2

* Usage -> Example, for tonicdev integration

## 0.16.1

* update `minecraft-data` : improvement in the protocol for slot, optionalNbt and nbt

## 0.16.0

* add effects

## 0.15.0

* make node-minecraft-data browserify compatible

## 0.14.0

* move to new minecraft-data organization: master branch containing all the versions instead of one branch per version

## 0.13.0

* minecraft-data 1.9 to 15w40b + 76

## 0.12.0

* update minecraft-data : update 1.8 version to 1.8.8, update 1.9 version to 15w39c

## 0.11.0

* update minecraft-data : in protocol.json the context is now implemented with ../ instead of this.

## 0.10.0

* add version to the API

## 0.9.0

* update minecraft-data : windows and protocol updates

## 0.8.1

* actually update protocol.json

## 0.8.0

* update protocol.json
* add windows

## 0.7.0

* update protocol.json : condition becomes switch

## 0.6.0

* update protocol.json in 1.8 and 1.9 : the schema is now better for types (see minecraft-data for more details)

## 0.5.2

* update README and example : the package is now called minecraft-data in npm

## 0.5.1

* fix bug with fs.existsSync
* fix npm badge in readme

## 0.5.0

* bumping a few versions because package now published as minecraft-data
* add multi-version support
* add protocol.json access

## 0.2.0

* add un-indexed version of blocks, items, biomes, instruments and entities

## 0.1.2

* fix bug in indexes

## 0.1.1

* fix bug in find functions

## 0.1.0

* provide : id-indexed data, name-indexed data and two functions to find items or blocks
