{"name": "golden-fleece", "description": "Parse and manipulate JSON5 strings", "version": "1.0.9", "main": "golden-fleece.umd.js", "module": "golden-fleece.es.js", "types": "types/index.d.ts", "files": ["golden-fleece.es.js", "golden-fleece.umd.js", "src", "types"], "devDependencies": {"@types/glob": "^5.0.33", "@types/mocha": "^2.2.42", "@types/node": "^8.0.26", "benchmark": "^2.1.4", "chalk": "^2.3.0", "degit": "^2.0.1", "glob": "^7.1.2", "json5": "^0.5.1", "locate-character": "^2.0.3", "mocha": "^3.5.0", "right-pad": "^1.0.1", "rollup": "^0.49.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-typescript": "^0.8.1", "rollup-plugin-virtual": "^1.0.1", "sander": "^0.6.0", "ts-node": "^3.3.0", "typescript": "^2.5.2"}, "scripts": {"build-declarations": "tsc -d && node scripts/move-declaration-files.js", "build": "npm run build-declarations && rollup -c", "fetch_tests": "degit json5/json5-tests test/json5-tests", "test": "mocha --opts mocha.opts", "prepublishOnly": "npm test && npm run build"}, "repository": {"type": "git", "url": "https://github.com/<PERSON>-<PERSON>/golden-fleece.git"}}