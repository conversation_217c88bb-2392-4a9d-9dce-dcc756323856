{"name": "prismarine-entity", "version": "2.4.0", "description": "Represent a minecraft entity", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "npm run lint", "lint": "standard"}, "repository": {"type": "git", "url": "https://github.com/PrismarineJS/prismarine-entity.git"}, "dependencies": {"prismarine-chat": "^1.4.1", "prismarine-item": "^1.11.2", "prismarine-registry": "^1.4.0", "vec3": "^0.1.4"}, "keywords": ["minecraft", "entity", "prismarine"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/PrismarineJS/prismarine-entity/issues"}, "devDependencies": {"@types/node": "^20.2.1", "prismarine-entity": "file:.", "standard": "^17.0.0"}}