{"version": 3, "file": "RegionDiscovery.mjs", "sources": ["../../src/authority/RegionDiscovery.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;;AAAA;;;AAGG;MAiBU,eAAe,CAAA;AAgBxB,IAAA,WAAA,CACI,gBAAgC,EAChC,MAAc,EACd,iBAAsC,EACtC,aAAsB,EAAA;AAEtB,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACzC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC3C,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;KACtC;AAED;;;;AAIG;AACI,IAAA,MAAM,YAAY,CACrB,iBAAqC,EACrC,uBAAgD,EAAA;AAEhD,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,2BAA2B,EAC7C,IAAI,CAAC,aAAa,CACrB,CAAC;;QAGF,IAAI,sBAAsB,GAAG,iBAAiB,CAAC;;QAG/C,IAAI,CAAC,sBAAsB,EAAE;AACzB,YAAA,MAAM,OAAO,GAAG,eAAe,CAAC,YAAY,CAAC;YAE7C,IAAI;AACA,gBAAA,MAAM,wBAAwB,GAAG,MAAM,WAAW,CAC9C,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EACjC,iBAAiB,CAAC,gCAAgC,EAClD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;gBACnC,IACI,wBAAwB,CAAC,MAAM;oBAC/B,aAAa,CAAC,WAAW,EAC3B;AACE,oBAAA,sBAAsB,GAAG,wBAAwB,CAAC,IAAI,CAAC;AACvD,oBAAA,uBAAuB,CAAC,aAAa;wBACjC,sBAAsB,CAAC,IAAI,CAAC;AACnC,iBAAA;;gBAGD,IACI,wBAAwB,CAAC,MAAM;oBAC/B,aAAa,CAAC,cAAc,EAC9B;AACE,oBAAA,MAAM,kBAAkB,GAAG,MAAM,WAAW,CACxC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EACjC,iBAAiB,CAAC,gCAAgC,EAClD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC,OAAO,CAAC,CAAC;oBACX,IAAI,CAAC,kBAAkB,EAAE;AACrB,wBAAA,uBAAuB,CAAC,aAAa;4BACjC,sBAAsB,CAAC,qBAAqB,CAAC;AACjD,wBAAA,OAAO,IAAI,CAAC;AACf,qBAAA;AAED,oBAAA,MAAM,0BAA0B,GAAG,MAAM,WAAW,CAChD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EACjC,iBAAiB,CAAC,gCAAgC,EAClD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;oBAC/B,IACI,0BAA0B,CAAC,MAAM;wBACjC,aAAa,CAAC,WAAW,EAC3B;wBACE,sBAAsB;4BAClB,0BAA0B,CAAC,IAAI,CAAC;AACpC,wBAAA,uBAAuB,CAAC,aAAa;4BACjC,sBAAsB,CAAC,IAAI,CAAC;AACnC,qBAAA;AACJ,iBAAA;AACJ,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACR,gBAAA,uBAAuB,CAAC,aAAa;oBACjC,sBAAsB,CAAC,qBAAqB,CAAC;AACjD,gBAAA,OAAO,IAAI,CAAC;AACf,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,uBAAuB,CAAC,aAAa;gBACjC,sBAAsB,CAAC,oBAAoB,CAAC;AACnD,SAAA;;QAGD,IAAI,CAAC,sBAAsB,EAAE;AACzB,YAAA,uBAAuB,CAAC,aAAa;gBACjC,sBAAsB,CAAC,qBAAqB,CAAC;AACpD,SAAA;QAED,OAAO,sBAAsB,IAAI,IAAI,CAAC;KACzC;AAED;;;;;AAKG;AACK,IAAA,MAAM,iBAAiB,CAC3B,OAAe,EACf,OAAoB,EAAA;AAEpB,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,gCAAgC,EAClD,IAAI,CAAC,aAAa,CACrB,CAAC;QACF,OAAO,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAC5C,CAAA,EAAG,SAAS,CAAC,aAAa,gBAAgB,OAAO,CAAA,YAAA,CAAc,EAC/D,OAAO,EACP,SAAS,CAAC,YAAY,CACzB,CAAC;KACL;AAED;;;;AAIG;IACK,MAAM,iBAAiB,CAC3B,OAAoB,EAAA;AAEpB,QAAA,IAAI,CAAC,iBAAiB,EAAE,mBAAmB,CACvC,iBAAiB,CAAC,gCAAgC,EAClD,IAAI,CAAC,aAAa,CACrB,CAAC;QACF,IAAI;AACA,YAAA,MAAM,QAAQ,GACV,MAAM,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAC3C,CAAA,EAAG,SAAS,CAAC,aAAa,cAAc,EACxC,OAAO,CACV,CAAC;;AAGN,YAAA,IACI,QAAQ,CAAC,MAAM,KAAK,aAAa,CAAC,cAAc;AAChD,gBAAA,QAAQ,CAAC,IAAI;AACb,gBAAA,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBAChC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,MAAM,GAAG,CAAC,EAC7C;gBACE,OAAO,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,aAAA;AAED,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;KACJ;;AArKD;AACiB,eAAA,CAAA,YAAY,GAAgB;AACzC,IAAA,OAAO,EAAE;AACL,QAAA,QAAQ,EAAE,MAAM;AACnB,KAAA;CACJ;;;;"}