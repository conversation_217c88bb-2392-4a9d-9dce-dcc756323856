name: CI

on:
  push:
    branches:
      - master
  pull_request:

jobs:
  Lint:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js 18.x
      uses: actions/setup-node@v1.4.4
      with:
        node-version: 18.x
    - run: npm i && npm run lint
  
  PrepareSupportedVersions:
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}

    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js 18.x
      uses: actions/setup-node@v1.4.4
      with:
        node-version: 18.x
    - id: set-matrix
      run: |
        node -e "
          const testedVersions = require('./lib/version').testedVersions;
          console.log('matrix='+JSON.stringify({'include': testedVersions.map(mcVersion => ({mcVersion}))}))
        " >> $GITHUB_OUTPUT

  MinecraftServer:
    needs: PrepareSupportedVersions
    runs-on: ubuntu-latest
    strategy:
      matrix: ${{from<PERSON><PERSON>(needs.PrepareSupportedVersions.outputs.matrix)}}
      fail-fast: false

    steps:
      - uses: actions/checkout@v2
      - name: Use Node.js 18.x
        uses: actions/setup-node@v1.4.4
        with:
          node-version: 18.x
      - name: Setup Java JDK
        uses: actions/setup-java@v1.4.3
        with:
          java-version: 21
          java-package: jre
      - name: Install Dependencies
        run: npm install
      - name: Start Tests
        run: npm run mocha_test -- -g ${{ matrix.mcVersion }}v
