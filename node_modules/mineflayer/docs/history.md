## 4.23.0
* [1.21 (#3480)](https://github.com/PrismarineJS/mineflayer/commit/4aa10fb45431940504c7809f078f1f410e7fa7a3) (thanks @Madlykeanu)
* [Adding mindcraft to mineflayer readme](https://github.com/PrismarineJS/mineflayer/commit/dd00db42ba20682418d8fbd5629e1033dfb0ff20) (thanks @rom1504)

## 4.22.0
* [Remove debug logging (#3478)](https://github.com/PrismarineJS/mineflayer/commit/eb29d350ede0590fce17e04bf21071807a87e3a1) (thanks @extremeheat)

## 4.21.0
* [1.20.6 (#3412)](https://github.com/PrismarineJS/mineflayer/commit/44fad41c18be5024564e24e1cdee624d35d4d387) (thanks @extremeheat)
* [Update README.md (#3420)](https://github.com/PrismarineJS/mineflayer/commit/1c2a5c0fa78f74a63fabd7edde85c4a74db32dee) (thanks @SilkePilon)
* [types: add pitchSpeed as an option in typings (#3446)](https://github.com/PrismarineJS/mineflayer/commit/05b48ad0dad4cf64a1c11660bac256d7b4015841) (thanks @GenerelSchwerz)
* [Fixed a bug with not closing the window when changing the subserver (#3424)](https://github.com/PrismarineJS/mineflayer/commit/d00c386cfe51cefc361c0ff4d30b100aee9f114a) (thanks @DenisKvak1)
* [Bump @types/node from 20.14.14 to 22.1.0 (#3431)](https://github.com/PrismarineJS/mineflayer/commit/1d461616b514969fdece38e49bfbec747ab8d76a) (thanks @dependabot[bot])
* [Fix updateSlot event type (#3425)](https://github.com/PrismarineJS/mineflayer/commit/5d39db26a6ab17baac38b68af8ccd3eeb4af3def) (thanks @DenisKvak1)
* [Player hitbox fixes (#3382)](https://github.com/PrismarineJS/mineflayer/commit/78b4eccb4572a821b11c3124b7a593f3b91f1180) (thanks @AreaDenial)
* [Typo fixes (#3418)](https://github.com/PrismarineJS/mineflayer/commit/ef042a242ca9f5fc5820fe4dc2e1d997ef1db202) (thanks @kgurchiek)
* [Added support for 1.18+ edit book packet #3204 (#3373)](https://github.com/PrismarineJS/mineflayer/commit/eb9982aa04973b0086aac68a2847005d77f01a3d) (thanks @unlimitedcoder2)
* [Fix typos (#3381)](https://github.com/PrismarineJS/mineflayer/commit/d581ea7cee4d5b7df9606f671656bb0be0fdbf91) (thanks @data-miner00)
* [Fix typescript types syntax for setCommandBlock (#3366)](https://github.com/PrismarineJS/mineflayer/commit/315cdfc4b1fc2760e4a8a36feb718626a66d5056) (thanks @undefined)
* [Remove invalid sign check (#3328)](https://github.com/PrismarineJS/mineflayer/commit/ec76468c8ac4c6232bad3c2b66d4160f95f58396) (thanks @zardoy)
* [refactor: simplifying the code of blockAtCursor (#3337)](https://github.com/PrismarineJS/mineflayer/commit/dc70f932ac9aaab6e6cdb15057b409b15c3232dd) (thanks @SnowRunescape)
* [Updated setCommandBlock's 3rd argument (#3356)](https://github.com/PrismarineJS/mineflayer/commit/04ad6db404f0da779004b3ddd0e049bf2c6be0a3) (thanks @FlooferLand)
* [Added the serverBrand property to index.d.ts (#3355)](https://github.com/PrismarineJS/mineflayer/commit/0bb2707d2f6d0d64a467d4e0d6ddc52adf526127) (thanks @Khaogamermain01)

## 4.20.1
* [Add bossBarCreated event in index.d.ts (#3340)](https://github.com/PrismarineJS/mineflayer/commit/8299288526cd7ff24bcd87511814221f8ad62507) (thanks @gguio)
* [Update scoreboard.js (#3318)](https://github.com/PrismarineJS/mineflayer/commit/195b3cbd70a110080af9b77a4659991c5d9e484a) (thanks @vicdum)
* [Fix hardcoded diggingface for cancel digging (#3322)](https://github.com/PrismarineJS/mineflayer/commit/ab78bf855929a476386b5eb6efcf3b271d02455e) (thanks @Vinciepincie)
* [Fix 1.20.4 server resource pack error (#3320)](https://github.com/PrismarineJS/mineflayer/commit/7c01eeb970647ed2933c10cb2b94fd7b44c777f5) (thanks @TerminalCalamitas)
* [Fix scoreboard delete handler not first checking if scoreboard exists (#3324)](https://github.com/PrismarineJS/mineflayer/commit/d9e9e15aeb646d81da2a3e2987566de47e3bae04) (thanks @Ynfuien)

## 4.20.0
* [Update api.md - addChatPattern[Set] link to example of usage (#3304)](https://github.com/PrismarineJS/mineflayer/commit/bb3e5877b7b3b8ab063b39a5b47d103b819da1c2) (thanks @boly38)
* [Fixed deleted scoreboards not being removed from ScoreBoard.positions (#3306)](https://github.com/PrismarineJS/mineflayer/commit/643023df91bf428d3e7d30e8f2eab97e3238b0b2) (thanks @Ynfuien)
* [Support 1.20.4 (#3310)](https://github.com/PrismarineJS/mineflayer/commit/aa99daa7d63ee9549f2dda5a79c140e30e19a89b) (thanks @rom1504)

## 4.19.0
* [Clarify readme createBot username handling (#3300)](https://github.com/PrismarineJS/mineflayer/commit/7a2680bc07f53d16626679537ea1f07aae180549) (thanks @extremeheat)
* [fix world typing (#3302)](https://github.com/PrismarineJS/mineflayer/commit/5dc36d6cdeaf4be72ea023827d45b9d78e575f66) (thanks @GenerelSchwerz)
* [modified the README.md files for other languages and fixed a linking issue at those files. (#3297)](https://github.com/PrismarineJS/mineflayer/commit/cc98f1307e3ab48477d2a9ff29da4447f42b30bc) (thanks @Axwaizee)
* [formatted docs/README.md for easy copy (#3295)](https://github.com/PrismarineJS/mineflayer/commit/468c8aa9d382a7872ec991c3b834b98cbe495e8d) (thanks @Axwaizee)
* [Added missing bot.teams definition (#3294)](https://github.com/PrismarineJS/mineflayer/commit/fb8ee7aa619bd38cc97d5dbd870bb11455d51d39) (thanks @Ynfuien)
* [Timeout for bot.tabComplete() (#3293)](https://github.com/PrismarineJS/mineflayer/commit/4231a169d579d08ac7b9ec0694e18b1f6ac837ea) (thanks @Ynfuien)
* [:label: Update types to be updated with what's in JavaScript (#3287)](https://github.com/PrismarineJS/mineflayer/commit/210785e86c031f7e3323d7d2ffe5152d2d4a5eb6) (thanks @fantomitechno)
* [Fixed some typo (#3290)](https://github.com/PrismarineJS/mineflayer/commit/ba53a953d03a6edb34aa5bf38bccde58e65d816d) (thanks @SilianZ)
* [Updated digging code to account for raycasted tall grass checks (#3285)](https://github.com/PrismarineJS/mineflayer/commit/bd0fb5c4d3b665f264009f62f9288828f3018cea) (thanks @GenerelSchwerz)

## 4.18.0
* [Minecraft 1.20.2 support (#3262)](https://github.com/PrismarineJS/mineflayer/commit/2ff9919760d714be57dcb678f8ab5ecff69f5fee) (thanks @rom1504)
* [Update recommended Node.js version (#3279)](https://github.com/PrismarineJS/mineflayer/commit/5c71edf48bb2f2dfa16cddb9af5baa0c4d55cf0d) (thanks @Nyaasu66)
* [feat: bot.respawn, fix respawn with flying squid (#3206)](https://github.com/PrismarineJS/mineflayer/commit/3a6ce543b4ba8a3d0f55777670d142968af14571) (thanks @zardoy)
* [Add `maxCatchupTicks`, improve `supportFeature` typing (#3277)](https://github.com/PrismarineJS/mineflayer/commit/91108d392c4c5800204dd4674ce9247495ac98e0) (thanks @zardoy)
* [Fixed typo of "fromt" to "from" (#3273)](https://github.com/PrismarineJS/mineflayer/commit/216cab742db1cd053d9fa23bd7293b770239085b) (thanks @BorretSquared)

## 4.17.0
* [Exclude browser from node version check (#3268)](https://github.com/PrismarineJS/mineflayer/commit/c466d327227796865c53bfd24e66668911be4af5) (thanks @rom1504)
* [Fix for a possible error in lib/plugins/entities.js file (#3254)](https://github.com/PrismarineJS/mineflayer/commit/15cfeb4fa59edfcddf7a0b70a966294b24d798ed) (thanks @Mykola1453)
* [Make explicit supported versions in readme (#3264)](https://github.com/PrismarineJS/mineflayer/commit/931a4187965aef686c6188b944de84455c65b827) (thanks @rom1504)

## 4.16.0
* [Fix version check (#3259)](https://github.com/PrismarineJS/mineflayer/commit/88d361f9209cdc2bc4620b32118fb2245f6dcdf9) (thanks @extremeheat)

## 4.15.0
* [Fix several bugs in villager trading (#3230)](https://github.com/PrismarineJS/mineflayer/commit/1caa2c216b3a10a2bccd7b78a22f3809cb359fe3) (thanks @evan-goode)
* [Fix `bot.heldItem` and `bot.entity.equipment` (#3225)](https://github.com/PrismarineJS/mineflayer/commit/9865ab72f7438fff9d74f2fe19a138da870c41aa) (thanks @szdytom)
* [Improve CI per version setup (#3256)](https://github.com/PrismarineJS/mineflayer/commit/48c3ca71ea5822c4304ec74951970dbefd5026eb) (thanks @rom1504)
* [added Readme Português-BR (#3219)](https://github.com/PrismarineJS/mineflayer/commit/70a652ee5b6c0151826e17b38efd458357fc93ac) (thanks @LukeTheNeko)
* [Fixes `fireworkRocketDuration` calculation (#3222)](https://github.com/PrismarineJS/mineflayer/commit/3d8a1aaed036c2df74c2e607245cefab12409761) (thanks @szdytom)
* [Update Minecraft Wiki links to new domain after fork (#3203)](https://github.com/PrismarineJS/mineflayer/commit/08208e2f110af2c6de41fac9a389597aac916412) (thanks @misode)
* [typings: add entityAtCursor to bot (#3200)](https://github.com/PrismarineJS/mineflayer/commit/7016c19f9c736671d8af1401ac25d5175401891f) (thanks @SnowRunescape)
* [Handle unknown scoreboard objectives (#3191)](https://github.com/PrismarineJS/mineflayer/commit/2e02cee82d6d154b3b7bfb30d213479e7c4fbc59) (thanks @frej4189)
* [Sidebar fixes (#3188)](https://github.com/PrismarineJS/mineflayer/commit/e571e54edf46ac6da000e1c84f36bec3b75ccf24) (thanks @FurnyGo)
* [Fix ci : fix lint in MD (#3192)](https://github.com/PrismarineJS/mineflayer/commit/7987e3c546038de0eaa6d573596f51d3edc383bb) (thanks @frej4189)
* [Updating RU docs (#3178)](https://github.com/PrismarineJS/mineflayer/commit/7474564da5432295c682e8a4f827f58e3c3f3be8) (thanks @FurnyGo)

## 4.14.0
* [Update Jupyter notebook to install node 18, update the server in example (#3176)](https://github.com/PrismarineJS/mineflayer/commit/e8a967d4e832f72d665781492c037d26169ae5a0) (thanks @extremeheat)
* [Update index.d.ts (#3175)](https://github.com/PrismarineJS/mineflayer/commit/d4db3991c135344180937b69621c0ee31daa39f0) (thanks @StayWithMeSenpai)
* [Add elytra flying support and rocket support (#3163)](https://github.com/PrismarineJS/mineflayer/commit/010460e9dd752a56195d8a48f35a62e704dcf99f) (thanks @lkwilson)

## 4.13.0
* [Switch to entity.displayName (#3168)](https://github.com/PrismarineJS/mineflayer/commit/2409ad458b952173de669a7d9cfaeb770effe3ae) (thanks @lkwilson)
* [Update readme auth doc (#3169)](https://github.com/PrismarineJS/mineflayer/commit/f5d4a288a768ca6717fa4d22c72fb0267428c684) (thanks @extremeheat)
* [Add OpenDeliveryBot to "Projects Using Mineflayer" (#3162)](https://github.com/PrismarineJS/mineflayer/commit/ab3c0cf25d0cc28ccba89640b2ceff6ab6b4dace) (thanks @SilkePilon)
* [Use node 18 on CI (#3157)](https://github.com/PrismarineJS/mineflayer/commit/d3df34dcaa804a71bf0d8cc50a419990d4a2dce3) (thanks @extremeheat)
* [Fix ambigious function naming (#3161)](https://github.com/PrismarineJS/mineflayer/commit/9ecdf201794bfa350486839a01e318dfd94b3bfb) (thanks @frej4189)

## 4.12.0
* [Mineflayer physics refactor (#2492)](https://github.com/PrismarineJS/mineflayer/commit/d0eb3a1afe6cda7b04ae2f88052cd868ba0c0c4f) (thanks @U5B)

## 4.11.0
* [Import changedSlots computation from prismarine-windows (#3134)](https://github.com/PrismarineJS/mineflayer/commit/e5b5eeecf1133c1c80c0ef48d6e72fed77d84834) (thanks @kaduvert)
* [Make the place block success check ignore block updates received with no block type changes (#3090)](https://github.com/PrismarineJS/mineflayer/commit/bbdd93afe2e31d1f1e899176e7edf8e73af5d5d3) (thanks @PondWader)
* [Use node-minecraft-protocol for chat (#3110)](https://github.com/PrismarineJS/mineflayer/commit/385fba65ed6ebe632c870c7cf234666cacf5a766) (thanks @lkwilson)
* [Extended useChests.js tests (#3132)](https://github.com/PrismarineJS/mineflayer/commit/****************************************) (thanks @kaduvert)
* [Allow more click modes (#3133)](https://github.com/PrismarineJS/mineflayer/commit/a315653bb94274113c9d6078d4c2ab840af0f62a) (thanks @kaduvert)
* [Add nether test (#2932)](https://github.com/PrismarineJS/mineflayer/commit/6b1d6ea15c72edc5b761b78765a53d2ab7d0d274) (thanks @frej4189)
* [Explicitly depend on pitem 1.14.0 with fix](https://github.com/PrismarineJS/mineflayer/commit/acc6ec9b5e61d566facb76e9c3ff512cc9a5137f) (thanks @rom1504)
* [Make sure we pass a string to a storagebuilder (#2645)](https://github.com/PrismarineJS/mineflayer/commit/fc95843dac69bc1101dd5ec898a2aaf4dcfbf520) (thanks @u9g)
* [extra types for enchantments (#3123)](https://github.com/PrismarineJS/mineflayer/commit/b336d2259d1ce0935bf8e10a4edb3c0a9030fb10) (thanks @zisis912)
* [Add 1.20 to supported versions in readme (#3111)](https://github.com/PrismarineJS/mineflayer/commit/d764706f53dbe7ba16cf49645d66d192a309cc5c) (thanks @litfa)
* [Handle hand swap entity status (#3097)](https://github.com/PrismarineJS/mineflayer/commit/a80d69a8f1a637ab1a0720ec776fc4f05c38afed) (thanks @PondWader)
* [Add command gh workflow allowing to use release command in comments (#3116)](https://github.com/PrismarineJS/mineflayer/commit/5a55744ee0dc670f984229ec2629239bdc3e5705) (thanks @rom1504)

## 4.10.1

* Fix attempting to access unloaded chunks (@frej4189)

## 4.10.0

* Fix handling for entities with unknown metadata (@extremeheat)
* support 1.20 (@PondWader)

## 4.9.0

* Fix bot not updating world height on respawn packet (@frej4189)
* Persist properties received in player_info packet (@Paulomart)
* Fix reference error with block updates (@IceTank)
* Add spectator to gameModes array (@williamd5)
* Standardize dimensions for all versions (@sefirosweb)
* Emit inject_allowed after a timeout of 0 (@IceTank)
* Add window transaction timeout (@firejoust)
* Made bot auto respawning togglable (@Averagess)
* support 1.19.4 (@extremeheat)

## 4.8.1

* Fix client crashing when player_remove contains unknown player (@frej4189)
* Improve look and fix bug slow craft (@sefirosweb)
* Fix player entity being unset when player is updated (@frej4189)
* Fix type (@sefirosweb)
* Improve crafting stacks (@sefirosweb)
* add example for using the node:readline module (@Jovan-04)

## 4.8.0

* Update chat parsing (@frej4189)
* Fix message event not including chat position (@frej4189)
* 1.19.3  (@frej4189)

## 4.7.0

* 1.19.2 support (@frej4189)

## 4.6.0

* Fix unhandled promise rejection in onceWithCleanup (@IceTank) [#2833](https://github.com/PrismarineJS/mineflayer/pull/2833)
* Extend every window that is opened with mineflayer specific window functions (@IceTank) [#2768][https://github.com/PrismarineJS/mineflayer/pull/2768]
* Fix example command line usage messages (@maximmasiutin) [#2853](https://github.com/PrismarineJS/mineflayer/pull/2853)
* Update README_ES.md (@PanIntegralus) [#2803](https://github.com/PrismarineJS/mineflayer/pull/2803)
* Fix block face position target when digging (@WhoTho) [#2801](https://github.com/PrismarineJS/mineflayer/pull/2801)
* Add a native mineflayer event for particles (@NyxaYu) [#2813](https://github.com/PrismarineJS/mineflayer/pull/2813)
* Fix viewDistance type (@Nciklol) [#2824](Fix viewDistance type (#2824) ) 
* Add French FAQ (@AugustinMauroy) [#2817](https://github.com/PrismarineJS/mineflayer/pull/2817)

## 4.5.1

* Fixed syntax error in TypeScript definitions (@JungleDome) [commit](https://github.com/PrismarineJS/mineflayer/commit/2c6a4036d84bedb5f349ea5a82d743e344c34224)

## 4.5.0

* 1.19 support (@extremeheat @rom1504 @FCKJohni @Shorent)
* refactoring examples to use bot.registry (@Epirito)
* Added barrel and coloured shulker boxes to openable windows (@lazydancer)
* types: Fix return type for openBlock and openEntity (@sefirosweb)
* Update activateEntity and activateEntityAt types (@amoraschi)

## 4.4.0

* Fix chatterbox example not getting dropped item (@u9g) [commit](https://github.com/PrismarineJS/mineflayer/commit/f860eac01a0418f4a3de749482d8cab681acc48a)
* Fix 404d link to license (@BalaM314) [#2601](https://github.com/PrismarineJS/mineflayer/pull/2601)
* Add bot.clickWindow mode disclaimer (@IceTank) [#2595](https://github.com/PrismarineJS/mineflayer/pull/2595)
* Add spectator to GameMode types (@IceTank) [#2627](https://github.com/PrismarineJS/mineflayer/pull/2627)
* Update types for isABed (@amoraschi) [#2628](https://github.com/PrismarineJS/mineflayer/pull/2628)
* Replace openChest with openContainer in docs and examples (@slightly-imperfect) [#2656](https://github.com/PrismarineJS/mineflayer/pull/2656)
* Add ender chests as a chest type (@RoseChilds) [#2642](https://github.com/PrismarineJS/mineflayer/pull/2642)
* Added method to wait until sleep function is in reality sleeping (@sefirosweb) [#2617](https://github.com/PrismarineJS/mineflayer/pull/2617)
* Added type on move event (@sefirosweb) [#2712](https://github.com/PrismarineJS/mineflayer/pull/2712)
* Added thunderState type (@sefirosweb) [#2711](https://github.com/PrismarineJS/mineflayer/pull/2711)
* Fix type error on chest open (@IceTank) [#2684](https://github.com/PrismarineJS/mineflayer/pull/2684)
* Add support for repeating and chain command blocks. (@mirkokral) [#2669](https://github.com/PrismarineJS/mineflayer/pull/2669)
* Add player object to blockBreakProgressEnd & observed. (@JackCrispy) [#2647](https://github.com/PrismarineJS/mineflayer/pull/2647)
* Add entity to blockBreakProgress (@JackCrispy ) [#2648](https://github.com/PrismarineJS/mineflayer/pull/2648)
* Add direction support to activateBlock, openBlock (@IceTank) [#2039](https://github.com/PrismarineJS/mineflayer/pull/2039)
* Add entityAtCursor function (@O-of) [#2077](https://github.com/PrismarineJS/mineflayer/pull/2077)
* Fix regex dos (@IceTank) [#2350](https://github.com/PrismarineJS/mineflayer/pull/2350)

## 4.3.0

* Cache 'positionUpdateSentEveryTick' feature lookup (@IceTank)
* Remove old teams from bot.teamMap (@U9G)
* mcdata 3.0.0

## 4.2.0

* add missing extraInfos argument to Bot.blockAt function (@dumbasPL)
* Emit window close event AFTER updating the inventory (@imharvol)
* Move supportFeature to mcdata (@U9G)
* Update lib/ and test/ to use prismarine-registry (@extremeheat)
* only open chests with openContainer (@U9G)
* Add bot.creative.clearSlot and bot.creative.clearInventory (@U9G)
* remove transaction warning

## 4.1.0

* 1.18.2 support
* Add nbt option to withdraw and deposit

## 4.0.0

* useEntity maintains sneak state
* BREAKING: remove all callbacks in favor of promises

## 3.18.0

* remove callbacks from types and docs

## 3.17.0

* callback are now depreciated with mineflayer. Any use of them will print a warning

## 3.16.0

* Use prismarine-chunk for block entities

## 3.15.0

* Supports 1.18

## 3.14.1

* Fix arm_animation and use_entity (@amorashi)

## 3.14.0

* Make prismarine-entity versioned (@u9g)
* fix(typings): Added OpenContainer (@SaubereSache)

## 3.13.1

* Fix bug with force lastSentPitch in bot.look (@KadaverBrutalo10)
* Fix typo harming type safety (@Eagle-Anne)

## 3.13.0

* compute scoreboard displayName dynamically (@U9G)
* SkinsRestorer fix (@U5B)
* Fix bot not swinging arm on block place (@IceTank)

## 3.12.0

* Bypass anticheats that detect sensitivity (@mat-1)
* Fix removing many players at once from tab list (@mat-1)
* Added blockAtEntityCursor function (@DatArnoGuy)
* add option to disable default chat patterns (@U5B)
* Fixed wrong arm swinging (@IceTank)
* Add pitch speed to look (@IceTank)
* Console spam fix (@IceTank)
* Update openVillager function to return a promise (@amoraschi)
* Send arm_animation before use_entity (@aesthetic0001)
* Add reason for the end of a mineflayer bot (@U5B)
* added rejection of invalid transaction packets (anticheat fix) (@U5B)

## 3.11.2
* Remove unnecessary and buggy inventory check in place block (@Karang)
* Make all events allow async cb typings (@u9g)

## 3.11.1
* Get rid of nowaiting (@nickelpro)
* update readme (@inthmafr)
* Fix Typings (@link-discord, @IceTank, @u9g)

## 3.11.0
* better chat, equipping and consuming errors (@u9g)
* add bot.usingHeldItem (@mat1)
* 1.17.1 support (mainly work from @nickelpro and @u9g, but also @Archengius @extremeheat @imharvol @willocn and @rom1504)

## 3.10.0
* Add Chinese translations (@Nyaasu66)
* Fix bot.equip failing with off-hand (@IceTank)
* window.withdraw no longer will drop items if it takes too many items (@Zn10plays)
* No longer have to await ready for enchanting (@u9g)
* Remove polling, recursive calling, rechecks for bot.waitForChunksToLoad (@u9g)
* Add crystal placing example (@u9g)
* Fixes physicsEnabled check for knockback (@u9g)
* Default swingArm to left hand (@u9g)
* Add support for teams (@builder-247)
* Add missing bot.transfer documentation (@IceTank)

## 3.9.0
* Fix crash on blocks without registered blockId (@Furry)
* Move when hit by an explsion (@u9g)
* Add getExplosionDamages() function (@Karang)
* doc of get explosion (@link-discord)

## 3.8.0
* Improved index.d.ts (@DrMoraschi)
* Added resource pack support (@kaffinpx)
* Fixed bot.dig error (@MoneyMakingTornado)
* Added timeout to #consume (@SeanmcCord)
* Added example for resource pack (@u9g)
* Improved workflow (@u9g)
* Linted JS in md files (@u9g)
* Added bot oxygen Level management (@kaffinpx)
* Improved links (@satyamedh)
* Improved links (@AwesomestCode)
* Improved typing (@u9g)
* Refactored chat.js (@u9g)
* Expanded placeBlockWith Options to offhand (@aestetic)
* Added anvil test (@u9g)
* Added placeEntity() (@u9g)
* Improved oxygen typings (@DrMoraschi)
* Improved socket snippet (@iceTank)
* Improved placeEntity (@u9g)
* Renamed bot.quit to end (@u9g)
* Updated Spanish readme (@DrMoraschi)
* Added French Translations (@creeper09)
* Corrected java version in gitpod (@rom)
* Improved readme lint (@rom)
* Added container and dropper to allowWindowTypes (@IceTank)


## 3.7.0
* Add bot.removeChatPattern() (@BlueBurgersTDD)
* Add events to typings (@DrMoraschi)
* Add TR translation (@KaffinPX)
* Create plugin example (@Zn10plays)
* Revise readme (@IceTank)
* Revise chat_parsing example comments (@U5B)
* Revise raycast example (@IceTank)
* allow passing nmpclient as an option in createbot (@u9g)
* Add bot.awaitMessage() (@u9g)
* Add modular example (@u9g)
* Fix bug with chat patterns (@u9g)
* Fix bug with game event (@u9g)

## 3.6.0
* add bot.addChatPattern() & bot.addChatPatternSet() & deprecate bot.chatAddPattern() (@U9G)

## 3.5.0
* Add common errors to FAQ (@U9G)
* Move mosts of index.js to lib/loader.js (@U9G)
* Improve packet_info handling (@Karang)
* Add getControlState function (@Camezza)

## 3.4.0
* fix once leak in placeBlock (@Karang)
* allow sleeping during rain/thunderstorms (@qrvd)
* Change transaction apology packet to match vanilla client (@FeldrinH)

## 3.3.3
* fix world switch leak

## 3.3.2
* fix entity names

## 3.3.1
* fix stop digging (@Karang)

## 3.3.0
* trading fix (@validgem)
* fix enchantments (@goncharovchik)
* fix newListener and removeListener stacking on world change (@U5B)
* add a 'messagestr' event (@U9G)
* Add an option forceLook for place block similar to the digging one (@CyberPatrick)
* Can see block add intersect match (@sefirosweb)
* Add ability to use an anvil fully (@U9G)

## 3.2.0
* Fix position in getBlock()

## 3.1.0
* Fix typings of findBlock and findBlocks (@csorfab)
* place block improvements (@Karang)
* add face option to dig (@IceTank)
* trading fixes (@validgem)
* world events exposed by pworld (@u9g)
* fix wait for ticks and expose physicsEnabled (@Karang)

## 3.0.0
* added null or undefined check in inventory (@u9g)
* Removed broken use of "this" in physics.js (@TheDudeFromCI)
* Promisify testCommon (@ArcticZeroo)
* Fixed Bot not skipping end credits (@IceTank)
* BREAKING: Simplify windows API and promisify tests (@Karang) : several methods and events from window API were changed:
  * Removed Chest, EnchantmentTable, Furnace, Dispenser and Villager classes (they all are Windows now)
  * Dispensers are now handled by the same code as other containers, hopper too (they were missing)
  * There is now only 2 events signaling a slot update ("updateSlot" and "updateSlot:slotId" of the Window class) (before there was: "setSlot", "setSlot:windowId", "windowUpdate", "updateSlot", on 3 different eventEmitter (and not all of them were working properly))
  * All windows (present and future) now have a withdraw and deposit function

## 2.41.0
* Fix Time type definition (@hivivo)
* Add face for block in sight result (@Karang)
* Fix skin restorer bug (@TheDudeFromCI)
* Improve enchantment table info (@Karang)
* 1.16.5 support (@rom1504)

## 2.40.1
* Fix for not handling negative numbers in time plugin (@Naomi)
* Fix typescript Bot definition (@rom1504)

## 2.40.0
* fix for dig ignore (@TheDudeFromCI)
* better calculation of digging range (@goncharovchik)
* emit death once (@extremeheat)
* add waitForTicks function (@TheDudeFromCI)
* add null check for sign text (@u9g)

## 2.39.2
* explicit node 14 support

## 2.39.1
* add null check in bot.dig (@rom1504)
* Fix deprecation warning for block in sight (@Karang)

## 2.39.0
* Add number support to bot.chat (@BlueBurgersTDD)
* Fixed && Improved blockFind function with useExtraInfo = true (@magicaltoast)
* Added option to allow the bot to keep it's head in place when mining. (@TheDudeFromCI)

## 2.38.0
* Add bot.game.serverBrand property (@Karang)
* set extraInfos to false in blockIsNotEmpty (@mat-1)
* make the ChatMessage.toAnsi:lang argument optional (@Antonio32A)
* Fixed message types (@TheDudeFromCI)
* by default hideErrors is now true (@rom1504)

## 2.37.1
* Optimize lookAt promise behavior (@ph0t0shop)

## 2.37.0
* Promisify villager & Trader (thanks @ph0t0shop)
* protect against action id going over 32767 (@rom1504)
* fix incorrect handling of username definition (@rom1504)

## 2.36.0
* all async method now both return promises and take a callback (thanks @ph0t0shop for this great improvement)

## 2.35.0
* Extra position packet after TP
* Add blockAtCursor
* Deprecate blockInSight
* TS typing fixes

## 2.34.0
* 1.16.4 support

## 2.33.0
* block_actions fix (thanks @SpikeThatMike)
* typescript fixes (thanks @TheDudeFromCI and @NotSugden)
* add uuid by objectUUID handling (thanks @Rob9315)
* fix bed packet (thanks @imharvol)
* better plugin handling (thanks @TheDudeFromCI)

## 2.32.0
* 1.16.3 support (thanks @GroobleDierne and @TheDudeFromCI)
* fix bug with entity width (thanks @TheDudeFromCI)
* Add ability to call openChest on shulker boxes (thanks @efunneko)

## 2.31.0
* Fix furnace and add tests (thanks @ImHarvol)
* Add offhand param to d.ts (thanks @TheDudeFromCI)
* Add hasAttackCooldown feature (thanks @TheDudeFromCI)
* Add type validation for bot.chat (thanks @BlueBurgersTDD)
* Add chat position to message event (thanks @larspapen)

## 2.30.0
* Add support for Barrel (#1344) (thanks @ImHarvol)
* Fix attack cooldown bug (thanks @TheDudeFromCI)
* Exposed getDestSlot (thanks @TheDudeFromCI)
* Simplify setCommandBlock arguments (thanks @ImHarvol)
* hide unknown transaction warning if hideErrors option is enabled

## 2.29.1
* fix findblock typescript def (thanks @TheDudeFromCI)
* fix setCommandBlock for recent versions (thanks @ImHarvol)

## 2.29.0
* Add hand parameter to activateItem (thanks @Karang)
* remove _chunkColumn from the api (bot.world should now be used)
* Handle MC|AdvCmd misspelling (thanks @ImHarvol)

## 2.28.1
* fix findBlocks (thanks @Karang)

## 2.28.0
* add nearestEntity function (thanks @Karang)

## 2.27.0
* add heldItemChanged

## 2.26.0
* use and expose prismarine-world as bot.world
* add itemDrop event (thanks @ImHarvol)
* fix bot.fish callback (thanks @GroobleDierne)
* parse entity metadata for crouching (thanks @IdanHo)
* fix bot.time.day (thanks @Naomi-alt)
* improve find blocks options (thanks @Karang)

## 2.25.0
* emit chestLidMove (thanks @imharvol)
* add options for main hand selection (thanks @Colten-Covington)
* fix respawning columns issues (thanks @Karang)

## 2.24.0
* Fix getBlockAt when outside bounds
* Improve documentation and examples
* Add ability to change the skin parts of a bot (thanks @Naomi-alt)

## 2.23.0
* 1.16 support
* fix noteheard (thanks @Naomi-alt)

## 2.22.1
* better typedef (thanks @Konstantin)
* fix off by 1 error in findBlocks (thanks @Karang)
* physics.js look fix (thanks @thesourceoferror)
* fix chat message bracketing (thanks @Nurutomo)
* use prismarine-physics

## 2.22.0
* Improve digTime computation (thanks @Karang)
* expose blockEntity.raw (thanks @SiebeDW)
* improve typedef for find block options (thanks @TheDudeFromCI)

## 2.21.0
* don't log errors if hideErrors is true

## 2.20.0
* add extra infos option in find block

## 2.19.2
* fix ground up for 1.13->1.15

## 2.19.1
* fix find block (thanks @Karang)
* improve sign parsing (thanks @cookiedragon234)

## 2.19.0
* much faster findBlock (thanks @Karang)

## 2.18.0
* fix bugs in lookAt and setQuickBarSlot
* add auto_totem example (thanks @AlexProgrammerDE)
* improve blockAt speed

## 2.17.0
* physics engine refactor (thanks @Karang)
* mcdata update for better 1.14 and 1.15 support

## 2.16.0
* use protodef compiler (thanks @Karang)
* off-hand support (thanks @Karang)
* fix type definitions (thanks @dada513)

## 2.15.0
* fix transfer bugs (thanks @Karang)
* add typescript definitions (thanks @IdanHo)

## 2.14.1
* fix openVillager

## 2.14.0
* 1.15 support
* russian translation (thanks @shketov)

## 2.13.0
* 1.14 support : more tests, refactored pwindows, feature flags (thanks @Karang)
* Look at the center of the face when placing block
* improve bot.sleep : don't sleep if mob are present (thanks @ImHarvol)

## 2.12.0
* 1.13 support (thanks @Karang, @hornta, @SiebeDW)
* better fishing support (thanks @hutu13879513663)

## 2.11.0
* Expose columns & blockEntities (thanks @SiebeDW)
* Create discord.js (thanks @SiebeDW)
* change amount of slots based on version (thanks @IdanHo)
* Fix 'respawn' event (thanks @ImHarvol)
* Add callback to creative set block (thanks @wvffle)

## 2.10.0
Lot of fixes from @wvffle in this release :
* more checks when digging
* expose a bot.swingArgm() function
* better toString to chat message
* fix handling of empty signs
* correct handling of entity metadata change
And some others :
* new tps plugin by @SiebeDW
* correct handling of chunk unloading by @IdanHo

## 2.9.6
* fix logErrors option

## 2.9.5
* fix logErrors

## 2.9.4
* enable catching and logging of errors by default

## 2.9.3
* fix typo in variable name actionId

## 2.9.2
* improve pushback (thanks @Vap0r1ze)
* more robust handling of tablist (thanks @wvffle)
* ignore (with a warning) transaction without previous click

## 2.9.1
* improve boss bar
* add checks in scoreboard implementation

## 2.9.0

* add universal chat patterns to support more chat plugins

## 2.8.1

* fix error on scoreboard removal

## 2.8.0

lot of new features from @wvffle :

* support for block entities
* improved block bars support
* add block in sight
* fix scoreboard support
* add eating support
* add tab complete support
* add fishing support
* better sign text support
* repl example

## 2.7.5

* improve basic find block a bit

## 2.7.4

* start the bot alive in all cases
* correct run speed and use it to limit the speed properly (thanks @CheezBarger)
* emit error instead of throwing when loading a chunk (thanks @ArcticZeroo)

## 2.7.3

* use docsify for docs

## 2.7.2

* don't do anything if transaction.action < 0 (fix for some non-vanilla plugins)

## 2.7.1

* include fixes from pchunk, protodef and mcdata

## 2.7.0

* fix cannot jump repeatedly
* fix spaces in chatmessage (thanks @Gjum)
* add bot.getControlStates (thanks @ArcticZeroo)
* Support end dimension (thanks @iRath96)
* Added sneaking option to controll states (thanks @Meldiron)
* add title event (thanks @yario-o)
* Update sound.js to include hardcoded sound effects (thanks @jeresuikkila)
* Support for the new launcher_profiles.json format  (thanks @Amezylst)
* update api about checkTimeoutInterval

## 2.6.1

* fix chatmessage
* add plugins to bot options to be able to disable an internal plugin

## 2.6.0

* improve ChatMessage translation functionality (thanks @plexigras)
* added eslint
* es6
* fix autoversion in online mode

## 2.5.0

* don't swing arm when activating an entity
* new plugin loading api

## 2.4.1

* better 1.12 support

## 2.4.0

* auto version detection (thanks @plexigras)

## 2.3.0

* support version 1.12 (thanks @jonathanperret)
* add example to use minecraft session file for auth (thanks @plexigras)

## 2.2.0

* added book writing plugin (thanks @plexigras)
* Make sure bot.time.day is between 0 and 24000 (thanks @roblabla)
* Pass skyLightSent to Chunk.load (thanks @iRath96)

## 2.1.1

* use protodef aliases to properly define channels

## 2.1.0

* add bot.canSeeBlock (thanks @Nixes)
* handle unknown entities and entities sent with their internal id
* add bloodhound to plugin list
* fix chat hoverEvent for 1.9

## 2.0.0

* added support for minecraft chests (thanks @plexigras)
* cross version support : 1.8, 1.9, 1.10 and 1.11 now supported
* [BREAKING] prismarine classes (Block, Entity, Recipe, ...) are now available only by requiring them, not in mineflayer.X. It was required to make cross version possible. minecraft-data is also to be required directly and not available as mineflayer.blocks. The code depending on this should be updated, hence the major version.

## 1.8.0

* add actionBar event (thanks @ArcticZeroo)
* added support for villager trading (thanks @plexigras)

## 1.7.5

* bump dependencies

## 1.7.4

* update minecraft-data

## 1.7.3

* add callback to activateBlock

## 1.7.2

* update dependencies

## 1.7.1

 * update minecraft-protocol, minecraft-data and protodef

## 1.7.0

 * listen for disconnect in login phase (thanks @deathcap)
 * fix multi_block_change (thanks @Corgano)
 * remove chat filter : fix utf8 in chat
 * add extra tolerance for malformed sign packets (thanks @G07cha)
 * adapt to new minecraft data entities format
 * update minecraft-protocol to 0.17.2


## 1.6.0

 * add functionalities to use scoreboard (thanks @jakibaki)
 * update to minecraft-data 0.16.3
 * 50 -> 20 tps for physics
 * Remove requireindex, for browserify support
 * add bot.setCommandBlock

## 1.5.3

 * fix entity_status

## 1.5.2

 * use prismarine-recipe and prismarine-windows
 * use require-self to be able to do require('mineflayer') in the examples
 * fix viewDistance sending

## 1.5.1

 * add checkTimeoutInterval to createBot

## 1.5.0

 * fix achievements parsing in toString()
 * update to nmp 0.16
 * use prismarine-item
 * add example to run multiple bots
 * uuid is now a dashed string
 * remove digging interruption : this doesn't happen in 1.8 servers (and caused problem in some spigot servers)

## 1.4.0

 * improve placeBlock : now use lookAt before placing and has a callback
 * fix soulsand speed
 * use new multi-version version of (node-)minecraft-data

## 1.3.0

 * swing arm on placing a block, look at center of block when activating a block (thanks gipsy-king)
 * refactor examples (thanks Pietro210)
 * add clickWindow support to ContainerWindow (thanks Gnomesley)
 * fix skylight in the nether
 * update node-mojangson to display unparsed text in case of error

## 1.2.1

 * Prevent crash when an unknown entity is spawned
 * add createBot to api.md

## 1.2.0

 * update minecraft-protocol to 0.14.0 : several fixes (error are now catchable, packets are in-order, packets fixes, etc.)
 * add ContainerWindow to support non-Vanilla plugins and add /invsee example (thanks Pietro210)
 * add a callback to bot.look and bot.lookAt
 * when receiving a remove effect packet : if the corresponding effect doesn't exist yet, emit an event with just the id of the effect (thanks Pietro210)
 * swing arm immediately when digging (thanks gipsy-king)
 * now updates bot.entity.heldItem when bot.heldItem is updated
 * fix cli args in examples
 * add forcedMove event
 * fix equipment api
 * new minecraft data version : better metadata handling

## 1.1.2

 * a small fix in chat.js
 * add a licence file

## 1.1.1

 * bot.transfer is faster
 * fix arm_animation
 * using mojangson parser for chat hoverevent
 * add chat patterns for unidentified chat messages
 * fix player leaving

## 1.1.0

Lot of fixes and improvements in this version in order to support mineflayer 1.8.3, including :

 * minecraft 1.8.3 support
 * update minecraft protocol to 0.13.4
 * move enums data to minecraft-data
 * add automatic testing with a vanilla minecraft server on circle ci
 * add argv arguments to examples
 * refactor inventory.js
 * use new recipe format handling metadata better
 * fix lot of things to support 1.8.3 including :
  * block format change
  * position change : y is now always at the feet of the bot

## 1.0.0

 * updated minecraft protocol to 0.11 (Minecraft 1.6.2 support).
 * small changes in the arguments of some events: `chat`, `whisper` and `message`. See [doc/api.md](https://github.com/andrewrk/mineflayer/blob/master/doc/api.md).

## 0.1.1

 * updated minecraft protocol to 0.10 (Minecraft 1.5.2 support).

## 0.1.0

Huge thanks to [zuazo](https://github.com/zuazo) for debugging and
eliminating the problems with 1.5.1 protocol update and node 0.10 update!

 * update minecraft-protocol to 0.9.0 - includes many fixes
 * blocks: fix buffer length assertion error (thanks zuazo)
 * physics: fix assertion error (thanks zuazo)

## 0.0.35

 * inventory: window clicking waits a bit if you have just dug
   fixes a rejected transaction race condition.

## 0.0.34

 * inventory: equipping makes the quick bar a basic LRU cache.
   This can alleviate some race conditions when trying to equip a
   different tool immediately after digging.

## 0.0.33

 * crafting: fix shapeless recipe support
 * inventory: fix several instances which could cause transaction rejected
 * add missing recipes (thanks rom1504)
 * `recipe.delta` data structure changed.

## 0.0.32

 * digging: fix crash when not holding a tool

## 0.0.31

 * only stationary water has a negative effect on digging
 * digging: if you dig while already digging, instead of crashing,
   mineflayer will cancel the in progress dig and start the new one.
 * digging: in creative mode dig time is 0
 * digging interruption error has a code so you can check for it

## 0.0.30

 * expose the materials enum as `mineflayer.materials`

## 0.0.29

 * digging is faster and has less bugs
 * you can stop digging with `bot.stopDigging()`.
 * `bot.dig(block, [timeout], [callback])` changed to `bot.dig(block, [callback])`.
 * add `bot.digTime(block)`
 * add `block.material`
 * add `block.harvestTools`
 * add `window.emptySlotCount()`
 * block and item enums are cleaned up. Every block and item has an
   unambiguous `name` and `displayName`.

## 0.0.28

 * add missing recipe for wooden planks
 * fix various crafting and inventory bugs
 * unequip works with hand as a destination

## 0.0.27

 * add `mineflayer.Location` which can help you locate chunk boundaries
 * `entity.metadata` is formatted as an object instead of an array for
   easier access
 * `canDigBlock` returns `false` if `block` is `null` instead of crashing.

## 0.0.26

 * fix `bot.heldItem` being wrong sometimes
 * water and lava are not solid

## 0.0.25

 * `bot.equip` - wait at least a tick before calling callback

## 0.0.24

 * fix digging leaves not calling callback.

## 0.0.23

 * add enchantment table support. See `examples/chest.js` for an example.
 * rename `bot.tell` to `bot.whisper` to be consistent with 'whisper' event.
   (thanks Darthfett)

## 0.0.22

 * update vec3 to 0.1.3
 * add "whisper" chat event

## 0.0.21

This release is feature-complete with the old
[C++/Qt based version of mineflayer](https://github.com/andrewrk/mineflayer/blob/cpp-qt-end).

 * add `bot.activateItem()`
 * add `bot.deactivateItem()`
 * add `bot.useOn(targetEntity)`

## 0.0.20

 * add dispenser support
   - add `mineflayer.Dispenser`
   - add `bot.openDispenser(dispenserBlock)`

## 0.0.19

 * add furnace support
   - add `mineflayer.Furnace`
   - add `bot.openFurnace(furnaceBlock)`
 * `mineflayer.Chest`: "update" event renamed to "updateSlot"
 * `bot.equip(itemType, destination, [callback])` changed to
   `bot.equip(item, destination, [callback])`. Use `bot.inventory.items()`
   to get a list of what items you can choose from to equip.
 * fix `bot.openChest` not working for ender chests
 * fix incorrectly scaled fuel percentage
 * upgrade to minecraft-protocol 0.7.0
   - `mineflayer.createBot` no longer takes a `email` argument.
   - The `username` and `password` arguments are used to authenticate with the
     official minecraft servers and determine the case-correct username. If
     you have migrated your user account to a mojang login, `username` looks
     like an email address.
   - If you leave out the `password` argument, `username` is used to connect
     directly to the server. In this case you will get kicked if the server is
     in online mode.

## 0.0.18

 * fix crash for some block updates

## 0.0.17

recalled

## 0.0.16

 * add chest support
   - add `mineflayer.Chest`
   - add `bot.openChest(chestBlock)`
 * `block.meta` renamed to `block.metadata`
 * `item.meta` renamed to `item.metadata`
 * fix crash when player causes entityGone message
 * update to minecraft-protocol 0.6.6

## 0.0.15

 * fix `bot.sleep` not working at all
 * add `bot.isSleeping`
 * add "sleep" event
 * add "wake" event
 * `bot.sleep(bedPoint)` changed to `bot.sleep(bedBlock)`
 * fix `mineflayer.Recipe` not exposed

## 0.0.14

 * add crafting support
   - add `mineflayer.windows`
   - add `mineflayer.Recipe`
   - `bot.inventory` is now an instance of `InventoryWindow`
   - `bot.inventory.count` is no longer a map of id to count.
     `Window` instances have a `count(itemType, [metadata])` method.
   - `bot.inventory.quickBarSlot` moved to `bot.quickBarSlot`.
   - add `'windowOpen' (window)` event
   - add `'windowClose' (window)` event
   - add `bot.craft(recipe, count, craftingTable, [callback])`
   - add `bot.recipesFor(itemType, metadata, minResultCount, craftingTable)`
 * `block.pos` renamed to `block.position`.
 * `'blockUpdate' (point)` event signature changed to
   `'blockUpdate' (oldBlock, newBlock)`
 * `'blockUpdate:(x, y, z)'` event signature changed to
   `'blockUpdate:(x, y, z)' (oldBlock, newBlock)`
 * add `'diggingAborted' (block)` event
 * add `bot.unequip(destination, [callback])`
 * add `bot.toss(itemType, metadata, count, [callback])`
 * `bot.startDigging(block)` changed to `bot.dig(block, [timeout], [callback])`.
 * add `bot.activateBlock(block)`

## 0.0.13

 * fix `bot.equip` when already equipping the item
 * fix some incorrect block physics
 * add `mineflayer.recipes` enum
 * fix crash when digging at a high elevation

## 0.0.12

 * add inventory support
   - add `Item` class which is exposed on `mineflayer`
   - add `bot.inventory` (see docs for more details)
   - add `bot.equip(itemType, destination, [callback])`
   - add `bot.tossStack(item, [callback])`
 * add digging support
   - add `bot.startDigging(block)`
   - add `bot.canDigBlock(block)`
 * blocks: add `blockUpdate:(x, y, z)` event.
 * add building support
   - add `bot.placeBlock(referenceBlock, faceVector)`
 * add `block.painting`
 * add `Painting` class which is exposed on `mineflayer`
 * add experience orb support
   - `entity.type` can be `orb` now
   - `entity.count` is how much experience you get for collecting it

## 0.0.11

 * physics: skip frames instead of glitching out
 * default bot name to Player - `createBot` can take no arguments now.

## 0.0.10

 * physics: fix bug: walking too slowly on Z axis

## 0.0.9

 * ability to sprint (thanks ruan942)
 * fix color code stripping (thanks rom1504)
 * event "onNonSpokenChat" deleted
 * new event "message" which fires for all messages
 * `bot.chat` no longer checks for "/tell" at the beginning
 * add `bot.tell(username, message)` method
 * fix crash when an entity effect occurs

## 0.0.8

 * chat: no longer suppress "chat" events for your own chat (thanks Darthfett).
 * ability to mount / dismount vehicles and attack
 * physics: fix tall grass and dead bushes treated as solid
 * fix "respawn" event firing twice sometimes
 * remove `bot.spawn()` and `autoSpawn` option. auto spawn is now mandatory.
 * fix sending spawn packet twice on init
 * fix bots spawning with their heads on backwards
 * fix bots jumping when they get hit
 * update player heights when they crouch
 * add support for signs: `block.signText` and `bot.updateSign(block, text)`

## 0.0.7

 * add `bot.time.day` and `bot.time.age` and "time" event
 * add `bot.entities` which is a map of the entities around you
 * add `bot.look(yaw, pitch, force)` and `bot.lookAt(point, force)`

## 0.0.6

 * add a physics engine which understands gravity
 * add jumper example, jumps whenever you chat
 * add `respawn` event which fires when you die or change dimensions
 * Block instances have a `boundingBox` property, which is currently either
   `solid` or `empty`.
 * fix `game` event to fire correctly
 * `bot.game.spawnPoint` moved to `bot.spawnPoint`.
 * `bot.game.players` moved to `bot.players`.
 * `bot.quit` has a default reason of "disconnect.quitting" (thanks Darthfett)

## 0.0.5

 * unload chunks when changing dimensions
 * blocks: handle all forms of block changing so that `blockAt` is always
   accurate.

## 0.0.4

 * expose Block, Biome, and Entity

## 0.0.3

 * add `bot.blockAt(point)` which returns a `Block`
 * add `mineflayer.blocks`, `mineflayer.biomes`, and `mineflayer.items`
 * add bot `chunk` event
 * fix `spawn` event and `settings.showCape`
 * added chatterbox example
 * changed `entityDetach` event to have a vehicle argument
 * changed `entityEffectEnd` event to have an effect argument
   instead of `effectId`
 * fix prefixes in pseudos in chat. (thanks rom1504)
 * update vec3 to 0.1.0 which uses euclidean modulus

## 0.0.2

 * add bot.game.spawnPoint
 * add spawn support
 * add rain support
 * add support for getting kicked
 * add settings support
 * add experience support
 * add bed support
 * health status knowledge
 * add entity tracking API
