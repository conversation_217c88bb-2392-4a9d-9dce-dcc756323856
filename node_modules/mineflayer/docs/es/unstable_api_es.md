<!-- START doctoc generated TOC please keep comment here to allow auto update -->
<!-- DON'T EDIT THIS SECTION, INSTEAD RE-RUN doctoc TO UPDATE -->
**Tabla de contenidos**  *generado con [DocToc](https://github.com/thlorenz/doctoc)*

- [API inestable : bot._](#api-instable--bot_)
  - [bot._client](#bot_client)

<!-- END doctoc generated TOC please keep comment here to allow auto update -->

# API inestable : bot._

Estos métodos y clases son útiles en algunos casos especiales pero no son estables y pueden cambiar en cualquier momento

## bot._client

`bot._client` es creado usando [node-minecraft-protocol](https://github.com/PrismarineJS/node-minecraft-protocol).
Maneja escribir y recibir paquetes.
El comportamiento puede cambiar (por ejemplo en cada versión nueva de minecraft), por eso es mejor usar los métodos de mineflayer si es posible.


Esta documentación no está mantenida oficialmente, si quiere ver las últimas novedades, por favor dirijase a la documentación original: [unstable_api](../unstable_api.md)
