## mineflayer-navigate

[navigate](https://github.com/andrewrk/mineflayer-navigate/) - Лёгкое передвижение с помощью поиска путей уровня A*.

<iframe type="text/html" width="640" height="360" src="http://www.youtube.com/embed/O6lQdmRz8eE" frameborder="0"></iframe>

## rbot

[rom1504/rbot](https://github.com/rom1504/rbot) - Умный бот, созданный на основе mineflayer.

<iframe type="text/html" width="640" height="360" src="http://www.youtube.com/embed/0cQxg9uDnzA" frameborder="0"></iframe>

## chaoscraft

[Chaoscraft](https://github.com/schematical/chaoscraft) - Майнкрафт бот, использующий генетические алгоритмы.


​<iframe width="640" height="360" src="https://www.youtube.com/embed/videoseries?list=PLLkpLgU9B5xJ7Qy4kOyBJl5J6zsDIMceH" frameborder="0" allow="autoplay; encrypted-media" allowfullscreen></iframe>