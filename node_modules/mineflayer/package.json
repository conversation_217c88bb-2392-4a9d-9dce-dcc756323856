{"name": "mineflayer", "version": "4.23.0", "description": "create minecraft bots with a stable, high level API", "main": "index.js", "types": "index.d.ts", "scripts": {"mocha_test": "mocha --reporter spec --exit --bail", "test": "npm run mocha_test", "pretest": "npm run lint", "lint": "standard && standard-markdown", "fix": "standard --fix && standard-markdown --fix", "prepublishOnly": "cp docs/README.md README.md"}, "repository": {"type": "git", "url": "git://github.com/PrismarineJS/mineflayer.git"}, "engines": {"node": ">=18"}, "license": "MIT", "dependencies": {"minecraft-data": "^3.76.0", "minecraft-protocol": "^1.50.0", "prismarine-biome": "^1.1.1", "prismarine-block": "^1.17.0", "prismarine-chat": "^1.7.1", "prismarine-chunk": "^1.36.0", "prismarine-entity": "^2.3.0", "prismarine-item": "^1.15.0", "prismarine-nbt": "^2.0.0", "prismarine-physics": "^1.9.0", "prismarine-recipe": "^1.3.0", "prismarine-registry": "^1.10.0", "prismarine-windows": "^2.9.0", "prismarine-world": "^3.6.0", "protodef": "1.17.0", "typed-emitter": "^1.0.0", "vec3": "^0.1.7"}, "devDependencies": {"@types/node": "^22.1.0", "doctoc": "^2.0.1", "minecraft-wrap": "^1.3.0", "mineflayer": "file:", "mocha": "^10.0.0", "protodef-yaml": "^1.5.3", "standard": "^17.0.0", "standard-markdown": "^7.1.0", "typescript": "^5.4.5"}}