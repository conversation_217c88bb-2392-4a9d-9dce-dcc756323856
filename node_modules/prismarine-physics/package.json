{"name": "prismarine-physics", "version": "1.9.0", "description": "Provide the physics engine for minecraft entities", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "mocha --reporter spec --exit", "pretest": "npm run lint", "lint": "standard", "fix": "standard --fix"}, "repository": {"type": "git", "url": "git+https://github.com/PrismarineJS/prismarine-physics.git"}, "keywords": ["physics", "prismarine", "minecraft"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/PrismarineJS/prismarine-physics/issues"}, "homepage": "https://github.com/PrismarineJS/prismarine-physics#readme", "devDependencies": {"expect": "^27.3.1", "mocha": "^10.0.0", "prismarine-block": "^1.7.3", "prismarine-physics": "file:./", "standard": "^17.0.0"}, "dependencies": {"minecraft-data": "^3.0.0", "prismarine-nbt": "^2.0.0", "vec3": "^0.1.7"}}