## History

### 1.9.0
* [add 1.21 to proportionalliquidgravity setting (#117)](https://github.com/PrismarineJS/prismarine-physics/commit/4986c8e395773e770d461db06bce38f9faed0757) (thanks @Madlykeanu)

### 1.8.0
* [Add support for elytra and rockets (#106)](https://github.com/PrismarineJS/prismarine-physics/commit/04351b4c5fa73e9eb7ca79b88c63f5bef59b5645) (thanks @lkwi<PERSON>)
* [Fix "flowing" liquids not being included (#105)](https://github.com/PrismarineJS/prismarine-physics/commit/1f95aaad67684b381aedeb0a990e93dbb03025d0) (thanks @Flonja)
* [Update index.js (#104)](https://github.com/PrismarineJS/prismarine-physics/commit/dd159da044af4959f9fd8d7d97c14921e7288c1e) (thanks @Vakore)
* [Fixed airborne movement factor for sprinting (#75)](https://github.com/PrismarineJS/prismarine-physics/commit/116bb6fe3ae5066d53cedb69683a5fdb497c470d) (thanks @olie304)
* [Add command gh workflow allowing to use release command in comments (#102)](https://github.com/PrismarineJS/prismarine-physics/commit/6797491346be0ef1572aac94c78b2a1377982d54) (thanks @rom1504)
* [Fix 1.17 & 1.18 effect names (#101)](https://github.com/PrismarineJS/prismarine-physics/commit/cc7ae73d6929689645e77c7ba26bf0dcc4bc1e01) (thanks @killbinvlog)

### 1.7.0

* 1.20
* add pitch

### 1.6.0

* Fix left and right (@Kashalls)
* Fix 1.19 Water Physics (@ATXLtheAxolotl)

### 1.5.2

* Fix publish.yml

### 1.5.1

* fix deleteAttributeModifier function (@bendgk)

### 1.5.0

* bump mcdata

### 1.4.0

* add movementSpeed attribute support
* support 1.18

### 1.3.1

* Add pitch speed

### 1.3.0

* support 1.17.0 (thanks @Archengius)

### 1.2.2

* Fix jump floating point value

### 1.2.1

* Fix airdrag value

### 1.2.0

* Add edge security when sneaking

### 1.1.0

* Support climb using jump (1.14+)

### 1.0.8

* Fix NBT enchantment issue on 1.13 clients (thanks @embeddedt)

### 1.0.7

* Use prismarine-nbt to cleanly handle nbt + check for name enchantments post-flattening

### 1.0.6

* fix Depth Strider Enchantment not working

### 1.0.5

* Handle missing nbt data for boots

### 1.0.4

* Fix null block error

### 1.0.3

* Improve 1.13+ physics (thanks @IdanHo)

### 1.0.2

* Fix undefined block when chunk not loaded

### 1.0.1

* Account for bounding boxes below player (thanks @zubearc)

### 1.0.0

* initial implementation
