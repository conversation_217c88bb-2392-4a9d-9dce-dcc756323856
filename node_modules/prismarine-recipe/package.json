{"name": "prismarine-recipe", "version": "1.3.1", "description": "Represent minecraft recipes", "main": "index.js", "typings": "index.d.ts", "scripts": {"test": "npm run lint", "lint": "standard", "fix": "standard --fix"}, "repository": {"type": "git", "url": "git+ssh://**************/PrismarineJS/prismarine-recipe.git"}, "peerDependencies": {"prismarine-registry": "^1.4.0"}, "devDependencies": {"standard": "^17.0.0"}, "keywords": ["minecraft", "recipe"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/PrismarineJS/prismarine-recipe/issues"}, "homepage": "https://github.com/PrismarineJS/prismarine-recipe#readme"}