# prismarine-world

[![NPM version](https://img.shields.io/npm/v/prismarine-world.svg)](http://npmjs.com/package/prismarine-world)
[![Build Status](https://github.com/PrismarineJS/prismarine-world/workflows/CI/badge.svg)](https://github.com/PrismarineJS/prismarine-world/actions?query=workflow%3A%22CI%22)
[![Discord](https://img.shields.io/badge/chat-on%20discord-brightgreen.svg)](https://discord.gg/GsEFRM8)
[![Gitter](https://img.shields.io/badge/chat-on%20gitter-brightgreen.svg)](https://gitter.im/PrismarineJS/general)
[![Irc](https://img.shields.io/badge/chat-on%20irc-brightgreen.svg)](https://irc.gitter.im/)

[![Try it on gitpod](https://img.shields.io/badge/try-on%20gitpod-brightgreen.svg)](https://gitpod.io/#https://github.com/PrismarineJS/prismarine-world)

Provides a minecraft world: an infinite collection of 16x256x16 chunks.

## Usage

See [anvil.js](examples/anvil.js)

## Related packages

* [prismarine-viewer](https://github.com/PrismarineJS/prismarine-viewer) to view a world
* [mineflayer](https://github.com/PrismarineJS/mineflayer) to use a world in a mc client
* [flying-squid](https://github.com/PrismarineJS/flying-squid) to serve a world from a mc server
* [schematic-to-world](https://github.com/rom1504/schematic-to-world) to add a mc schematic to a world

## Available Storage Providers

* [prismarine-provider-anvil](https://github.com/PrismarineJS/prismarine-provider-anvil) Anvil (Minecraft's world format) based storage
* [prismarine-provider-raw](https://github.com/PrismarineJS/prismarine-provider-raw) Prismarine-chunk based raw storage

## API

Read [API.md](docs/API.md)

## History

Read [HISTORY.md](docs/HISTORY.md)
