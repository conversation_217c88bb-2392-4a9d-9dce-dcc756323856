{"name": "prismarine-viewer-standalone", "version": "1.0.0", "description": "prismarine-viewer-standalone", "main": "index.js", "scripts": {"prepare": "webpack", "start": "webpack serve", "prod-start": "http-server public/"}, "dependencies": {"assert": "^2.0.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "copy-webpack-plugin": "^7.0.0", "diamond-square": "^1.2.0", "events": "^3.2.0", "prismarine-viewer": "^1.11.1", "prismarine-world": "file:../../", "process": "^0.11.10", "stream-browserify": "^3.0.0", "webpack": "^5.11.0", "webpack-cli": "^4.2.0", "webpack-dev-server": "^3.11.0"}, "devDependencies": {"http-server": "^0.12.3"}}