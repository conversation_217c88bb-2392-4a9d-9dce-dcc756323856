{"name": "protodef", "version": "1.17.0", "description": "A simple yet powerful way to define binary protocols", "main": "index.js", "types": "index.d.ts", "author": "roblabla <<EMAIL>>", "scripts": {"lint": "standard", "fix": "standard --fix", "unit-test": "mocha --recursive --reporter spec", "test": "npm run lint && npm run unit-test", "benchmark": "mocha benchmark/benchmark_unified.js"}, "tonicExampleFilename": "example.js", "license": "MIT", "dependencies": {"lodash.get": "^4.4.2", "lodash.reduce": "^4.6.0", "protodef-validator": "^1.3.0", "readable-stream": "^4.4.0"}, "engines": {"node": ">=14"}, "bugs": {"url": "https://github.com/ProtoDef-io/node-protodef/issues"}, "homepage": "https://github.com/ProtoDef-io/node-protodef", "repository": {"type": "git", "url": "https://github.com/ProtoDef-io/node-protodef.git"}, "devDependencies": {"benchmark": "^2.1.4", "chai": "^4.1.2", "jsonschema": "^1.2.4", "mocha": "^10.2.0", "protodef": "file:.", "standard": "^17.0.0"}}