{"name": "prompt-sync", "version": "4.2.0", "description": "a synchronous prompt for node.js", "main": "index.js", "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "https://github.com/heapwolf/prompt-sync.git"}, "keywords": ["prompt", "sync", "blocking", "readline", "input", "getline", "repl", "history"], "contributors": [{"name": "<PERSON>", "email": "pao<PERSON>@async.ly"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "devDependencies": {"prompt-sync-history": "^1.0.1"}, "dependencies": {"strip-ansi": "^5.0.0"}}