{"title": "datatype", "description": "default dataTypes", "oneOf": [{"$ref": "switch"}, {"$ref": "option"}, {"$ref": "i8"}, {"$ref": "u8"}, {"$ref": "i16"}, {"$ref": "u16"}, {"$ref": "i32"}, {"$ref": "u32"}, {"$ref": "f32"}, {"$ref": "f64"}, {"$ref": "li8"}, {"$ref": "lu8"}, {"$ref": "li16"}, {"$ref": "lu16"}, {"$ref": "li32"}, {"$ref": "lu32"}, {"$ref": "lf32"}, {"$ref": "lf64"}, {"$ref": "i64"}, {"$ref": "li64"}, {"$ref": "u64"}, {"$ref": "lu64"}, {"$ref": "int"}, {"$ref": "varint"}, {"$ref": "varint64"}, {"$ref": "varint128"}, {"$ref": "zigzag32"}, {"$ref": "zigzag64"}, {"$ref": "lint"}, {"$ref": "array"}, {"$ref": "container"}, {"$ref": "count"}, {"$ref": "bool"}, {"$ref": "cstring"}, {"$ref": "void"}, {"$ref": "pstring"}, {"$ref": "buffer"}, {"$ref": "bitfield"}, {"$ref": "bitflags"}, {"$ref": "mapper"}]}