{"title": "datatype tests", "type": "array", "uniqueItems": true, "definitions": {"datatype_test": {"oneOf": [{"$ref": "#/definitions/test_simple_datatype"}, {"$ref": "#/definitions/test_complex_datatype"}]}, "test_simple_datatype": {"title": "test simple datatype", "type": "object", "properties": {"type": {"type": "string"}, "values": {"$ref": "#/definitions/test_values"}}, "required": ["type", "values"], "additionalProperties": false}, "test_complex_datatype": {"title": "test simple datatype", "type": "object", "properties": {"type": {"type": "string"}, "subtypes": {"type": "array", "items": {"title": "subtype test", "type": "object", "properties": {"description": {"type": "string"}, "vars": {"type": "array"}, "type": {"type": ["object", "array"]}, "values": {"$ref": "#/definitions/test_values"}}, "required": ["description", "type", "values"], "additionalProperties": false}}}, "required": ["type", "subtypes"], "additionalProperties": false}, "test_values": {"type": "array", "items": {"title": "test value", "type": "object", "properties": {"description": {"type": "string"}, "vars": {"type": "array"}, "value": {"type": ["array", "boolean", "integer", "null", "number", "object", "string"]}, "buffer": {"type": "array", "additionalItems": false, "items": {"type": "string", "pattern": "^0x[0-9a-fA-F]{2}$"}}}, "required": ["value", "buffer"], "additionalProperties": false}, "additionalItems": false}}, "items": {"$ref": "#/definitions/datatype_test"}, "additionalItems": false}