[{"type": "switch", "subtypes": [{"description": "container including a switch going to u8, u16 or u32", "type": ["container", [{"name": "action", "type": "u8"}, {"name": "result", "type": ["switch", {"compareTo": "action", "fields": {"0": "u8", "1": "u16", "2": "u32"}}]}]], "values": [{"description": "u8", "value": {"action": 0, "result": 3}, "buffer": ["0x00", "0x03"]}, {"description": "u32", "value": {"action": 2, "result": 4294966272}, "buffer": ["0x02", "0xff", "0xff", "0xfc", "0x00"]}]}, {"description": "container with a variable", "vars": [["colorTransparent", 3]], "type": ["container", [{"name": "color", "type": "i32"}, {"name": "opacity", "type": ["switch", {"compareTo": "color", "fields": {"/colorTransparent": "void"}, "default": "u8"}]}]], "values": [{"description": "active", "value": {"color": 3, "opacity": "undefined"}, "buffer": ["0x00", "0x00", "0x00", "0x03"]}, {"description": "inactive", "value": {"color": 2, "opacity": 4}, "buffer": ["0x00", "0x00", "0x00", "0x02", "0x04"]}]}]}, {"type": "option", "subtypes": [{"description": "optional u16", "type": ["option", "u16"], "values": [{"value": 61303, "buffer": ["0x01", "0xef", "0x77"]}, {"value": null, "buffer": ["0x00"]}]}]}]