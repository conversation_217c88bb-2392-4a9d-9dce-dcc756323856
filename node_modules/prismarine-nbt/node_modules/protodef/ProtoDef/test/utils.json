[{"type": "bool", "values": [{"description": "binary 0 is false", "buffer": ["0x00"], "value": false}, {"description": "1 is true", "buffer": ["0x01"], "value": true}]}, {"type": "varint", "values": [{"description": "8-bit integer", "value": 1, "buffer": ["0x01"]}, {"description": "8-bit maximum integer", "value": 127, "buffer": ["0x7f"]}, {"description": "16-bit integer", "value": 300, "buffer": ["0xac", "0x02"]}, {"description": "24-bit integer", "value": 100000, "buffer": ["0xa0", "0x8d", "0x06"]}, {"description": "32-bit integer", "value": 16909060, "buffer": ["0x84", "0x86", "0x88", "0x08"]}, {"description": "negative integer", "value": -1, "buffer": ["0xff", "0xff", "0xff", "0xff", "0x0f"]}, {"description": "maximum varint (32bit)", "value": 2147483647, "buffer": ["0xff", "0xff", "0xff", "0xff", "0x07"]}, {"description": "minimum varint (32bit)", "value": -2147483648, "buffer": ["0x80", "0x80", "0x80", "0x80", "0x08"]}]}, {"type": "varint64", "values": [{"description": "8-bit integer", "value": 1, "buffer": ["0x01"]}, {"description": "8-bit maximum integer", "value": 127, "buffer": ["0x7f"]}, {"description": "16-bit integer", "value": 300, "buffer": ["0xac", "0x02"]}, {"description": "24-bit integer", "value": 100000, "buffer": ["0xa0", "0x8d", "0x06"]}, {"description": "32-bit integer", "value": 16909060, "buffer": ["0x84", "0x86", "0x88", "0x08"]}]}, {"type": "varint128", "values": [{"description": "8-bit integer", "value": 1, "buffer": ["0x01"]}, {"description": "8-bit maximum integer", "value": 127, "buffer": ["0x7f"]}, {"description": "16-bit integer", "value": 300, "buffer": ["0xac", "0x02"]}, {"description": "24-bit integer", "value": 100000, "buffer": ["0xa0", "0x8d", "0x06"]}, {"description": "32-bit integer", "value": 16909060, "buffer": ["0x84", "0x86", "0x88", "0x08"]}]}, {"type": "zigzag32", "values": [{"description": "8-bit integer", "value": 1, "buffer": ["0x02"]}]}, {"type": "zigzag64", "values": [{"description": "8-bit integer", "value": 1, "buffer": ["0x02"]}]}, {"type": "buffer", "subtypes": [{"description": "a fixed size buffer", "type": ["buffer", {"count": 3}], "values": [{"value": ["0x05", "0x10", "0xAE"], "buffer": ["0x05", "0x10", "0xAE"]}]}, {"description": "an u8 prefixed buffer", "type": ["buffer", {"countType": "u8"}], "values": [{"value": ["0x05", "0x10", "0xAE"], "buffer": ["0x03", "0x05", "0x10", "0xAE"]}]}]}, {"type": "pstring", "subtypes": [{"description": "fixed size string", "type": ["pstring", {"count": 6}], "values": [{"description": "simple hello", "buffer": ["0x48", "0x65", "0x6C", "0x6C", "0x6F", "0x21"], "value": "Hello!"}]}, {"description": "i16 prefixed string", "type": ["pstring", {"countType": "i16"}], "values": [{"description": "simple hello", "buffer": ["0x00", "0x06", "0x48", "0x65", "0x6C", "0x6C", "0x6F", "0x21"], "value": "Hello!"}, {"description": "japanese", "buffer": ["0x00", "0x10", "0xE3", "0x81", "0x93", "0xE3", "0x82", "0x93", "0xE3", "0x81", "0xAB", "0xE3", "0x81", "0xA1", "0xE3", "0x81", "0xAF", "0x21"], "value": "こんにちは!"}]}, {"description": "varint prefixed string", "type": ["pstring", {"countType": "varint"}], "values": [{"description": "simple hello", "buffer": ["0x06", "0x48", "0x65", "0x6C", "0x6C", "0x6F", "0x21"], "value": "Hello!"}, {"description": "japanese", "buffer": ["0x10", "0xE3", "0x81", "0x93", "0xE3", "0x82", "0x93", "0xE3", "0x81", "0xAB", "0xE3", "0x81", "0xA1", "0xE3", "0x81", "0xAF", "0x21"], "value": "こんにちは!"}]}]}, {"type": "cstring", "values": [{"description": "simple hello", "buffer": ["0x48", "0x65", "0x6C", "0x6C", "0x6F", "0x21", "0x00"], "value": "Hello!"}, {"description": "japanese", "buffer": ["0xE3", "0x81", "0x93", "0xE3", "0x82", "0x93", "0xE3", "0x81", "0xAB", "0xE3", "0x81", "0xA1", "0xE3", "0x81", "0xAF", "0x21", "0x00"], "value": "こんにちは!"}]}, {"type": "void", "values": [{"description": "undefined", "buffer": [], "value": null}]}, {"type": "bitfield", "subtypes": [{"description": "an unsigned 8 bit number", "type": ["bitfield", [{"name": "one", "size": 8, "signed": false}]], "values": [{"buffer": ["0xff"], "value": {"one": 255}}]}, {"description": "a signed 8 bit number", "type": ["bitfield", [{"name": "one", "size": 8, "signed": true}]], "values": [{"buffer": ["0xff"], "value": {"one": -1}}]}, {"description": "multiple signed 8 bit numbers", "type": ["bitfield", [{"name": "one", "size": 8, "signed": true}, {"name": "two", "size": 8, "signed": true}, {"name": "three", "size": 8, "signed": true}]], "values": [{"buffer": ["0xff", "0x80", "0x12"], "value": {"one": -1, "two": -128, "three": 18}}]}, {"description": "multiple unsigned 4 bit numbers", "type": ["bitfield", [{"name": "one", "size": 4, "signed": false}, {"name": "two", "size": 4, "signed": false}, {"name": "three", "size": 4, "signed": false}]], "values": [{"buffer": ["0xff", "0x80"], "value": {"one": 15, "two": 15, "three": 8}}]}, {"description": "multiple signed 4 bit numbers", "type": ["bitfield", [{"name": "one", "size": 4, "signed": true}, {"name": "two", "size": 4, "signed": true}, {"name": "three", "size": 4, "signed": true}]], "values": [{"buffer": ["0xff", "0x80"], "value": {"one": -1, "two": -1, "three": -8}}]}, {"description": "an unsigned 12 bit number", "type": ["bitfield", [{"name": "one", "size": 12, "signed": false}]], "values": [{"buffer": ["0xff", "0x80"], "value": {"one": 4088}}]}, {"description": "a complex structure", "type": ["bitfield", [{"name": "x", "size": 26, "signed": true}, {"name": "y", "size": 12, "signed": true}, {"name": "z", "size": 26, "signed": true}]], "values": [{"buffer": ["0x00", "0x00", "0x03", "0x05", "0x30", "0x42", "0xE0", "0x65"], "value": {"x": 12, "y": 332, "z": 4382821}}]}]}, {"type": "bitflags", "subtypes": [{"description": "8bit bitset flag array", "type": ["bitflags", {"type": "u8", "flags": ["onGround"]}], "values": [{"value": {"_value": 1, "onGround": true}, "buffer": ["0x01"]}]}, {"description": "8bit bitset flag object", "type": ["bitflags", {"type": "u8", "flags": {"onGround": 1}}], "values": [{"value": {"_value": 1, "onGround": true}, "buffer": ["0x01"]}]}, {"description": "8bit bitset flag big object", "type": ["bitflags", {"type": "u8", "big": true, "flags": {"onGround": 1}}], "values": [{"value": {"_value": 1, "onGround": true}, "buffer": ["0x01"]}]}]}, {"type": "mapper", "subtypes": [{"description": "a mapper mapping 0 to zero, 1 to one and 2 to two", "type": ["mapper", {"type": "u8", "mappings": {"0": "zero", "1": "one", "2": "two"}}], "values": [{"description": "zero", "value": "zero", "buffer": ["0x00"]}, {"description": "two", "value": "two", "buffer": ["0x02"]}]}]}]