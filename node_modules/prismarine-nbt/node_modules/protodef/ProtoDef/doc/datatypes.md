# Datatypes reference
* [Conditional](./datatypes/conditional.md)
* * [switch](./datatypes/conditional.md#)
* * [option](./datatypes/conditional.md#)
* [Numeric](./datatypes/numeric.md)
* * [i8 / u8 / i16 / u16 / i32 / u32 / i64 / u64 / f32 / f64](./datatypes/numeric.md)
* * [varint](./datatypes/numeric.md)
* [Structures](./datatypes/structures.md)
* * [array](./datatypes/structures.md)
* * [container](./datatypes/structures.md)
* * [count](./datatypes/structures.md)
* [Primitives](./datatypes/primitives.md)
* * [bool](./datatypes/primitives.md)
* * [cstring](./datatypes/primitives.md)
* * [void](./datatypes/primitives.md)
* [Utils](./datatypes/utils.md)
* * [buffer](./datatypes/utils.md)
* * [bitfield](./datatypes/utils.md)
* * [mapper](./datatypes/utils.md)
* * [pstring](./datatypes/utils.md)
