{"container": "native", "varint": "native", "byte": "native", "bool": "native", "switch": "native", "entity_look": ["container", [{"name": "entityId", "type": "varint"}, {"name": "yaw", "type": "i8"}, {"name": "pitch", "type": "i8"}, {"name": "onGround", "type": "bool"}, {"name": "position", "type": ["bitfield", [{"name": "x", "size": 26, "signed": true}, {"name": "y", "size": 12, "signed": true}, {"name": "z", "size": 26, "signed": true}]]}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"22": "entity_look"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"entity_look": "entity_look"}}]}]]}