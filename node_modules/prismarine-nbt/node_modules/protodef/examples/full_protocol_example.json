{"types": {"varint": "native", "pstring": "native", "u16": "native", "u8": "native", "i64": "native", "buffer": "native", "i32": "native", "i8": "native", "bool": "native", "i16": "native", "f32": "native", "f64": "native", "option": "native", "bitfield": "native", "container": "native", "switch": "native", "void": "native", "array": "native", "string": ["pstring", {"countType": "varint"}]}, "status": {"toClient": {"types": {"packet_server_info": ["container", [{"name": "response", "type": "string"}]], "packet_ping": ["container", [{"name": "time", "type": "i64"}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "server_info", "0x01": "ping"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"server_info": "packet_server_info", "ping": "packet_ping"}}]}]]}}, "toServer": {"types": {"packet_ping_start": ["container", []], "packet_ping": ["container", [{"name": "time", "type": "i64"}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x00": "ping_start", "0x01": "ping"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"ping_start": "packet_ping_start", "ping": "packet_ping"}}]}]]}}}, "login": {"toClient": {"types": {"packet_success": ["container", [{"name": "uuid", "type": "string"}, {"name": "username", "type": "string"}]], "packet": ["container", [{"name": "name", "type": ["mapper", {"type": "varint", "mappings": {"0x02": "success"}}]}, {"name": "params", "type": ["switch", {"compareTo": "name", "fields": {"success": "packet_success"}}]}]]}}}}