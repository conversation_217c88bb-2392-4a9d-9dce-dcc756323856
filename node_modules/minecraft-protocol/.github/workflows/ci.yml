name: CI

on:
  push:
    branches:
      - master
  pull_request:
    branches: 
      - master

jobs:
  Lint:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js 18.x
      uses: actions/setup-node@v1.4.4
      with:
        node-version: 18.x
    - run: npm i && npm run lint
  PrepareSupportedVersions:
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}

    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js 18.x
      uses: actions/setup-node@v1.4.4
      with:
        node-version: 18.x
    - id: set-matrix
      run: |
        node -e "
          const supportedVersions = require('./src/version').supportedVersions;
          console.log('matrix='+JSON.stringify({'include': supportedVersions.map(mcVersion => ({mcVersion}))}))
        " >> $GITHUB_OUTPUT
  test:
    needs: PrepareSupportedVersions
    runs-on: ubuntu-latest
    strategy:
      matrix: ${{from<PERSON><PERSON>(needs.PrepareSupportedVersions.outputs.matrix)}}
      fail-fast: false

    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js 18.x
      uses: actions/setup-node@v1
      with:
        node-version: 18.x
    - name: Setup Java JDK
      uses: actions/setup-java@v1.4.3
      with:
        java-version: '21'
        distribution: 'adopt'
    - name: Install dependencies
      run: npm install
    - name: Run tests
      run: npm run mochaTest -- -g ${{ matrix.mcVersion }}v
