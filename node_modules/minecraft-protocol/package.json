{"name": "minecraft-protocol", "version": "1.51.0", "description": "Parse and serialize minecraft packets, plus authentication and encryption.", "main": "src/index.js", "types": "src/index.d.ts", "repository": {"type": "git", "url": "git://github.com/PrismarineJS/node-minecraft-protocol.git"}, "scripts": {"test": "npm run mochaTest", "mochaTest": "mocha --recursive --reporter spec --exit", "lint": "standard", "fix": "standard --fix", "pretest": "npm run lint", "prepublishOnly": "cp docs/README.md README.md"}, "keywords": ["minecraft", "protocol", "parse", "serialize", "packet", "authentication", "encrypton", "bot"], "author": "<PERSON>", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=14"}, "browser": "src/browser.js", "devDependencies": {"@types/node": "^22.7.9", "espower-loader": "^1.0.0", "intelli-espower-loader": "^1.0.0", "minecraft-packets": "^1.1.5", "minecraft-protocol": "file:.", "minecraft-wrap": "^1.2.3", "mocha": "^10.0.0", "power-assert": "^1.0.0", "standard": "^17.0.0", "prismarine-registry": "^1.8.0"}, "dependencies": {"@types/node-rsa": "^1.1.4", "@types/readable-stream": "^4.0.0", "aes-js": "^3.1.2", "buffer-equal": "^1.0.0", "debug": "^4.3.2", "endian-toggle": "^0.0.0", "lodash.get": "^4.1.2", "lodash.merge": "^4.3.0", "minecraft-data": "^3.78.0", "minecraft-folder-path": "^1.2.0", "node-fetch": "^2.6.1", "node-rsa": "^0.4.2", "prismarine-auth": "^2.2.0", "prismarine-chat": "^1.10.0", "prismarine-nbt": "^2.5.0", "prismarine-realms": "^1.2.0", "protodef": "^1.17.0", "readable-stream": "^4.1.0", "uuid-1345": "^1.0.1", "yggdrasil": "^1.4.0"}}