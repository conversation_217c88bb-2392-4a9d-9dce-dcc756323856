{"name": "prismarine-block", "version": "1.19.0", "description": "Represent a minecraft block with its associated data", "main": "index.js", "scripts": {"test": "mocha --reporter spec --exit", "pretest": "npm run lint", "fix": "standard --fix", "lint": "standard"}, "repository": {"type": "git", "url": "https://github.com/PrismarineJS/prismarine-block.git"}, "dependencies": {"minecraft-data": "^3.38.0", "prismarine-biome": "^1.1.0", "prismarine-chat": "^1.5.0", "prismarine-item": "^1.10.1", "prismarine-nbt": "^2.0.0", "prismarine-registry": "^1.1.0"}, "devDependencies": {"expect": "^29.1.0", "minecraft-protocol": "^1.30.0", "minecraft-wrap": "^1.4.0", "mocha": "^10.0.0", "prismarine-block": "file:", "standard": "^17.1.0"}, "keywords": ["prismarine", "minecraft", "block", "js"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/PrismarineJS/prismarine-block/issues"}}