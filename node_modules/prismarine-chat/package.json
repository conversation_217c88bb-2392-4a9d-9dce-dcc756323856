{"name": "prismarine-chat", "version": "1.10.1", "description": "Wrapper for a minecraft chat message", "main": "index.js", "scripts": {"test": "mocha --reporter spec --exit", "pretest": "npm run lint", "lint": "standard", "fix": "standard --fix"}, "repository": {"type": "git", "url": "git+https://github.com/PrismarineJS/prismarine-chat.git"}, "keywords": ["prismarine", "chat"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/PrismarineJS/prismarine-chat/issues"}, "homepage": "https://github.com/PrismarineJS/prismarine-chat#readme", "devDependencies": {"expect": "^29.1.0", "mocha": "^10.0.0", "prismarine-chat": "file:.", "standard": "^17.0.0", "prismarine-item": "^1.10.0"}, "dependencies": {"mojangson": "^2.0.1", "prismarine-nbt": "^2.0.0", "prismarine-registry": "^1.4.0"}}